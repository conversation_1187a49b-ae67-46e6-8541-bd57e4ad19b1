-- ========================================
-- 搜索词分析系统必需数据库表
-- ========================================
-- 执行顺序：按照下面的顺序依次执行SQL语句
-- 注意：请根据你的实际数据库环境调整字符集和引擎

-- 1. 搜索词分析数据表（核心表）
-- 存储从Amazon SP-API获取的搜索词分析数据
CREATE TABLE `ads_search_term_analytics` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `account_id` VARCHAR(50) NOT NULL COMMENT '账户ID',
    `profile_id` BIGINT(20) NOT NULL COMMENT '配置文件ID',
    `marketplace_id` VARCHAR(20) NOT NULL COMMENT '市场ID',
    `report_date` DATE NOT NULL COMMENT '报告日期',
    `search_term` VARCHAR(500) NOT NULL COMMENT '搜索词',
    `search_rank` INT(11) DEFAULT NULL COMMENT '搜索词排名',
    `monthly_search_volume` BIGINT(20) DEFAULT NULL COMMENT '月搜索量',
    `previous_month_search_volume` BIGINT(20) DEFAULT NULL COMMENT '上月搜索量',
    `search_volume_growth_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '月搜索量环比增长率(%)',
    `clicks` INT(11) DEFAULT 0 COMMENT '点击量',
    `impressions` INT(11) DEFAULT 0 COMMENT '展示量',
    `click_through_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '点击率(%)',
    `conversion_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '转化率(%)',
    `average_cost_per_click` DECIMAL(10,2) DEFAULT NULL COMMENT '平均点击成本',
    `search_frequency_rank` TINYINT(4) DEFAULT NULL COMMENT '搜索频率排名(1-高频,2-中频,3-低频)',
    `competition_intensity` TINYINT(4) DEFAULT NULL COMMENT '竞争强度(1-低,2-中,3-高)',
    `seasonality_index` DECIMAL(10,2) DEFAULT NULL COMMENT '季节性指数',
    `relevance_score` DECIMAL(10,2) DEFAULT NULL COMMENT '相关性得分',
    `data_source` VARCHAR(100) DEFAULT NULL COMMENT '数据来源',
    `status` VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态(ACTIVE-有效,INACTIVE-无效)',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新者',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_search_term_date` (`account_id`, `profile_id`, `search_term`(100), `report_date`),
    KEY `idx_account_profile` (`account_id`, `profile_id`),
    KEY `idx_search_term` (`search_term`(100)),
    KEY `idx_report_date` (`report_date`),
    KEY `idx_search_volume` (`monthly_search_volume`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索词分析数据表';

-- 2. 搜索词与商品类目关联表
-- 存储搜索词与商品类目的关联关系，按点击量排序取前三
CREATE TABLE `ads_search_term_category_relation` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `account_id` VARCHAR(50) NOT NULL COMMENT '账户ID',
    `profile_id` BIGINT(20) NOT NULL COMMENT '配置文件ID',
    `marketplace_id` VARCHAR(20) NOT NULL COMMENT '市场ID',
    `search_term` VARCHAR(500) NOT NULL COMMENT '搜索词',
    `category_id` BIGINT(20) NOT NULL COMMENT '类目ID',
    `category_name` VARCHAR(200) DEFAULT NULL COMMENT '类目名称',
    `parent_category_id` BIGINT(20) DEFAULT NULL COMMENT '父类目ID',
    `category_level` TINYINT(4) DEFAULT NULL COMMENT '类目层级',
    `category_path` VARCHAR(1000) DEFAULT NULL COMMENT '类目路径',
    `clicks_in_category` INT(11) DEFAULT 0 COMMENT '该搜索词在此类目下的点击量',
    `impressions_in_category` INT(11) DEFAULT 0 COMMENT '该搜索词在此类目下的展示量',
    `conversions_in_category` INT(11) DEFAULT 0 COMMENT '该搜索词在此类目下的转化量',
    `sales_in_category` DECIMAL(15,2) DEFAULT 0.00 COMMENT '该搜索词在此类目下的销售额',
    `click_rank` TINYINT(4) DEFAULT NULL COMMENT '点击量排名(1-最高)',
    `relevance_score` DECIMAL(10,2) DEFAULT NULL COMMENT '关联强度得分(0-100)',
    `market_share` DECIMAL(10,2) DEFAULT NULL COMMENT '类目在该搜索词下的市场份额(%)',
    `competing_products_count` INT(11) DEFAULT 0 COMMENT '竞争商品数量',
    `average_product_price` DECIMAL(15,2) DEFAULT NULL COMMENT '平均商品价格',
    `is_primary_category` TINYINT(1) DEFAULT 0 COMMENT '是否为主要类目(1-是,0-否)',
    `statistics_date` DATE DEFAULT NULL COMMENT '数据统计日期',
    `data_source` VARCHAR(100) DEFAULT NULL COMMENT '数据来源',
    `status` VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新者',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_search_term_category` (`account_id`, `profile_id`, `search_term`(100), `category_id`),
    KEY `idx_account_profile` (`account_id`, `profile_id`),
    KEY `idx_search_term` (`search_term`(100)),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_click_rank` (`click_rank`),
    KEY `idx_clicks_in_category` (`clicks_in_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索词与商品类目关联表';

-- 3. 搜索词商品点击排名表
-- 存储搜索词关联的商品点击量排名数据，包含商品图片、ASIN、标题等
CREATE TABLE `ads_search_term_product_ranking` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `account_id` VARCHAR(50) NOT NULL COMMENT '账户ID',
    `profile_id` BIGINT(20) NOT NULL COMMENT '配置文件ID',
    `marketplace_id` VARCHAR(20) NOT NULL COMMENT '市场ID',
    `search_term` VARCHAR(500) NOT NULL COMMENT '搜索词',
    `asin` VARCHAR(20) NOT NULL COMMENT '商品ASIN',
    `product_title` VARCHAR(1000) DEFAULT NULL COMMENT '商品标题',
    `main_image_url` VARCHAR(500) DEFAULT NULL COMMENT '商品主图URL',
    `thumbnail_image_url` VARCHAR(500) DEFAULT NULL COMMENT '商品缩略图URL',
    `product_url` VARCHAR(500) DEFAULT NULL COMMENT '商品详情页URL',
    `brand` VARCHAR(200) DEFAULT NULL COMMENT '商品品牌',
    `price` DECIMAL(15,2) DEFAULT NULL COMMENT '商品价格',
    `currency` VARCHAR(10) DEFAULT NULL COMMENT '货币单位',
    `rating` DECIMAL(3,2) DEFAULT NULL COMMENT '商品评分',
    `review_count` INT(11) DEFAULT 0 COMMENT '评论数量',
    `category_id` BIGINT(20) DEFAULT NULL COMMENT '商品类目ID',
    `category_name` VARCHAR(200) DEFAULT NULL COMMENT '商品类目名称',
    `clicks` INT(11) DEFAULT 0 COMMENT '该搜索词下的点击量',
    `impressions` INT(11) DEFAULT 0 COMMENT '该搜索词下的展示量',
    `conversions` INT(11) DEFAULT 0 COMMENT '该搜索词下的转化量',
    `sales` DECIMAL(15,2) DEFAULT 0.00 COMMENT '该搜索词下的销售额',
    `click_rank` TINYINT(4) DEFAULT NULL COMMENT '点击量排名(1-最高)',
    `conversion_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '转化率(%)',
    `click_through_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '点击率(%)',
    `average_cost_per_click` DECIMAL(10,2) DEFAULT NULL COMMENT '平均点击成本',
    `competitiveness_score` DECIMAL(10,2) DEFAULT NULL COMMENT '商品竞争力得分(0-100)',
    `average_position` DECIMAL(10,2) DEFAULT NULL COMMENT '商品在搜索结果中的平均位置',
    `stock_status` VARCHAR(20) DEFAULT NULL COMMENT '库存状态',
    `is_amazons_choice` TINYINT(1) DEFAULT 0 COMMENT '是否为Amazon Choice(1-是,0-否)',
    `is_best_seller` TINYINT(1) DEFAULT 0 COMMENT '是否为Best Seller(1-是,0-否)',
    `is_prime` TINYINT(1) DEFAULT 0 COMMENT '是否为Prime商品(1-是,0-否)',
    `seller_type` VARCHAR(20) DEFAULT NULL COMMENT '卖家类型(FBA,FBM,AMAZON)',
    `statistics_date` DATE DEFAULT NULL COMMENT '数据统计日期',
    `data_source` VARCHAR(100) DEFAULT NULL COMMENT '数据来源',
    `status` VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新者',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_search_term_asin` (`account_id`, `profile_id`, `search_term`(100), `asin`),
    KEY `idx_account_profile` (`account_id`, `profile_id`),
    KEY `idx_search_term` (`search_term`(100)),
    KEY `idx_asin` (`asin`),
    KEY `idx_click_rank` (`click_rank`),
    KEY `idx_clicks` (`clicks`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索词商品点击排名表';

-- ========================================
-- 可选：创建当月分表（如果数据量大）
-- ========================================
-- 如果你预期数据量很大，可以创建按月分表
-- 格式：表名_YYYYMM

-- 创建2025年1月的分表示例
CREATE TABLE `ads_search_term_analytics_202501` LIKE `ads_search_term_analytics`;
CREATE TABLE `ads_search_term_category_relation_202501` LIKE `ads_search_term_category_relation`;
CREATE TABLE `ads_search_term_product_ranking_202501` LIKE `ads_search_term_product_ranking`;

-- ========================================
-- 验证表创建
-- ========================================
-- 执行以下查询验证表是否创建成功
-- SHOW TABLES LIKE 'ads_search_term%';
-- DESCRIBE ads_search_term_analytics;
-- DESCRIBE ads_search_term_category_relation;
-- DESCRIBE ads_search_term_product_ranking;
