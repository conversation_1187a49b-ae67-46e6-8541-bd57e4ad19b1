package com.yiyitech.ads.service.api;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.response.AdsSpCategoryResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpCategoryApi.java
 * @Description
 * @createTime 2025年02月18日 13:58:00
 */
@Slf4j
@Component
public class AdsSpCategoryApi {
    @Value("${client.id}")
    private String clientId;
    @Value("${url.api_url}")
    private String apiUrl;

    /**
     * 获取站点所有品类。
     * @param accessToken
     * @param profileId
     * @return
     */
    public AdsSpCategoryResponse getSpCategory(String accessToken, String profileId) {
        HttpGet request = new HttpGet(apiUrl + "/sp/targets/categories");
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Prefer", "true");
        request.setHeader("Authorization", "Bearer " + accessToken);
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("获取站点所有品类请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsSpCategoryResponse spCategoryResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsSpCategoryResponse.class);
            return spCategoryResponse;
        } catch (IOException e) {
            log.error("获取站点所有品类异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("获取站点所有品类异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

}
