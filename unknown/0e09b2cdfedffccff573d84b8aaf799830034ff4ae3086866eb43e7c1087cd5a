package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsUserAccountModel.java
 * @Description
 * @createTime 2025年06月22日 23:30:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_user_account")
public class AdsUserAccountModel implements Serializable {
    private static final long serialVersionUID = -4183932409082595430L;

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "user_id")
    private Long userId;

    @TableField(value = "account_id")
    private Long accountId;

    @TableField(value = "account_pro_id")
    private Long accountProId;

    /**
     * 绑定类型（1管理，2被授权）
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 状态(0无效，1有效)
     */
    @TableField(value = "status")
    private Boolean status;

    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
