package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.enums.AdsNegLogStatusEnum;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.AdsNegativeOperLogModel;
import com.yiyitech.ads.util.QueryWrapperUtil;
import com.yiyitech.support.mybatis.AbstractBaseMapper;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperLogMapper.java
 * @Description
 * @createTime 2025年07月03日 22:23:00
 */
public interface AdsNegativeOperLogMapper extends AbstractBaseMapper<AdsNegativeOperLogModel> {

    default List<AdsNegativeOperLogModel> searchByProfileLstByStatus(List<AdsAccountProfileModel> profileLst, List<Integer> statusLst) {
        if (CollectionUtils.isEmpty(profileLst)) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsNegativeOperLogModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendAccountProfileCondition(queryWrapper, profileLst);
//        queryWrapper.in("status", Arrays.asList(AdsNegLogStatusEnum.NOTVIEWED.getCode(), AdsNegLogStatusEnum.UNTREATED.getCode()));
        queryWrapper.in("status", statusLst);
        queryWrapper.orderByAsc("create_time");
        return selectList(queryWrapper);
    }

    default List<AdsNegativeOperLogModel> searchByProfileLstByStatusBYAsin(List<AdsAccountProfileModel> profileLst, String asin) {
        if (CollectionUtils.isEmpty(profileLst)) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsNegativeOperLogModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendAccountProfileCondition(queryWrapper, profileLst);
        queryWrapper.eq("status", AdsNegLogStatusEnum.NOTVIEWED.getCode());
        queryWrapper.eq("asin", asin);
        queryWrapper.orderByAsc("create_time");
        return selectList(queryWrapper);
    }

    default List<AdsNegativeOperLogModel> searchByProfileLstLstByDateByStatus(List<AdsAccountProfileModel> profileLst, String startTime, String endTime, Integer status) {
        if (CollectionUtils.isEmpty(profileLst)) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsNegativeOperLogModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendAccountProfileCondition(queryWrapper, profileLst);
        queryWrapper.ge("create_time", startTime).le("create_time", endTime);
        if(null != status){
            queryWrapper.eq("status", status);
        }
        queryWrapper.orderByDesc("update_time");
        return selectList(queryWrapper);
    }

}
