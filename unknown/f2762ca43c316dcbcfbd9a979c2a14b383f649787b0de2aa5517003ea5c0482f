package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsRolePermissionModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsRolePermissionMapper.java
 * @Description
 * @createTime 2025年06月20日 16:13:00
 */
public interface AdsRolePermissionMapper extends AbstractBaseMapper<AdsRolePermissionModel> {

    default List<AdsRolePermissionModel> searchByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsRolePermissionModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", roleIds);
        return selectList(queryWrapper);
    }
}
