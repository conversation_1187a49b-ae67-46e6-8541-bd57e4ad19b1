package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yiyitech.ads.model.AdsSearchTermProductRankingModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 搜索词商品排名Mapper接口
 * 用于搜索词关联商品排名数据的数据库操作
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermProductRankingMapper.java
 * @Description 搜索词商品排名Mapper接口
 * @createTime 2025年01月31日
 */
@Mapper
public interface AdsSearchTermProductRankingMapper extends BaseMapper<AdsSearchTermProductRankingModel> {

    /**
     * 根据搜索词和ASIN查找商品排名数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @param asin 商品ASIN
     * @return 商品排名数据
     */
    AdsSearchTermProductRankingModel findBySearchTermAndAsin(@Param("accountId") String accountId,
                                                            @Param("profileId") Long profileId,
                                                            @Param("searchTerm") String searchTerm,
                                                            @Param("asin") String asin);

    /**
     * 获取搜索词关联的前N个商品（按点击量排序）
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @param limit 返回数量限制
     * @return 商品排名列表
     */
    List<AdsSearchTermProductRankingModel> getTopProductsBySearchTerm(@Param("accountId") String accountId,
                                                                      @Param("profileId") Long profileId,
                                                                      @Param("searchTerm") String searchTerm,
                                                                      @Param("limit") Integer limit);

    /**
     * 获取搜索词的所有关联商品
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 商品列表
     */
    List<AdsSearchTermProductRankingModel> getProductsBySearchTerm(@Param("accountId") String accountId,
                                                                   @Param("profileId") Long profileId,
                                                                   @Param("searchTerm") String searchTerm);

    /**
     * 获取商品关联的搜索词列表
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param asin 商品ASIN
     * @return 搜索词关联列表
     */
    List<AdsSearchTermProductRankingModel> getSearchTermsByProduct(@Param("accountId") String accountId,
                                                                   @Param("profileId") Long profileId,
                                                                   @Param("asin") String asin);

    /**
     * 获取热门商品（按总点击量排序）
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param limit 返回数量限制
     * @return 热门商品列表
     */
    List<Map<String, Object>> getTopProducts(@Param("accountId") String accountId,
                                            @Param("profileId") Long profileId,
                                            @Param("limit") Integer limit);

    /**
     * 获取商品的竞争力分析
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param asin 商品ASIN
     * @return 竞争力分析数据
     */
    Map<String, Object> getProductCompetitivenessAnalysis(@Param("accountId") String accountId,
                                                          @Param("profileId") Long profileId,
                                                          @Param("asin") String asin);

    /**
     * 获取品牌的商品排名分布
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param brand 品牌名称
     * @return 品牌商品排名分布
     */
    List<Map<String, Object>> getBrandProductRankingDistribution(@Param("accountId") String accountId,
                                                                 @Param("profileId") Long profileId,
                                                                 @Param("brand") String brand);

    /**
     * 获取类目下的商品排名
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param categoryId 类目ID
     * @param limit 返回数量限制
     * @return 类目商品排名列表
     */
    List<AdsSearchTermProductRankingModel> getProductRankingsByCategory(@Param("accountId") String accountId,
                                                                        @Param("profileId") Long profileId,
                                                                        @Param("categoryId") Long categoryId,
                                                                        @Param("limit") Integer limit);

    /**
     * 批量插入商品排名数据
     * 
     * @param dataList 数据列表
     * @return 插入数量
     */
    int batchInsert(@Param("dataList") List<AdsSearchTermProductRankingModel> dataList);

    /**
     * 批量更新商品排名数据
     * 
     * @param dataList 数据列表
     * @return 更新数量
     */
    int batchUpdate(@Param("dataList") List<AdsSearchTermProductRankingModel> dataList);

    /**
     * 获取商品的搜索词覆盖度
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param asin 商品ASIN
     * @return 搜索词覆盖度数据
     */
    Map<String, Object> getProductSearchTermCoverage(@Param("accountId") String accountId,
                                                     @Param("profileId") Long profileId,
                                                     @Param("asin") String asin);

    /**
     * 获取价格区间的商品分布
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @return 价格区间分布
     */
    List<Map<String, Object>> getProductPriceDistribution(@Param("accountId") String accountId,
                                                          @Param("profileId") Long profileId);

    /**
     * 获取高转化率商品
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param minConversionRate 最小转化率
     * @param limit 返回数量限制
     * @return 高转化率商品列表
     */
    List<AdsSearchTermProductRankingModel> getHighConversionProducts(@Param("accountId") String accountId,
                                                                     @Param("profileId") Long profileId,
                                                                     @Param("minConversionRate") Double minConversionRate,
                                                                     @Param("limit") Integer limit);

    /**
     * 获取商品的排名趋势
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param asin 商品ASIN
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 排名趋势数据
     */
    List<Map<String, Object>> getProductRankingTrend(@Param("accountId") String accountId,
                                                     @Param("profileId") Long profileId,
                                                     @Param("asin") String asin,
                                                     @Param("startDate") String startDate,
                                                     @Param("endDate") String endDate);

    /**
     * 获取搜索词的商品竞争分析
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 商品竞争分析数据
     */
    Map<String, Object> getSearchTermProductCompetition(@Param("accountId") String accountId,
                                                        @Param("profileId") Long profileId,
                                                        @Param("searchTerm") String searchTerm);

    /**
     * 更新商品排名
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     */
    void updateProductRankings(@Param("accountId") String accountId,
                              @Param("profileId") Long profileId,
                              @Param("searchTerm") String searchTerm);

    /**
     * 获取新品（最近添加的商品）
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param days 天数
     * @param limit 返回数量限制
     * @return 新品列表
     */
    List<AdsSearchTermProductRankingModel> getNewProducts(@Param("accountId") String accountId,
                                                          @Param("profileId") Long profileId,
                                                          @Param("days") Integer days,
                                                          @Param("limit") Integer limit);

    /**
     * 获取商品的评分分布
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @return 评分分布统计
     */
    List<Map<String, Object>> getProductRatingDistribution(@Param("accountId") String accountId,
                                                           @Param("profileId") Long profileId);

    /**
     * 删除过期的商品排名数据
     * 
     * @param beforeDate 删除此日期之前的数据
     * @return 删除数量
     */
    int deleteExpiredData(@Param("beforeDate") String beforeDate);

    /**
     * 获取卖家类型分布
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @return 卖家类型分布
     */
    List<Map<String, Object>> getSellerTypeDistribution(@Param("accountId") String accountId,
                                                        @Param("profileId") Long profileId);
}
