package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.AdsDailyAdvertiserReportModel;
import com.yiyitech.ads.util.QueryWrapperUtil;
import com.yiyitech.support.mybatis.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsDailyAdvertiserReportMapper.java
 * @Description
 * @createTime 2025年07月05日 18:37:00
 */
public interface AdsDailyAdvertiserReportMapper extends AbstractBaseMapper<AdsDailyAdvertiserReportModel> {

    default List<AdsDailyAdvertiserReportModel> searchNeedUpdateByDate(String startDate, String endDate) {
        QueryWrapper<AdsDailyAdvertiserReportModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("advertised_parent_asin");
        queryWrapper.isNull("picture_url");
        queryWrapper.between("date", startDate, endDate);
        return selectList(queryWrapper);
    }

    default List<AdsDailyAdvertiserReportModel> searchByProLstByCampByGroupByAsinByDate(List<AdsAccountProfileModel> profileLst, List<String> campaignIds,
                                                                                        List<String> adGroupIds, String asin, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(profileLst)) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsDailyAdvertiserReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendAccountProfileCondition(queryWrapper, profileLst);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            queryWrapper.in("campaign_id", campaignIds);
        }
        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            queryWrapper.in("ad_group_id", adGroupIds);
        }
        if (StringUtils.isNotBlank(asin)) {
            queryWrapper.eq("advertised_asin", asin);
        }
        queryWrapper.between("date", startDate, endDate).orderByDesc("id");
        return selectList(queryWrapper);
    }

    default AdsDailyAdvertiserReportModel searchByAccByProByCamByGroupByAsin(String accountId, Long profileId, String campaignId, String adGroupId, String asin) {
        QueryWrapper<AdsDailyAdvertiserReportModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_id", accountId);
        queryWrapper.eq("profile_id", profileId);
        queryWrapper.eq("campaign_id", campaignId);
        queryWrapper.eq("ad_group_id", adGroupId);
        queryWrapper.eq("advertised_asin", asin);
        return CollectionUtils.isEmpty(selectList(queryWrapper)) ? null : selectList(queryWrapper).get(0);
    }

    default int deleteByProfileIdAndDate(String profileId, String date) {
        QueryWrapper<AdsDailyAdvertiserReportModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("profile_id", profileId).eq("date", date);
        return delete(queryWrapper);
    }

    default List<AdsDailyAdvertiserReportModel> selectByProLstByDate(List<AdsAccountProfileModel> profileLst, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(profileLst)) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsDailyAdvertiserReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendAccountProfileCondition(queryWrapper, profileLst);
        queryWrapper.between("date", startDate, endDate);
        return selectList(queryWrapper);
    }

}
