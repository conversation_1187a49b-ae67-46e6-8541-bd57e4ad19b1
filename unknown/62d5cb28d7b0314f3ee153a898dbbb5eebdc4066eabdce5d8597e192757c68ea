package com.yiyitech.ads.config;

import com.alibaba.fastjson.JSON;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.mapper.AdsCreateReportMapper;
import com.yiyitech.ads.mapper.AdsReportTasksMapper;
import com.yiyitech.ads.util.RedisUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AccessTokenHourlyConfig.java
 * @Description
 * @createTime 2023年11月30日 14:56:00
 */
//@Configuration
public class AccessTokenHourlyConfig {
//    @Autowired
//    private RedisUtils redisUtils;
//    @Value("${client.id}")
//    private String clientId;
//    @Value("${client.secret}")
//    private String clientSecret;
//    @Value("${url.token_url}")
//    private String tokenUrl;
//    @Value("${token.refresh.wmdd}")
//    private String wmddRefreshToken;
//    @Autowired
//    private AdsCreateReportMapper adsCreateReportMapper;
//    @Autowired
//    private AdsReportTasksMapper adsReportTasksMapper;
////    @Bean
//    public void accessTokenHourly() {
//        ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);
//        Runnable task = new Runnable() {
//            @Override
//            public void run() {
//                HttpPost request = new HttpPost(tokenUrl);
//                String grant_type = "refresh_token";
//                String reqdata = "{"
//                        + "\"grant_type\": \"" + grant_type + "\","
//                        + "\"refresh_token\": \"" + wmddRefreshToken + "\","
//                        + "\"client_id\": \"" + clientId + "\","
//                        + "\"client_secret\": \"" + clientSecret + "\"" + "}";
//                StringEntity entity = new StringEntity(reqdata, "utf-8");
//                entity.setContentType("application/x-www-form-urlencoded");
//                request.setEntity(entity);
//                request.setHeader("Accept", "*/*");
//                request.setHeader("Content-Type", "application/json");
//                try {
//                    CloseableHttpClient httpClient = HttpClients.createMinimal();
//                    CloseableHttpResponse response = null;
//                    response = httpClient.execute(request);
//                    Map maps = (Map) JSON.parse(EntityUtils.toString(response.getEntity(), "UTF-8"));
//                    String accessToken = maps.get("access_token").toString();
////                    redisUtils.set(CommonConstants.ACCESS_TOKEN_REDIS_KEY, accessToken);
//                    System.out.println("定时任务存入token：" + accessToken);
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        };
//        executorService.scheduleAtFixedRate(task, 0, 1, TimeUnit.HOURS);
//    }
//
////    @Bean
////    public void a(){
////        redisUtils.aaa("");
////    }

}
