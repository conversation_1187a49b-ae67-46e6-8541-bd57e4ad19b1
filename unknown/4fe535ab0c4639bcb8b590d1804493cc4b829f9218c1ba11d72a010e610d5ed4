package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsMonitorRulesModel.java
 * @Description
 * @createTime 2025年07月02日 15:58:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_monitor_rules")
public class AdsMonitorRulesModel implements Serializable {
    private static final long serialVersionUID = -1844764164384220101L;

    @TableId(type = IdType.AUTO)
    private Long id;

    // 关联的业务配置id
    @TableField(value = "svc_config_id")
    private Long svcConfigId;

    // 规则名称
    @TableField(value = "rule_name")
    private String ruleName;

    // 处理方式（1.无行为，2.系统自动执行，3.人工操作）
    @TableField(value = "action_type")
    private Integer actionType;

    // 排序序号
    @TableField(value = "sort_order")
    private Integer sortOrder;

    // 状态：0.无效，1.有效
    @TableField(value = "status")
    private Boolean status;

    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
