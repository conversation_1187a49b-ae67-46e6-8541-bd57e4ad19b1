package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import com.yiyitech.ads.mapper.*;
import com.yiyitech.ads.model.*;
import com.yiyitech.ads.service.CommonService;
import com.yiyitech.ads.util.AdsCountryUtils;
import com.yiyitech.ads.vo.AdsCountryAndProVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CommonServiceImpl.java
 * @Description
 * @createTime 2025年06月23日 13:36:00
 */
@Slf4j
@Service
public class CommonServiceImpl implements CommonService {
    @Autowired
    private AdsCountryUtils adsCountryUtils;
    @Autowired
    private AdsAccountInfoMapper adsAccountInfoMapper;
    @Autowired
    private AdsAccountProfileMapper adsAccountProfileMapper;
    @Autowired
    private AdsUserAccountMapper adsUserAccountMapper;
    @Autowired
    private AdsCampaignMapper adsCampaignMapper;
    @Autowired
    private AdsAdGroupMapper adsAdGroupMapper;

//    @Override
//    public Map<String, Object> searchCurrentProfileInfo(Long userId, boolean isManager) {
//        Map<String, Object> map = new HashMap<>();
//        List<String> accountIdLst = new ArrayList<>();
//        if (isManager) {
//            accountIdLst = adsAccountInfoMapper.searchByUserIdByStatus(userId, true).stream()
//                    .map(AdsAccountInfoModel::getAccountId)
//                    .filter(Objects::nonNull)
//                    .collect(Collectors.toList());
//        }
//        //当前非管理员，获取其所有被授权的站点
//        List<Long> accountProIdLst = adsUserAccountMapper.searchByUserIdByStatus(userId, true).stream()
//                .map(AdsUserAccountModel::getAccountProId)
//                .collect(Collectors.toList());
//        //获取当前用户下的站点详情
//        List<AdsAccountProfileModel> profileLst = adsAccountProfileMapper.searchByIdByAccountId(accountIdLst, accountProIdLst, isManager);
//        map.put("profiles", profileLst);
//        return map;
//    }

    //TODO 后续放缓存中，每次先从缓存拉取
    @Override
    public Map<String, Object> searchFilterOrCurrentProfileInfo(Long userId, String accProIds) {
        Map<String, Object> map = new HashMap<>();
        List<Long> paramAccProIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(accProIds)) {
            paramAccProIdList = Arrays.stream(accProIds.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
        //查询当前用户有哪些店铺和站点
        List<AdsUserAccountModel> userAccLst = adsUserAccountMapper.searchByUserIdByStatus(userId, true);
        //查询当前用户的站点信息
        List<AdsAccountProfileModel> profileLst = adsAccountProfileMapper.selectBatchIds(paramAccProIdList.size() <= 0 ? userAccLst.stream().map(AdsUserAccountModel::getAccountProId).collect(Collectors.toList()) : paramAccProIdList);
        map.put("profiles", profileLst);
        return map;
    }

    @Override
    public Map<String, Object> searchDefaultFilterList() {
        //TODO 后续确认区域和国家关系再处理
        Map<String, Object> map = new HashMap<>();
        List<AdsCountryAndProVO> voLst = new ArrayList<>();

        List<AdsCountryAndProVO.Country> countrys = new ArrayList<>();
        AdsCountryAndProVO.Country country1 = new AdsCountryAndProVO.Country();
        country1.setCountryCode("US").setCountryName("美国");
        AdsCountryAndProVO.Country country2 = new AdsCountryAndProVO.Country();
        country2.setCountryCode("MX").setCountryName("墨西哥");
        AdsCountryAndProVO.Country country3 = new AdsCountryAndProVO.Country();
        country3.setCountryCode("CA").setCountryName("加拿大");
        countrys.add(country1);
        countrys.add(country2);
        countrys.add(country3);

        List<AdsCountryAndProVO.Region> regions = new ArrayList<>();
        AdsCountryAndProVO.Region region = new AdsCountryAndProVO.Region();
        region.setRegionName("北美区");
        region.setCountrys(countrys);
        regions.add(region);

        AdsCountryAndProVO Vo = new AdsCountryAndProVO();
        Vo.setRegions(regions);
        voLst.add(Vo);

        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> searchCurrentFilterList(Long userId, String acceptLanguage) {
        Map<String, Object> map = new HashMap<>();
        //获取所有国家名称和编码
        List<AdsCountryAndProVO> voLst = adsCountryUtils.getCountryMapByLang(acceptLanguage).entrySet().stream()
                .map(entry -> new AdsCountryAndProVO().setCountryCode(entry.getKey()).setCountryName(entry.getValue()))
                .collect(Collectors.toList());
        //当前用户拥有的站点
        List<AdsAccountProfileModel> profileLst = (List<AdsAccountProfileModel>) this.searchFilterOrCurrentProfileInfo(userId, null).get("profiles");
        //获取当前用户下的站点id集合
        List<Long> profileIds = profileLst.stream().map(AdsAccountProfileModel::getProfileId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //获取拥有站点下的所有广告活动
        List<AdsCampaignModel> campaignModelLst = adsCampaignMapper.searchByProfileIdLst(profileIds);
        //广告活动id集合
        List<String> campaignIds = campaignModelLst.stream().map(AdsCampaignModel::getCampaignId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //获取活动下的所有广告组
        List<AdsAdGroupModel> adGroupModelLst = adsAdGroupMapper.searchByCampaignIds(campaignIds);
        //组合站点、活动、广告组
        Map<String, List<AdsCountryAndProVO.AccountProfile>> accProMap = listToMap(profileLst, campaignModelLst, adGroupModelLst);
        voLst.forEach(vo -> {
            vo.setAccountProfiles(accProMap.get(vo.getCountryCode()));
        });

        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    public Map<String, List<AdsCountryAndProVO.AccountProfile>> listToMap(List<AdsAccountProfileModel> profileLst,
                                                                          List<AdsCampaignModel> campaignModelLst,
                                                                          List<AdsAdGroupModel> adGroupModelLst) {
        //adGroup按campaignId分组
        Map<String, List<AdsAdGroupModel>> adGroupMap = adGroupModelLst.stream()
                .collect(Collectors.groupingBy(AdsAdGroupModel::getCampaignId));
        //campaign按profileId分组
        Map<Long, List<AdsCampaignModel>> campaignMap = campaignModelLst.stream()
                .collect(Collectors.groupingBy(AdsCampaignModel::getProfileId));

        Map<String, List<AdsCountryAndProVO.AccountProfile>> resultMap = profileLst.stream().collect(Collectors.toMap(
                AdsAccountProfileModel::getCountryCode,
                profile -> {
                    AdsCountryAndProVO.AccountProfile accProfile = new AdsCountryAndProVO.AccountProfile()
                            .setAccProId(profile.getId().toString())
                            .setProfileId(profile.getProfileId().toString())
                            .setProCountryCode(profile.getCountryCode())
                            .setAccountId(profile.getAccountId())
                            .setProName(profile.getName() == null ? "" : profile.getName());
                    //profile对应的campaigns
                    List<AdsCampaignModel> campaigns = campaignMap.getOrDefault(profile.getProfileId(), new ArrayList<>());
                    for (AdsCampaignModel camp : campaigns) {
                        AdsCountryAndProVO.Campaigns campVO = new AdsCountryAndProVO.Campaigns();
                        campVO.setCampaignId(String.valueOf(camp.getCampaignId()));
                        campVO.setCampaignName(camp.getName());
                        //campaign对应的adGroups
                        List<AdsAdGroupModel> adGroups = adGroupMap.getOrDefault(camp.getCampaignId(), new ArrayList<>());
                        for (AdsAdGroupModel adg : adGroups) {
                            AdsCountryAndProVO.AdGroups adgVO = new AdsCountryAndProVO.AdGroups();
                            adgVO.setAdGroupId(String.valueOf(adg.getAdGroupId()));
                            adgVO.setAdGroupName(adg.getName());
                            campVO.getAdGroups().add(adgVO);
                        }
                        accProfile.getCampaigns().add(campVO);
                    }
                    return Lists.newArrayList(accProfile);
                },
                (list1, list2) -> {
                    list1.addAll(list2);
                    return list1;
                }
        ));
        return resultMap;
    }

}
