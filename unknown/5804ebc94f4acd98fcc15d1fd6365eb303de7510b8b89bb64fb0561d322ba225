package com.yiyitech.ads.service.api;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.request.AdsSpCampaignCreateRequest;
import com.yiyitech.ads.model.response.AdsSpCampaignCreateResponse;
import com.yiyitech.ads.model.response.AdsSpCampaignsListResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpCampaignsAPi.java
 * @Description
 * @createTime 2023年12月11日 16:31:00
 */
@Slf4j
@Component
public class AdsSpCampaignsAPi {
    @Value("${client.id}")
    private String clientId;
    @Value("${url.api_url}")
    private String apiUrl;

    /**
     * 查询活动列表
     * @param accessToken
     * @param profileId
     * @return
     */
    public AdsSpCampaignsListResponse campaignsList(String accessToken, String profileId) {
        HttpPost request = new HttpPost(apiUrl + "/sp/campaigns/list");
//        JSONObject reqObj = new JSONObject();
//        reqObj.put("stateFilter", new JSONObject().put("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 50);
//        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Accept", "application/vnd.spCampaign.v3+json");
        request.setHeader("Content-Type", "application/vnd.spCampaign.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("活动列表api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsSpCampaignsListResponse campaignsLstResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsSpCampaignsListResponse.class);
            return campaignsLstResponse;
        } catch (IOException e) {
            log.error("活动列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("活动列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

    /**
     * 创建广告活动
     * @param accessToken
     * @param profileId
     * @param adsSpCampaignCreateRequest
     * @return
     */
    public AdsSpCampaignCreateResponse campaignCreate(String accessToken, String profileId, AdsSpCampaignCreateRequest adsSpCampaignCreateRequest) {
        HttpPost request = new HttpPost(apiUrl + "/sp/campaigns");
//        JSONObject reqObj = new JSONObject();
//        reqObj.put("campaignIdFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd(campaignId)));
//        reqObj.put("stateFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 10);
//        reqObj.put("includeExtendedDataFields", true);
//        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setEntity(new StringEntity(new Gson().toJson(adsSpCampaignCreateRequest), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Prefer", "return=representation");
        request.setHeader("Accept", "application/vnd.spCampaign.v3+json");
        request.setHeader("Content-Type", "application/vnd.spCampaign.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("广告活动创建api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsSpCampaignCreateResponse spCampaignCreateResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsSpCampaignCreateResponse.class);
            return spCampaignCreateResponse;
        } catch (IOException e) {
            log.error("广告活动创建异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("广告活动创建异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

}
