package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.enums.AdsNegLogStatusEnum;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.AdsCreateReportModel;
import com.yiyitech.ads.model.AdsDailyAdvertiserReportModel;
import com.yiyitech.ads.model.AdsNegativeOperLogModel;
import com.yiyitech.ads.util.QueryWrapperUtil;
import com.yiyitech.support.mybatis.AbstractBaseMapper;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCreateReportMapper.java
 * @Description
 * @createTime 2023年12月26日 16:35:00
 */
public interface AdsCreateReportMapper extends AbstractBaseMapper<AdsCreateReportModel> {

    default List<AdsCreateReportModel> searchByStatusByDate(String status, String date) {
        QueryWrapper<AdsCreateReportModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status)
                .eq("job_date", date);
        return selectList(queryWrapper);
    }

    default List<AdsCreateReportModel> searchByProfileLst(List<AdsAccountProfileModel> profileLst) {
        if (CollectionUtils.isEmpty(profileLst)) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsCreateReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendAccountProfileCondition(queryWrapper, profileLst);
        queryWrapper.orderByDesc("created_at");
        queryWrapper.last("LIMIT 3");
        return selectList(queryWrapper);
    }

    default int deleteByProfileIdAndJobDate(String profileId, String date) {
        QueryWrapper<AdsCreateReportModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("profile_id", profileId).eq("job_date", date);
        return delete(queryWrapper);
    }

    default List<AdsCreateReportModel> searchByStatusBetweenDates(String status, String startDate, String endDate) {
        QueryWrapper<AdsCreateReportModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        queryWrapper.between("job_date", startDate, endDate);
        return selectList(queryWrapper);
    }

}
