package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.model.AdsCampaignModel;
import com.yiyitech.support.mybatis.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCampaignMapper.java
 * @Description
 * @createTime 2023年12月06日 17:24:00
 */
public interface AdsCampaignMapper extends AbstractBaseMapper<AdsCampaignModel> {

    default AdsCampaignModel getCampaignByCampaignId(String campaignId) {
        QueryWrapper<AdsCampaignModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("campaign_id", campaignId);
        return selectOne(queryWrapper);
    }

    default List<AdsCampaignModel> searchByProfileId(String profileId) {
        QueryWrapper<AdsCampaignModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("profile_id", profileId);
        return selectList(queryWrapper);
    }

    default List<AdsCampaignModel> searchByProfileIdLst(List<Long> profileIds) {
        QueryWrapper<AdsCampaignModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("profile_id", profileIds);
        return selectList(queryWrapper);
    }

}
