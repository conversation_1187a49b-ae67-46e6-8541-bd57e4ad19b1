package com.yiyitech.ads.service.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.response.AdsNegativeKeywordResponse;
import com.yiyitech.ads.model.response.AdsNegativeTargetingResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeApi.java
 * @Description
 * @createTime 2025年07月07日 11:06:00
 */
@Slf4j
@Component
public class AdsNegativeApi {
    @Value("${client.id}")
    private String clientId;
    @Value("${url.api_url}")
    private String apiUrl;

    /**
     * 否定关键词
     * @param accessToken
     * @param profileId
     * @param campaignId
     * @param adGroupId
     * @param keywordText
     * @param matchType
     * @return
     */
    public AdsNegativeKeywordResponse negativeKeywords(String accessToken, String profileId, String campaignId, String adGroupId, String keywordText, Integer matchType) {
        HttpPost request = new HttpPost(apiUrl + "/sp/negativeKeywords");
        JSONObject reqObj = new JSONObject();
        JSONArray negativeKeywordsArray = new JSONArray();

        JSONObject negativeKeywordObj = new JSONObject();
        negativeKeywordObj.put("campaignId", campaignId);
        //还有"NEGATIVE_BROAD形式，后续根据需求变更"
        if (1 == matchType) {
            negativeKeywordObj.put("matchType", "NEGATIVE_EXACT");
        }
        if (2 == matchType) {
            negativeKeywordObj.put("matchType", "NEGATIVE_PHRASE");
        }
        negativeKeywordObj.put("state", "ENABLED");
        negativeKeywordObj.put("adGroupId", adGroupId);
        negativeKeywordObj.put("keywordText", keywordText);

        negativeKeywordsArray.add(negativeKeywordObj);
        reqObj.put("negativeKeywords", negativeKeywordsArray);

        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Accept", "application/vnd.spNegativeKeyword.v3+json");
        request.setHeader("Content-Type", "application/vnd.spNegativeKeyword.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != 207) {
                log.error("否定关键词api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsNegativeKeywordResponse negativeKeywordResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsNegativeKeywordResponse.class);
            return negativeKeywordResponse;
        } catch (IOException e) {
            log.error("否定关键词异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("否定关键词异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

    /**
     * 否定投放(asin)
     * @param accessToken
     * @param profileId
     * @param campaignId
     * @param adGroupId
     * @param asin
     * @param matchType
     * @return
     */
    public AdsNegativeTargetingResponse negativeTargeting(String accessToken, String profileId, String campaignId, String adGroupId, String asin, Integer matchType) {
        HttpPost request = new HttpPost(apiUrl + "/sp/negativeTargets");
        JSONObject reqObj = new JSONObject();
        JSONArray negativeTargetingClausesArray = new JSONArray();

        JSONObject clauseObj = new JSONObject();
        clauseObj.put("campaignId", campaignId);
        clauseObj.put("adGroupId", adGroupId);
        clauseObj.put("state", "ENABLED");

        JSONArray expressionArray = new JSONArray();
        JSONObject expressionObj = new JSONObject();

        if (3 == matchType) {
            expressionObj.put("type", "ASIN_SAME_AS");
        }
        if (4 == matchType) {
            expressionObj.put("type", "ASIN_BRAND_SAME_AS");
        }
        expressionObj.put("value", asin);
        expressionArray.add(expressionObj);

        clauseObj.put("expression", expressionArray);
        negativeTargetingClausesArray.add(clauseObj);
        reqObj.put("negativeTargetingClauses", negativeTargetingClausesArray);

        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Prefer", "return=representation");
        request.setHeader("Accept", "application/vnd.spNegativeTargetingClause.v3+json");
        request.setHeader("Content-Type", "application/vnd.spNegativeTargetingClause.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != 207) {
                log.error("否定投放(asin) api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsNegativeTargetingResponse negativeTargetingResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsNegativeTargetingResponse.class);
            return negativeTargetingResponse;
        } catch (IOException e) {
            log.error("否定投放(asin)异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("否定投放(asin)异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

}
