package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCreateReportModel.java
 * @Description
 * @createTime 2023年12月22日 14:01:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_report_tasks")
public class AdsReportTasksModel implements Serializable {
    private static final long serialVersionUID = -1378367249470200618L;

    @TableId(type = IdType.AUTO)
    private Long id;
    //报告名称
    @TableField(value = "report_name")
    private String reportName;
    //报告分组依据
    @TableField(value = "group_by")
    private String groupBy;
    //报告类型
    @TableField(value = "report_type_id")
    private String reportTypeId;
    //报告请求字段
    @TableField(value = "columns")
    private String columns;
    //任务状态（0无效、1有效）
    @TableField(value = "status")
    private Boolean status;
    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private java.util.Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
