package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAccountProfileMapper.java
 * @Description
 * @createTime 2023年12月15日 17:38:00
 */
public interface AdsAccountProfileMapper extends AbstractBaseMapper<AdsAccountProfileModel> {

    default List<AdsAccountProfileModel> searchAllAccountProfile() {
        QueryWrapper<AdsAccountProfileModel> queryWrapper = new QueryWrapper<>();
        return selectList(queryWrapper);
    }

    default List<AdsAccountProfileModel> searchByAccountId(List<String> accountIds) {
        QueryWrapper<AdsAccountProfileModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("account_id", accountIds);
        return selectList(queryWrapper);
    }

    default List<AdsAccountProfileModel> searchByAccountIdByCountry(String accountId, String countryCode) {
        QueryWrapper<AdsAccountProfileModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_id", accountId);
        queryWrapper.eq("country_code", countryCode);
        return selectList(queryWrapper);
    }

    default List<AdsAccountProfileModel> searchByIdByAccountId(List<String> accountIds, List<Long> IdLst, boolean isManager) {
        QueryWrapper<AdsAccountProfileModel> queryWrapper = new QueryWrapper<>();
        if(isManager){
            queryWrapper.in("account_id", accountIds);
        }else {
            queryWrapper.in("id", IdLst);
        }
        return selectList(queryWrapper);
    }

}
