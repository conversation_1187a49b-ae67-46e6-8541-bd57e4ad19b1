package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsUserAccountModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsUserAccountMapper.java
 * @Description
 * @createTime 2025年06月23日 15:27:00
 */
public interface AdsUserAccountMapper extends AbstractBaseMapper<AdsUserAccountModel> {

    default List<AdsUserAccountModel> searchByUserIdByStatus(Long userId, boolean status) {
        QueryWrapper<AdsUserAccountModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }
}
