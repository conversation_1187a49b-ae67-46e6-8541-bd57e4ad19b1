package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsUserModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsUserModelMapper.java
 * @Description
 * @createTime 2025年06月13日 14:22:00
 */
public interface AdsUserMapper extends AbstractBaseMapper<AdsUserModel> {

    default List<AdsUserModel> searchByPhone(String phone) {
        QueryWrapper<AdsUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("phone", phone);
        return selectList(queryWrapper);
    }
}
