package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.google.common.collect.Lists;
import com.yiyitech.ads.enums.AdsAccountNameEnum;
import com.yiyitech.ads.mapper.AdsAccountInfoMapper;
import com.yiyitech.ads.mapper.AdsAccountProfileMapper;
import com.yiyitech.ads.mapper.AdsUserMapper;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.AdsUserModel;
import com.yiyitech.ads.service.AdsUserService;
import com.yiyitech.ads.util.AdsCountryUtils;
import com.yiyitech.ads.vo.AdsUserAccountVO;
import com.yiyitech.ads.vo.AdsUserVO;
import com.yiyitech.support.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsUserServiceImpl.java
 * @Description
 * @createTime 2024年01月18日 16:49:00
 */
@Slf4j
@Service
public class AdsUserServiceImpl implements AdsUserService {
    @Autowired
    private AdsCountryUtils adsCountryUtils;
    @Autowired
    private AdsUserMapper adsUserMapper;
    @Autowired
    private AdsAccountInfoMapper adsAccountInfoMapper;
    @Autowired
    private AdsAccountProfileMapper adsAccountProfileMapper;

    @Override
    public Map<String, Object> searchUserAccountList(String empAcc) {
        Map<String, Object> map = new HashMap<>();
        List<AdsUserAccountVO> voLst = adsCountryUtils.getCountryMap().entrySet().stream()
                .map(entry -> new AdsUserAccountVO().setCountryCode(entry.getKey()).setCountryName(entry.getValue()))
                .collect(Collectors.toList());

        // TODO 根据员工权限对应的店铺和站点表获取accountIdLst
        List<String> accountIdLst = new ArrayList<>();
        accountIdLst.add("A3QXCPZWLQ2IWO");
        accountIdLst.add("AF96VBWMIRW5L");
        accountIdLst.add("A17DJHZT1NEVL4");
        accountIdLst.add("A1G4JLDUB3JG9");
        accountIdLst.add("A1HCWIVLDP3LFS");

        Map<String, String> accountMap = adsAccountInfoMapper.searchByAccountIdLstByStatusToMap(accountIdLst);
        List<AdsAccountProfileModel> profileLst = adsAccountProfileMapper.searchByAccountId(accountIdLst);
        Map<String, List<AdsUserAccountVO.AccountProfile>> accProMap = listToMap(profileLst, accountMap);
        voLst.forEach(vo -> {
            vo.setAccountProfiles(accProMap.get(vo.getCountryCode()));
        });
//        System.out.println(voLst.size());
        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    public Map<String, List<AdsUserAccountVO.AccountProfile>> listToMap(List<AdsAccountProfileModel> profileLst, Map<String, String> accountMap) {
        Map<String, List<AdsUserAccountVO.AccountProfile>> resultMap = profileLst.stream().collect(Collectors.toMap(
                AdsAccountProfileModel::getCountryCode,
                profile -> Lists.newArrayList(new AdsUserAccountVO.AccountProfile()
                        .setProfileId(profile.getProfileId().toString())
                        .setProCountryCode(profile.getCountryCode())
                        .setAccountId(profile.getAccountId())
                        .setAccountName(AdsAccountNameEnum.getColumn(accountMap.get(profile.getAccountId())))),
                (object1, object2) -> {
                    object1.addAll(object2);
                    return object1;
                }
        ));
        return resultMap;
    }

    @Override
    public Map<String, Object> getUserInfo(Long userId, boolean isAdmin) {
        Map<String, Object> map = new HashMap<>();
        AdsUserVO Vo = new AdsUserVO();
        AdsUserModel userModel = adsUserMapper.selectById(userId);
        BeanCopyUtil.copyBean(userModel, Vo);
        Vo.setUserId(userId.toString());
        if(isAdmin && !Vo.getAlreadyLogin()){
            userModel.setAlreadyLogin(true);
            adsUserMapper.updateById(userModel);
        }
        map.put("data", Vo);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }
}
