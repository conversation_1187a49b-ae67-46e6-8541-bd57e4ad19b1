package com.yiyitech.ads.service.api;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.response.AdsTokenResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsRefreshTokenApi.java
 * @Description
 * @createTime 2023年12月08日 17:45:00
 */
@Slf4j
@Component
public class AdsTokenApi {
    @Value("${client.id}")
    private String clientId;
    @Value("${client.secret}")
    private String clientSecret;
    @Value("${url.token_url}")
    private String tokenUrl;
    @Value("${url.redirect_uri}")
    private String redirectUrl;

    /**
     * 获取令牌
     * @param code
     * @return
     */
    public AdsTokenResponse accessToken(String code) {
        HttpPost request = new HttpPost(tokenUrl);
        String grant_type = "authorization_code";
        String reqdata = "{"
                + "\"grant_type\": \"" + grant_type + "\","
                + "\"code\": \"" + code + "\","
                + "\"redirect_uri\": \"" + redirectUrl + "\","
                + "\"client_id\": \"" + clientId + "\","
                + "\"client_secret\": \"" + clientSecret + "\"" + "}";
        StringEntity entity = new StringEntity(reqdata, "utf-8");
        entity.setContentType("application/x-www-form-urlencoded");
        request.setEntity(entity);
        request.setHeader("Accept", "*/*");
        request.setHeader("Content-Type", "application/json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = null;
            response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("令牌获取异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsTokenResponse apiResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsTokenResponse.class);
            return apiResponse;
        } catch (IOException e) {
            log.error("令牌获取异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

    /**
     * 刷新令牌
     * @param refreshToken
     * @return
     */
    public AdsTokenResponse refreshToken(String refreshToken) {
        HttpPost request = new HttpPost(tokenUrl);
        String grant_type = "refresh_token";
        String reqdata = "{"
                + "\"grant_type\": \"" + grant_type + "\","
                + "\"refresh_token\": \"" + refreshToken + "\","
                + "\"client_id\": \"" + clientId + "\","
                + "\"client_secret\": \"" + clientSecret + "\"" + "}";
        StringEntity entity = new StringEntity(reqdata, "utf-8");
        entity.setContentType("application/x-www-form-urlencoded");
        request.setEntity(entity);
        request.setHeader("Accept", "*/*");
        request.setHeader("Content-Type", "application/json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = null;
            response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("令牌刷新异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsTokenResponse apiResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsTokenResponse.class);
            return apiResponse;
        } catch (IOException e) {
            log.error("令牌刷新异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }
}
