package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperLogModel.java
 * @Description
 * @createTime 2025年07月03日 22:18:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_negative_oper_log")
public class AdsNegativeOperLogModel implements Serializable {
    private static final long serialVersionUID = 3386842744273254177L;

    /** ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 广告组合id */
    @TableField("portfolio_id")
    private Long portfolioId;

    /** 账户id */
    @TableField("account_id")
    private String accountId;

    /** 文件id（profileId） */
    @TableField("profile_id")
    private Long profileId;

    /** 活动id */
    @TableField(value = "campaign_id")
    private String campaignId;

    /** 活动名称 */
    @TableField(value = "campaign_name")
    private String campaignName;

    /** 广告组id */
    @TableField(value = "ad_group_id")
    private String adGroupId;

    /** 广告组名称 */
    @TableField(value = "ad_group_name")
    private String adGroupName;

    /** 否定值的唯一标识 */
    @TableField("negative_value_sign")
    private String negativeValueSign;

    /** 否定的值 */
    @TableField("negative_value")
    private String negativeValue;

    /** 否定类型（1精准，2词组，3asin，4精准+词组） */
    @TableField("negative_type")
    private Integer negativeType;

    /** 来源（1搜索词报告，2关键词报告） */
    @TableField("source")
    private Integer source;

    /** 商品的父asin */
    @TableField("parent_asin")
    private String parentAsin;

    /** 商品asin */
    @TableField("asin")
    private String asin;

    /** 商品sku */
    @TableField("sku")
    private String sku;

    /** 商品图片url */
    @TableField("picture_url")
    private String pictureUrl;

    /** 商品asin的名称 */
    @TableField("asin_Item_name")
    private String asinItemName;

    /** 修改级别（1广告组合，2广告活动，3否词运营） */
    @TableField("oper_level")
    private Integer operLevel;

    /** 修改类型（1关键词否定，2商品ASIN否定，3自动匹配否定，4否词运营监控） */
    @TableField("oper_type")
    private Integer operType;

    /** 修改前内容 */
    @TableField("before_oper")
    private String beforeOper;

    /** 修改后内容 */
    @TableField("after_oper")
    private String afterOper;

    /** 判定类型（1人工主动，2系统主动，3系统判定+人工决策） */
    @TableField("judge_type")
    private Integer judgeType;

    /** 日志状态（1已完成，2待查看，3待处理） */
    @TableField("status")
    private Integer status;

    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
