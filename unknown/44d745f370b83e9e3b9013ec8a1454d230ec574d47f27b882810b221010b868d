package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 搜索词商品点击排名模型
 * 用于存储搜索词关联的商品点击量排名数据
 * 包含商品的ASIN、图片、标题等信息，按点击量排序
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermProductRankingModel.java
 * @Description 搜索词关联商品排名模型，存储点击量前三的商品信息
 * @createTime 2025年01月31日
 */
@Data
@Accessors(chain = true)
@TableName("ads_search_term_product_ranking")
public class AdsSearchTermProductRankingModel implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    // 账户id（REFERENCES ads_account_info）
    @TableField(value = "account_id")
    private String accountId;

    // 账户文件id（REFERENCES ads_account_profile）
    @TableField(value = "profile_id")
    private Long profileId;

    // 市场ID
    @TableField(value = "marketplace_id")
    private String marketplaceId;

    // 搜索词
    @TableField(value = "search_term")
    private String searchTerm;

    // 商品ASIN
    @TableField(value = "asin")
    private String asin;

    // 商品标题
    @TableField(value = "product_title")
    private String productTitle;

    // 商品主图URL
    @TableField(value = "main_image_url")
    private String mainImageUrl;

    // 商品缩略图URL
    @TableField(value = "thumbnail_image_url")
    private String thumbnailImageUrl;

    // 商品详情页URL
    @TableField(value = "product_url")
    private String productUrl;

    // 商品品牌
    @TableField(value = "brand")
    private String brand;

    // 商品价格
    @TableField(value = "price")
    private Double price;

    // 商品货币单位
    @TableField(value = "currency")
    private String currency;

    // 商品评分
    @TableField(value = "rating")
    private Double rating;

    // 评论数量
    @TableField(value = "review_count")
    private Integer reviewCount;

    // 商品类目ID
    @TableField(value = "category_id")
    private Long categoryId;

    // 商品类目名称
    @TableField(value = "category_name")
    private String categoryName;

    // 该搜索词下的点击量
    @TableField(value = "clicks")
    private Integer clicks;

    // 该搜索词下的展示量
    @TableField(value = "impressions")
    private Integer impressions;

    // 该搜索词下的转化量
    @TableField(value = "conversions")
    private Integer conversions;

    // 该搜索词下的销售额
    @TableField(value = "sales")
    private Double sales;

    // 点击量排名（1-最高点击量，2-第二高，3-第三高）
    @TableField(value = "click_rank")
    private Integer clickRank;

    // 转化率
    @TableField(value = "conversion_rate")
    private Double conversionRate;

    // 点击率（CTR）
    @TableField(value = "click_through_rate")
    private Double clickThroughRate;

    // 平均点击成本
    @TableField(value = "average_cost_per_click")
    private Double averageCostPerClick;

    // 商品竞争力得分（基于点击量、转化率、评分等综合计算）
    @TableField(value = "competitiveness_score")
    private Double competitivenessScore;

    // 商品在搜索结果中的平均位置
    @TableField(value = "average_position")
    private Double averagePosition;

    // 商品库存状态（IN_STOCK-有库存，OUT_OF_STOCK-缺货，LIMITED_STOCK-库存有限）
    @TableField(value = "stock_status")
    private String stockStatus;

    // 是否为Amazon's Choice
    @TableField(value = "is_amazons_choice")
    private Boolean isAmazonsChoice;

    // 是否为Best Seller
    @TableField(value = "is_best_seller")
    private Boolean isBestSeller;

    // 是否为Prime商品
    @TableField(value = "is_prime")
    private Boolean isPrime;

    // 卖家类型（FBA, FBM, AMAZON）
    @TableField(value = "seller_type")
    private String sellerType;

    // 数据统计日期
    @TableField(value = "statistics_date")
    private Date statisticsDate;

    // 数据来源（SP_API_SEARCH_CATALOG_PERFORMANCE等）
    @TableField(value = "data_source")
    private String dataSource;

    // 状态（ACTIVE-有效，INACTIVE-无效）
    @TableField(value = "status")
    private String status;

    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 计算转化率
     * @return 转化率百分比
     */
    public Double calculateConversionRate() {
        if (clicks == null || clicks == 0) {
            return 0.0;
        }
        if (conversions == null) {
            return 0.0;
        }
        return ((double) conversions / clicks) * 100;
    }

    /**
     * 计算点击率
     * @return 点击率百分比
     */
    public Double calculateClickThroughRate() {
        if (impressions == null || impressions == 0) {
            return 0.0;
        }
        if (clicks == null) {
            return 0.0;
        }
        return ((double) clicks / impressions) * 100;
    }

    /**
     * 判断是否为前三名商品
     * @return true-前三名，false-非前三名
     */
    public boolean isTopThreeProduct() {
        return clickRank != null && clickRank <= 3;
    }

    /**
     * 计算商品竞争力得分
     * 基于点击量、转化率、评分、评论数等因素综合计算
     * @return 竞争力得分（0-100）
     */
    public Double calculateCompetitivenessScore() {
        if (clicks == null) {
            return 0.0;
        }
        
        // 点击量得分（权重40%）
        double clickScore = Math.min(clicks / 1000.0 * 40, 40);
        
        // 转化率得分（权重25%）
        double conversionScore = calculateConversionRate() * 0.25;
        
        // 评分得分（权重20%）
        double ratingScore = rating != null ? (rating / 5.0) * 20 : 0;
        
        // 评论数得分（权重15%）
        double reviewScore = reviewCount != null ? Math.min(reviewCount / 1000.0 * 15, 15) : 0;
        
        return Math.min(clickScore + conversionScore + ratingScore + reviewScore, 100.0);
    }

    /**
     * 获取商品显示名称（品牌 + 标题，截取前50字符）
     * @return 显示名称
     */
    public String getDisplayName() {
        StringBuilder displayName = new StringBuilder();
        if (brand != null && !brand.trim().isEmpty()) {
            displayName.append(brand).append(" ");
        }
        if (productTitle != null) {
            String title = productTitle.length() > 50 ? productTitle.substring(0, 50) + "..." : productTitle;
            displayName.append(title);
        }
        return displayName.toString();
    }
}
