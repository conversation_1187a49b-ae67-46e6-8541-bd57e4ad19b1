package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCreateReportModel.java
 * @Description
 * @createTime 2023年12月26日 16:09:00
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("ads_create_report")
public class AdsCreateReportModel implements Serializable {
    private static final long serialVersionUID = 8541275493739404078L;

    @TableId(type = IdType.AUTO)
    private Long id;
    //生成报告的job日期
    @TableField(value = "job_date")
    private String jobDate;
    //账户id（REFERENCES ads_account_info）
    @TableField(value = "account_id")
    private String accoutnId;
    //账户文件id（REFERENCES ads_account_profile）
    @TableField(value = "profile_id")
    private String profileId;
    //报告id编号
    @TableField(value = "report_id")
    private String reportId;
    //报告类型
    @TableField(value = "report_type_id")
    private String reportTypeId;
    //报告名称
    @TableField(value = "name")
    private String name;
    //报告创建时间
    @TableField(value = "created_at")
    private Timestamp createdAt;
    //报告开始日期
    @TableField(value = "start_date")
    private String startDate;
    //报告结束日期
    @TableField(value = "end_date")
    private String endDate;
    //报告状态
    @TableField(value = "status")
    private String status;
    //报告失败原因
    @TableField(value = "failure_reason")
    private String failureReason;
    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private java.util.Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    public AdsCreateReportModel(String jobDate, String accoutnId, String profileId, String reportId, String reportTypeId,
                                String name, String createdAt, String startDate, String endDate,  String status, String failureReason) {
        this.jobDate = jobDate;
        this.accoutnId = accoutnId;
        this.profileId = profileId;
        this.reportId = reportId;
        this.reportTypeId = reportTypeId;
        this.name = name;
        this.createdAt = Timestamp.from(Instant.parse(createdAt));
        this.startDate = startDate;
        this.endDate = endDate;
        this.status = status;
        this.failureReason = failureReason;
    }
}
