package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsMonitorRulesModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsMonitorRulesMapper.java
 * @Description
 * @createTime 2025年07月02日 17:01:00
 */
public interface AdsMonitorRulesMapper extends AbstractBaseMapper<AdsMonitorRulesModel> {

    default List<AdsMonitorRulesModel> searchBySvcConfigId(Long svcConfigId) {
        QueryWrapper<AdsMonitorRulesModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("svc_config_id", svcConfigId);
        return selectList(queryWrapper);
    }

    default List<AdsMonitorRulesModel> searchByConfigIdsByStatus(List<Long> configurationIds, Boolean status) {
        QueryWrapper<AdsMonitorRulesModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("svc_config_id", configurationIds);
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }
}
