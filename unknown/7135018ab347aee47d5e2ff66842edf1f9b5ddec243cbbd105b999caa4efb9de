package com.yiyitech.ads.service.api;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.request.AdsSpKeywordsCreateRequest;
import com.yiyitech.ads.model.request.AdsSpKeywordsRecRequest;
import com.yiyitech.ads.model.response.AdsSpKeywordsCreateResponse;
import com.yiyitech.ads.model.response.AdsSpKeywordsListResponse;
import com.yiyitech.ads.model.response.AdsSpKeywordsRecResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpKeywordsAPi.java
 * @Description
 * @createTime 2023年12月12日 13:26:00
 */
@Slf4j
@Component
public class AdsSpKeywordsAPi {
    @Value("${client.id}")
    private String clientId;
    @Value("${url.api_url}")
    private String apiUrl;

    /**
     * 查询关键词列表
     * @param accessToken
     * @param profileId
     * @param campaignIds
     * @param groupIds
     * @return
     */
    public AdsSpKeywordsListResponse keywordsList(String accessToken, String profileId, List<String> campaignIds, List<String> groupIds) {
        HttpPost request = new HttpPost(apiUrl + "/sp/keywords/list");
//        JSONObject reqObj = new JSONObject();
//        reqObj.put("campaignIdFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd(campaignId)));
//        reqObj.put("stateFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 10);
//        reqObj.put("includeExtendedDataFields", true);
//        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        request.setHeader("Accept", "application/vnd.spKeyword.v3+json");
        request.setHeader("Content-Type", "application/vnd.spKeyword.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("关键词列表api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsSpKeywordsListResponse keywordsLstResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsSpKeywordsListResponse.class);
            return keywordsLstResponse;
        } catch (IOException e) {
            log.error("关键词列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("关键词列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

    /**
     * 获取关键字建议
     * @param accessToken
     * @param profileId
     * @param adsSpKeywordsRecRequest
     * @return
     */
    public AdsSpKeywordsRecResponse keywordsRec(String accessToken, String profileId, AdsSpKeywordsRecRequest adsSpKeywordsRecRequest) {
        HttpPost request = new HttpPost(apiUrl + "/sp/targets/keywords/recommendations");
//        JSONObject reqObj = new JSONObject();
//        reqObj.put("campaignIdFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd(campaignId)));
//        reqObj.put("stateFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 10);
//        reqObj.put("includeExtendedDataFields", true);
        request.setEntity(new StringEntity(new Gson().toJson(adsSpKeywordsRecRequest), StandardCharsets.UTF_8));
        request.setHeader("Authorization", "Bearer " + accessToken);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Content-Type", "application/vnd.spkeywordsrecommendation.v5+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("关键词建议api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsSpKeywordsRecResponse keywordsRecResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsSpKeywordsRecResponse.class);
            return keywordsRecResponse;
        } catch (IOException e) {
            log.error("关键词建议异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("关键词建议异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }


    /**
     * 创建关键字
     * @param accessToken
     * @param profileId
     * @param adsSpKeywordsCreateRequest
     */
    public AdsSpKeywordsCreateResponse createKeywords(String accessToken, String profileId, AdsSpKeywordsCreateRequest adsSpKeywordsCreateRequest) {
        HttpPost request = new HttpPost(apiUrl + "/sp/keywords");
//        JSONObject reqObj = new JSONObject();
//        reqObj.put("campaignIdFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd(campaignId)));
//        reqObj.put("stateFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 10);
//        reqObj.put("includeExtendedDataFields", true);
        request.setEntity(new StringEntity(new Gson().toJson(adsSpKeywordsCreateRequest), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Prefer", "return=representation");
        request.setHeader("Accept", "application/vnd.spKeyword.v3+json");
        request.setHeader("Content-Type", "application/vnd.spKeyword.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != 207) {
                log.error("关键词创建api请求异常：" + response.getStatusLine().getReasonPhrase());
                int statusCode = response.getStatusLine().getStatusCode();
                String reasonPhrase = response.getStatusLine().getReasonPhrase();
                HttpEntity entity = response.getEntity();
                String errorMessage = entity != null ? EntityUtils.toString(entity, "UTF-8") : "No response body";
                log.error("Error: HTTP " + statusCode + " - " + reasonPhrase);
                log.error("Details: " + errorMessage);
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsSpKeywordsCreateResponse keywordsCreateResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsSpKeywordsCreateResponse.class);
            return keywordsCreateResponse;
        } catch (IOException e) {
            log.error("关键词创建异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("关键词创建异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }


}
