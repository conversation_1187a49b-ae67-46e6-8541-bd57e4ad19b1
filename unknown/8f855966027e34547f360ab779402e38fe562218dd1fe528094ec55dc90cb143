package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsExchangeRateModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsExchangeRateMapper.java
 * @Description
 * @createTime 2024年01月24日 17:58:00
 */
public interface AdsExchangeRateMapper extends AbstractBaseMapper<AdsExchangeRateModel> {

    default List<AdsExchangeRateModel> searchExchangeRateModelLst(String original, String target, String startDate, String endDate) {
        QueryWrapper<AdsExchangeRateModel> queryWrapper = new QueryWrapper<>();
        if (ObjectUtils.isNotEmpty(original)) {
            queryWrapper.eq("original_currency_code", original);
        }
        if (ObjectUtils.isNotEmpty(target)) {
            queryWrapper.eq("target_currency_code", target);
        }
        queryWrapper.between("effect_date", startDate, endDate);
        return selectList(queryWrapper);
    }

    default List<AdsExchangeRateModel> searchByStatusByDate(String startDate, String endDate) {
        QueryWrapper<AdsExchangeRateModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", true).between("effect_date", startDate, endDate);
        return selectList(queryWrapper);
    }
}
