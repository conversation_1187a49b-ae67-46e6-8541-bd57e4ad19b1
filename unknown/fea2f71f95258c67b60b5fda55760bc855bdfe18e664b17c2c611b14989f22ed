package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsReportTasksModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsReportTaskMapper.java
 * @Description
 * @createTime 2023年12月26日 15:15:00
 */
public interface AdsReportTasksMapper extends AbstractBaseMapper<AdsReportTasksModel> {

    default List<AdsReportTasksModel> searchAllAccountInfo(boolean status) {
        QueryWrapper<AdsReportTasksModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }
}
