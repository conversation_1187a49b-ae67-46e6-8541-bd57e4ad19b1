package com.yiyitech.ads.mapper;

import cn.hutool.log.Log;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.yiyitech.ads.model.AdsAccountInfoModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.session.SqlSession;
import org.apache.poi.ss.formula.functions.T;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAccountInfoMapper.java
 * @Description
 * @createTime 2023年12月15日 17:37:00
 */
public interface AdsAccountInfoMapper extends AbstractBaseMapper<AdsAccountInfoModel> {

    default List<AdsAccountInfoModel> searchAllAccountInfo(boolean status) {
        QueryWrapper<AdsAccountInfoModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }

    default int insertOnDuplicateKeyUpdate(AdsAccountInfoModel entity) {
        UpdateWrapper<AdsAccountInfoModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("account_id", entity.getAccountId());
        return update(entity, updateWrapper);
    }


//    @Insert({
//            "INSERT INTO ads_account_info (account_id, type, name, valid_payment, refresh_token, status)",
//            "VALUES (#{accoutnId}, #{type}, #{name}, #{validPayment}, #{refreshToken}, #{status})",
//            "ON DUPLICATE KEY UPDATE",
//            "account_id = #{accoutnId}"
//    })
//    int insertOnDuplicateKeyUpdate(AdsAccountInfoModel entity);

    default Map<String, String> searchByAccountIdLstByStatusToMap(List<String> accountId) {
        QueryWrapper<AdsAccountInfoModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("account_id", accountId);
        return selectList(queryWrapper).stream().collect(Collectors.toMap(AdsAccountInfoModel::getAccountId, AdsAccountInfoModel::getName));
    }

    default List<AdsAccountInfoModel> searchByAccountId(String accountId) {
        QueryWrapper<AdsAccountInfoModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("account_id", accountId);
        return selectList(queryWrapper);
    }

    default List<AdsAccountInfoModel> searchByUserIdByStatus(Long userId, boolean status) {
        QueryWrapper<AdsAccountInfoModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }

    default List<AdsAccountInfoModel> searchByAccountIdsByStatus(List<String> accountIds, boolean status) {
        QueryWrapper<AdsAccountInfoModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("account_id", accountIds);
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }

    default List<AdsAccountInfoModel> searchByAccountIdsByStatusByIsAuth(List<String> accountIds, Boolean spAuth, Boolean adsAuth, boolean status) {
        QueryWrapper<AdsAccountInfoModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("account_id", accountIds);
        queryWrapper.eq("status", status);
        if (null != spAuth) {
            if (spAuth) {
                queryWrapper.isNotNull("refresh_token_sp");
            } else {
                queryWrapper.isNull("refresh_token_sp");
            }
        }
        if (null != adsAuth) {
            if (adsAuth) {
                queryWrapper.isNotNull("refresh_token");
            } else {
                queryWrapper.isNull("refresh_token");
            }
        }
        return selectList(queryWrapper);
    }
}
