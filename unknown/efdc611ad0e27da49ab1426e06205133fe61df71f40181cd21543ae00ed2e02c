package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yiyitech.ads.enums.AdsCommonStatusEnum;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.AdsDailySearchTermReportModel;
import com.yiyitech.ads.util.QueryWrapperUtil;
import com.yiyitech.support.mybatis.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsDailySearchTermReportMapper.java
 * @Description
 * @createTime 2023年12月19日 17:28:00
 */
public interface AdsDailySearchTermReportMapper extends AbstractBaseMapper<AdsDailySearchTermReportModel> {

    default List<AdsDailySearchTermReportModel> searchDailySearchTermReportModel(List<String> accountId, List<String> profileId, String startDate, String endDate, String campaignName, String adGroupName, String searchTerm, String keyword) {
        QueryWrapper<AdsDailySearchTermReportModel> queryWrapper = new QueryWrapper<>();
        if (ObjectUtils.isNotEmpty(accountId)) {
            queryWrapper.in("account_id", accountId);
        }
        if (ObjectUtils.isNotEmpty(profileId)) {
            queryWrapper.in("profile_id", profileId);
        }
        if (ObjectUtils.isNotEmpty(campaignName)) {
            queryWrapper.like("campaign_name", campaignName);
        }
        if (ObjectUtils.isNotEmpty(adGroupName)) {
            queryWrapper.like("ad_group_name", adGroupName);
        }
        if (ObjectUtils.isNotEmpty(searchTerm)) {
            queryWrapper.like("search_term", searchTerm);
        }
        if (ObjectUtils.isNotEmpty(keyword)) {
            queryWrapper.like("keyword", keyword);
        }
        queryWrapper.between("date", startDate, endDate).orderByDesc("id");
        return selectList(queryWrapper);
    }

    default List<AdsDailySearchTermReportModel> searchByDateBySearchTermLst(String startDate, String endDate, List<String> searchTermLst) {
        if (searchTermLst == null || searchTermLst.isEmpty()) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsDailySearchTermReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendSearchTermCondition(queryWrapper, searchTermLst,false);
        queryWrapper.between("date", startDate, endDate).orderByDesc("id");
        return selectList(queryWrapper);
    }

    default List<AdsDailySearchTermReportModel> searchLstByUniqueSign(List<String> uniqueSignLst) {
        if (uniqueSignLst == null || uniqueSignLst.isEmpty()) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsDailySearchTermReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendSearchTermCondition(queryWrapper, uniqueSignLst,false);
        return selectList(queryWrapper);
    }

    default List<AdsDailySearchTermReportModel> searchByProLstByCampByGroupByStatusByDate(List<AdsAccountProfileModel> profileLst, List<String> campaignIds,
                                                                                          List<String> adGroupIds, Integer status, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(profileLst)) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsDailySearchTermReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendAccountProfileCondition(queryWrapper, profileLst);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            queryWrapper.in("campaign_id", campaignIds);
        }
        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            queryWrapper.in("ad_group_id", adGroupIds);
        }
        if (null != status) {
            if (AdsCommonStatusEnum.EXCEPT_ARCHIVED.getCode() == status) {
                queryWrapper.in("campaign_status", AdsCommonStatusEnum.getExceptArchivedDescList());
            } else {
                queryWrapper.eq("campaign_status", AdsCommonStatusEnum.of(status).getDesc());
            }
        }
        queryWrapper.between("date", startDate, endDate).orderByDesc("id");
        return selectList(queryWrapper);
    }

    default List<AdsDailySearchTermReportModel> searchByUniqueSignByDate(String uniqueSign, String startDate, String endDate) {
        QueryWrapper<AdsDailySearchTermReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendSearchTermCondition(queryWrapper, Arrays.asList(uniqueSign),false);
        queryWrapper.between("date", startDate, endDate).orderByDesc("id");
        return selectList(queryWrapper);
    }

    default int deleteByProfileIdAndDate(String profileId, String date) {
        QueryWrapper<AdsDailySearchTermReportModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("profile_id", profileId).eq("date", date);
        return delete(queryWrapper);
    }

    default List<AdsDailySearchTermReportModel> searchByAccIdByProIdByDate(String accountId, Long profileId, String startDate, String endDate) {
        QueryWrapper<AdsDailySearchTermReportModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_id", accountId);
        queryWrapper.eq("profile_id", profileId);
        queryWrapper.between("date", startDate, endDate);
        return selectList(queryWrapper);
    }

    default List<AdsDailySearchTermReportModel> selectByProLstByDate(List<AdsAccountProfileModel> profileLst, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(profileLst)) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsDailySearchTermReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendAccountProfileCondition(queryWrapper, profileLst);
        queryWrapper.between("date", startDate, endDate);
        return selectList(queryWrapper);
    }

}
