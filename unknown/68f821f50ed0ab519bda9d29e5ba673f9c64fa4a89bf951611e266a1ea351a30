package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 搜索词与商品类目关联模型
 * 用于存储搜索词与商品类目的关联关系
 * 一个搜索词可能关联多个类目，按点击量排序取前三
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermCategoryRelationModel.java
 * @Description 搜索词与类目关联关系模型
 * @createTime 2025年01月31日
 */
@Data
@Accessors(chain = true)
@TableName("ads_search_term_category_relation")
public class AdsSearchTermCategoryRelationModel implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    // 账户id（REFERENCES ads_account_info）
    @TableField(value = "account_id")
    private String accountId;

    // 账户文件id（REFERENCES ads_account_profile）
    @TableField(value = "profile_id")
    private Long profileId;

    // 市场ID
    @TableField(value = "marketplace_id")
    private String marketplaceId;

    // 搜索词
    @TableField(value = "search_term")
    private String searchTerm;

    // 类目ID（关联ads_sp_category表）
    @TableField(value = "category_id")
    private Long categoryId;

    // 类目名称
    @TableField(value = "category_name")
    private String categoryName;

    // 父类目ID
    @TableField(value = "parent_category_id")
    private Long parentCategoryId;

    // 类目层级（1-一级类目，2-二级类目，3-三级类目等）
    @TableField(value = "category_level")
    private Integer categoryLevel;

    // 类目路径（如：Electronics > Computers > Laptops）
    @TableField(value = "category_path")
    private String categoryPath;

    // 该搜索词在此类目下的点击量
    @TableField(value = "clicks_in_category")
    private Integer clicksInCategory;

    // 该搜索词在此类目下的展示量
    @TableField(value = "impressions_in_category")
    private Integer impressionsInCategory;

    // 该搜索词在此类目下的转化量
    @TableField(value = "conversions_in_category")
    private Integer conversionsInCategory;

    // 该搜索词在此类目下的销售额
    @TableField(value = "sales_in_category")
    private Double salesInCategory;

    // 点击量排名（在该搜索词的所有关联类目中的排名，1-最高）
    @TableField(value = "click_rank")
    private Integer clickRank;

    // 关联强度得分（0-100，基于点击量、转化率等计算）
    @TableField(value = "relevance_score")
    private Double relevanceScore;

    // 类目在该搜索词下的市场份额（百分比）
    @TableField(value = "market_share")
    private Double marketShare;

    // 竞争商品数量（该类目下与此搜索词相关的商品数量）
    @TableField(value = "competing_products_count")
    private Integer competingProductsCount;

    // 平均商品价格（该类目下相关商品的平均价格）
    @TableField(value = "average_product_price")
    private Double averageProductPrice;

    // 是否为主要类目（true-前三名类目，false-其他）
    @TableField(value = "is_primary_category")
    private Boolean isPrimaryCategory;

    // 数据统计日期
    @TableField(value = "statistics_date")
    private Date statisticsDate;

    // 数据来源（SP_API_SEARCH_CATALOG_PERFORMANCE等）
    @TableField(value = "data_source")
    private String dataSource;

    // 状态（ACTIVE-有效，INACTIVE-无效）
    @TableField(value = "status")
    private String status;

    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 计算点击转化率
     * @return 转化率百分比
     */
    public Double calculateConversionRate() {
        if (clicksInCategory == null || clicksInCategory == 0) {
            return 0.0;
        }
        if (conversionsInCategory == null) {
            return 0.0;
        }
        return ((double) conversionsInCategory / clicksInCategory) * 100;
    }

    /**
     * 计算点击率
     * @return 点击率百分比
     */
    public Double calculateClickThroughRate() {
        if (impressionsInCategory == null || impressionsInCategory == 0) {
            return 0.0;
        }
        if (clicksInCategory == null) {
            return 0.0;
        }
        return ((double) clicksInCategory / impressionsInCategory) * 100;
    }

    /**
     * 判断是否为热门类目（前三名）
     * @return true-热门类目，false-非热门类目
     */
    public boolean isTopCategory() {
        return clickRank != null && clickRank <= 3;
    }

    /**
     * 计算关联强度得分
     * 基于点击量、转化率、市场份额等因素综合计算
     * @return 关联强度得分（0-100）
     */
    public Double calculateRelevanceScore() {
        if (clicksInCategory == null || marketShare == null) {
            return 0.0;
        }
        
        // 基础得分：点击量权重40%
        double clickScore = Math.min(clicksInCategory / 1000.0 * 40, 40);
        
        // 转化率权重30%
        double conversionScore = calculateConversionRate() * 0.3;
        
        // 市场份额权重30%
        double marketShareScore = marketShare * 0.3;
        
        return Math.min(clickScore + conversionScore + marketShareScore, 100.0);
    }
}
