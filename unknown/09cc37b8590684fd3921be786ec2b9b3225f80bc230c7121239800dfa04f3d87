package com.yiyitech.ads.filter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AwsRequestSigningApacheInterceptor.java
 * @Description
 * @createTime 2025年06月19日 18:16:00
 */
import org.apache.http.*;
import org.apache.http.protocol.HttpContext;
import org.apache.http.entity.BufferedHttpEntity;
import org.apache.http.util.EntityUtils;

import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.signer.Aws4Signer;
import software.amazon.awssdk.auth.signer.params.Aws4SignerParams;
import software.amazon.awssdk.http.SdkHttpFullRequest;
import software.amazon.awssdk.http.SdkHttpMethod;
import software.amazon.awssdk.regions.Region;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URI;
import java.util.*;

public class AwsRequestSigningApacheInterceptor implements HttpRequestInterceptor {

    private final String serviceName;
    private final Aws4Signer signer;
    private final AwsCredentialsProvider credentialsProvider;

    public AwsRequestSigningApacheInterceptor(String serviceName,
                                              Aws4Signer signer,
                                              AwsCredentialsProvider credentialsProvider) {
        this.serviceName = serviceName;
        this.signer = signer;
        this.credentialsProvider = credentialsProvider;
    }

    @Override
    public void process(HttpRequest request, HttpContext context) throws HttpException, IOException {
        String method = request.getRequestLine().getMethod();

        //组装url常用写法
        HttpHost targetHost = (HttpHost) context.getAttribute("http.target_host");
        String fullUrl = targetHost.toURI() + request.getRequestLine().getUri();
        URI uri = URI.create(fullUrl);

        //组装url写法二（HttpRequestWrapper有apache和springframework两种包都试试）
//        URI uri;
//        if (request instanceof HttpRequestWrapper) {
//            uri = ((HttpRequestWrapper) request).getURI();
//        } else {
//            HttpHost targetHost = (HttpHost) context.getAttribute("http.target_host");
//            String fullUrl = targetHost.toURI() + request.getRequestLine().getUri();
//            uri = URI.create(fullUrl);
//        }
//        System.out.println("签名前使用的完整 URI: " + uri);


        // 构建 headers
        Map<String, List<String>> headers = new HashMap<>();
        for (Header header : request.getAllHeaders()) {
            String name = header.getName().toLowerCase(Locale.ROOT);
            headers.computeIfAbsent(name, k -> new ArrayList<>()).add(header.getValue());
        }

        // 设置 host header（AWS Signature 要求必须有）
        headers.put("host", Collections.singletonList(uri.getHost()));

        // 构建 SdkHttpFullRequest.Builder
        SdkHttpFullRequest.Builder requestBuilder = SdkHttpFullRequest.builder()
                .method(SdkHttpMethod.valueOf(method.toUpperCase(Locale.ROOT)))
                .uri(uri)
                .headers(headers);

        // 如果是 POST/PUT 等有请求体的请求
        if (request instanceof HttpEntityEnclosingRequest) {
            HttpEntityEnclosingRequest enclosingRequest = (HttpEntityEnclosingRequest) request;
            HttpEntity entity = enclosingRequest.getEntity();
            if (entity != null) {
                BufferedHttpEntity bufferedEntity = new BufferedHttpEntity(entity); // 可重复读取
                byte[] content = EntityUtils.toByteArray(bufferedEntity);
                requestBuilder.contentStreamProvider(() -> new ByteArrayInputStream(content));
                enclosingRequest.setEntity(bufferedEntity); // 写回去
            }
        }

        // 构建签名参数
//        Aws4SignerParams signerParams = Aws4SignerParams.builder()
//                .signingName(serviceName)
//                .signingRegion(resolveRegionFromHost(uri.getHost()))
//                .awsCredentials(credentialsProvider.resolveCredentials())
//                .build();
        String host = uri.getHost();
        Region region = resolveRegionFromHost(host != null ? host : "");
        Aws4SignerParams signerParams = Aws4SignerParams.builder()
                .signingName(serviceName)
                .signingRegion(region)
                .awsCredentials(credentialsProvider.resolveCredentials())
                .build();

        // 签名
        SdkHttpFullRequest signedRequest = signer.sign(requestBuilder.build(), signerParams);

        // 将签名头写回原始请求
        for (Map.Entry<String, List<String>> entry : signedRequest.headers().entrySet()) {
            request.removeHeaders(entry.getKey());
            for (String value : entry.getValue()) {
                request.addHeader(entry.getKey(), value);
            }
        }
    }

    /**
     * 根据域名解析出 AWS Region
     */
    private Region resolveRegionFromHost(String host) {
        host = host.toLowerCase(Locale.ROOT);
        if (host.contains("sellingpartnerapi-na.amazon.com")) {
            return Region.US_EAST_1;
        } else if (host.contains("sellingpartnerapi-eu.amazon.com")) {
            return Region.EU_WEST_1;
        } else if (host.contains("sellingpartnerapi-fe.amazon.com")) {
            return Region.AP_NORTHEAST_1;
        }
        return Region.US_EAST_1; // 默认区域
    }

}