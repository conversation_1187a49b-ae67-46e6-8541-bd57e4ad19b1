package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.yiyitech.ads.exception.UserExceptionCode;
import com.yiyitech.ads.mapper.*;
import com.yiyitech.ads.model.*;
import com.yiyitech.ads.service.AdsLoginService;
import com.yiyitech.ads.service.CommonService;
import com.yiyitech.ads.util.TokenUtil;
import com.yiyitech.ads.vo.AdsUserVO;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AdsLoginServiceImpl implements AdsLoginService {
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private CommonService commonService;
    @Autowired
    private AdsUserMapper adsUserMapper;
    @Autowired
    private AdsUserRoleMapper adsUserRoleMapper;
    @Autowired
    private AdsRolePermissionMapper adsRolePermissionMapper;
    @Autowired
    private AdsRoleMapper adsRoleMapper;
    @Autowired
    private AdsPermissionMapper adsPermissionMapper;
    @Autowired
    private AdsLogoutLogMapper adsLogoutLogMapper;

    @Override
    public Map<String, Object> register(String phone, String password, String code) {
        Map<String, Object> map = new HashMap<>();

//        AdsUserModel model = new AdsUserModel().setPhone(phone).setPassword(passwordEncoder.encode(password)).setStatus(true);
//        System.out.println(model.getPassword());

        if(CollectionUtils.isNotEmpty(adsUserMapper.searchByPhone(phone))){
            log.error("用户{"+phone+"}已注册");
            throw new BusinessException(UserExceptionCode.USER_ALREADY_EXIST_CODE, UserExceptionCode.USER_ALREADY_EXIST_MSG);
        }
        AdsUserModel model = new AdsUserModel().setPhone(phone).setPassword(passwordEncoder.encode(password)).setStatus(true);
        adsUserMapper.insert(model);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> login(String phone, String password) {
        Map<String, Object> map = new HashMap<>();
        if (!StringUtils.hasText(phone) || !StringUtils.hasText(password)) {
            throw new BusinessException(UserExceptionCode.USER_INFO_IS_EMPTY_CODE, UserExceptionCode.USER_INFO_IS_EMPTY_MSG);
        }
        List<AdsUserModel> userModelLst  = adsUserMapper.searchByPhone(phone);
        if (CollectionUtils.isEmpty(userModelLst)) {
            throw new BusinessException(UserExceptionCode.USER_NOT_EXIST_CODE, UserExceptionCode.USER_NOT_EXIST_MSG);
        }
        AdsUserModel user = userModelLst.get(0);
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new BusinessException(UserExceptionCode.PASSWORD_ERROR_CODE, UserExceptionCode.PASSWORD_ERROR__MSG);
        }
        //TODO 权限角色放入redis取用
        //查询用户拥有的角色和权限
        List<AdsUserRoleModel> roleModels = adsUserRoleMapper.searchByUserId(user.getId());
        List<Long> roleIds = roleModels.stream().map(AdsUserRoleModel::getRoleId).collect(Collectors.toList());
        List<AdsRolePermissionModel> permissionModels = adsRolePermissionMapper.searchByRoleIds(roleIds);
        List<Long> permissionIds = permissionModels.stream().map(AdsRolePermissionModel::getPermissionId).collect(Collectors.toList());
        List<AdsRoleModel> roleLst = roleIds.size() < 0 ? new ArrayList<>() : adsRoleMapper.selectBatchIds(roleIds);
        List<AdsPermissionModel> permissionLst = permissionIds.size() < 0 ? new ArrayList<>() :  adsPermissionMapper.selectBatchIds(permissionIds);
        //角色和权限信息放入token
        List<String> roleList = roleLst.stream().map(AdsRoleModel::getRoleCode).collect(Collectors.toList());
        List<String> permissionList = permissionLst.stream().map(AdsPermissionModel::getPermissionCode).collect(Collectors.toList());


        String token = TokenUtil.generateToken(user.getId().toString(), user.getUsername(), user.getParentId().toString(), phone, roleList, permissionList);
        AdsUserVO Vo = new AdsUserVO();
        Vo.setToken(token);
        map.put("data", Vo);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public void loginOut(Long userId, boolean isManager) {
        if (isManager) {
            return;
        }
        List<AdsAccountProfileModel> profileLst = (List<AdsAccountProfileModel>) commonService.searchFilterOrCurrentProfileInfo(userId, null).get("profiles");
        Date now = new Date();
        for (AdsAccountProfileModel profile : profileLst) {
            String accountId = profile.getAccountId();
            Long profileId = profile.getProfileId();
            Long accProId = profile.getId();
            List<AdsLogoutLogModel> existingLogs = adsLogoutLogMapper.searchByAccountIdByProfileId(accountId, profileId);
            if (CollectionUtils.isNotEmpty(existingLogs)) {
                for (AdsLogoutLogModel log : existingLogs) {
                    log.setUpdateTime(now)
                            .setUserId(userId)
                            .setUpdateBy(String.valueOf(userId));
                    adsLogoutLogMapper.updateById(log);
                }
            } else {
                AdsLogoutLogModel newLog = new AdsLogoutLogModel()
                        .setAccProId(accProId)
                        .setAccountId(accountId)
                        .setProfileId(profileId)
                        .setUserId(userId)
                        .setCreateTime(now)
                        .setUpdateTime(now)
                        .setCreateBy(String.valueOf(userId))
                        .setUpdateBy(String.valueOf(userId));
                adsLogoutLogMapper.insert(newLog);
            }
        }
    }
}
