package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsLogoutLogModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsLogoutLogMapper.java
 * @Description
 * @createTime 2025年07月08日 11:54:00
 */
public interface AdsLogoutLogMapper extends AbstractBaseMapper<AdsLogoutLogModel> {

    default List<AdsLogoutLogModel> searchByAccountIdByProfileId(String accountId, Long profileId) {
        QueryWrapper<AdsLogoutLogModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_id", accountId);
        queryWrapper.eq("profile_id", profileId);
        return selectList(queryWrapper);
    }

    default List<AdsLogoutLogModel> searchByAccProIds(List<Long> accProIds) {
        QueryWrapper<AdsLogoutLogModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("acc_pro_id", accProIds);
        return selectList(queryWrapper);
    }

}
