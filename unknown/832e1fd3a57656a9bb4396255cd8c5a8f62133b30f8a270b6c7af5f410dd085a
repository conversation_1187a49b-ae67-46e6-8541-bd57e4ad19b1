package com.yiyitech.ads.config;

import com.yiyitech.ads.filter.AwsRequestSigningApacheInterceptor;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.signer.Aws4Signer;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RestTemplateConfig.java
 * @Description
 * @createTime 2025年06月18日 15:59:00
 */
@Configuration
public class RestTemplateConfig {
    @Value("${amazon.spapi.clientId}")
    private String accessKey;

    @Value("${amazon.spapi.clientSecret}")
    private String secretKey;

    @Bean("defaultRestTemplate")
    public RestTemplate defaultRestTemplate() {
        return new RestTemplate();
    }

    @Bean("awsSignedRestTemplate")
    public RestTemplate awsSignedRestTemplate() {
//        AwsCredentialsProvider credentialsProvider = DefaultCredentialsProvider.create();
        AwsCredentialsProvider credentialsProvider = () -> AwsBasicCredentials.create(accessKey, secretKey);
        Aws4Signer signer = Aws4Signer.create();
        AwsRequestSigningApacheInterceptor interceptor = new AwsRequestSigningApacheInterceptor(
                "execute-api", signer, credentialsProvider
        );
        HttpClient client = HttpClients.custom()
                .addInterceptorLast(interceptor)
                .build();
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(client);
        return new RestTemplate(factory);
    }
}
