package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.exception.CampaignExceptionCode;
import com.yiyitech.ads.exception.GroupExceptionCode;
import com.yiyitech.ads.exception.KeywordExceptionCode;
import com.yiyitech.ads.model.response.AdsProfileResponse;
import com.yiyitech.ads.model.response.AdsTokenResponse;
import com.yiyitech.ads.service.AmazoService;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.ads.vo.AdsCampaignVO;
import com.yiyitech.ads.vo.AdsAdGroupVO;
import com.yiyitech.ads.vo.AdsTopKeyWordVO;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AmazoServiceImpl.java
 * @Description
 * @createTime 2023年11月16日 15:39:00
 */
@Slf4j
@Service
public class AmazoServiceImpl implements AmazoService{
    @Autowired
    private RedisUtils redisUtils;

    private static final String clientId = "amzn1.application-oa2-client.2797fe8ed5f1484d96e0a1f04faa2b1e";
    private static final String clientSecret = "amzn1.oa2-cs.v1.8c152ec95cd8fe7f53b68bdd5a3256faef675d167d75f4e85a51d1318b4ec48b";
    private static final String tokenUrl = "https://api.amazon.com/auth/o2/token";
    private static final String apiUrl = "https://advertising-api.amazon.com";
    private static final String REDIRECT_URI = "https://www.wkefu.com/";
    private static final String PROFILEID = "1762485261961949";

    @Override
    public void accessToken(String code) {
        HttpPost request = new HttpPost(tokenUrl);
        String grant_type = "authorization_code";
        String reqdata = "{"
                + "\"grant_type\": \""+grant_type+"\","
                + "\"code\": \""+code+"\","
                + "\"redirect_uri\": \""+REDIRECT_URI+"\","
                + "\"client_id\": \""+clientId+"\","
                + "\"client_secret\": \""+clientSecret+"\"" + "}";
        StringEntity entity = new StringEntity(reqdata,"utf-8");
        entity.setContentType("application/x-www-form-urlencoded");
        request.setEntity(entity);
        request.setHeader("Accept", "*/*");
        request.setHeader("Content-Type", "application/json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = null;
            response = httpClient.execute(request);
            HttpEntity entityy = response.getEntity();
            String responseString = EntityUtils.toString(entityy, "UTF-8");
            System.out.println(responseString);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void refreshToken(String refreshToken) {
        refreshToken = "Atzr|IwEBIKFc7JTjSQrrPvyy0JikT80kQj6XrZfcTtxO_46FOBLG-Lro6aK3ckv2sjPhsyNcyHNX1r8p9GkIRNPgE4Izwx8gUZ1QWwEyPHQNpC2gVmg9XINcqf7UpBe4cW4Y2YqW4NJvqPz9g652nB9YD-ZPtCVxESAQ77Bi-j32eRBAL-4kTDHO7pPGGYw-naUkt5NiinUxIeWCCcEy2N7KCySLfQTrt6xcS-XLvCNvKbytJqRyw4EQikXVz8eqnwUctHhDSBTf1u_HM6yf2QSsqYcqfgcvKAJ6I0qS0sYT6ileYNEcA4Mqxk6HkzKSRzT2TsOReFedRyKfIjxwQFBo0KXn1JsDwOhQ6l28DyKd918KVOgnW-sBDZI4i97g2FRmyJvIicqfJWL-RwNl8tJbakQJRIPxJ0_2FgogB775q1BogrFuA-Dka_fe3WcMh5buMRrjp-2hvgXf61RXNE20fs94vFwm";
        HttpPost request = new HttpPost(tokenUrl);
        String grant_type = "refresh_token";
        String reqdata = "{"
                + "\"grant_type\": \""+grant_type+"\","
                + "\"refresh_token\": \""+refreshToken+"\","
                + "\"client_id\": \""+clientId+"\","
                + "\"client_secret\": \""+clientSecret+"\"" + "}";
        StringEntity entity = new StringEntity(reqdata,"utf-8");
        entity.setContentType("application/x-www-form-urlencoded");
        request.setEntity(entity);
        request.setHeader("Accept", "*/*");
        request.setHeader("Content-Type", "application/json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = null;
            response = httpClient.execute(request);
            AdsTokenResponse tokenResponses = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsTokenResponse.class);
            System.out.println(tokenResponses.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public Map<String, Object> profileList(String token) {
        HttpGet request = new HttpGet(apiUrl+"/v2/profiles");
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Authorization", "Bearer " + token);
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = null;
            response = httpClient.execute(request);
            String responseString = EntityUtils.toString(response.getEntity(), "UTF-8");
            List<AdsProfileResponse> lst = JSONArray.parseArray(responseString, AdsProfileResponse.class);
            System.out.println(lst.get(0).toString());
        } catch (IOException e) {
            e.printStackTrace();
        }


        Map<String, Object> map = new HashMap<>();
        map.put("list",null);
        return null;
    }

    @Override
    public Map<String, Object> campaignList() {
        Map<String, Object> data = new HashMap<>();
        HttpPost request = new HttpPost(apiUrl+"/sp/campaigns/list");
//        JSONObject reqObj = new JSONObject();
//        reqObj.put("stateFilter", new JSONObject().put("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 50);
//        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
//        request.setHeader("Authorization", "Bearer " + redisUtils.get(CommonConstants.ACCESS_TOKEN_REDIS_KEY).toString());
        request.setHeader("Amazon-Advertising-API-Scope", PROFILEID);
        request.setHeader("Accept", "application/vnd.spCampaign.v3+json");
//        request.setHeader("Content-Type", "application/json");
        request.setHeader("Content-Type", "application/vnd.spCampaign.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = null;
            response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("活动列表api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            Map maps = (Map) JSON.parse(EntityUtils.toString(response.getEntity(), "UTF-8"));
            List<AdsCampaignVO> campaignVOLst = JSONArray.parseArray(JSONArray.toJSONString(maps.get("campaigns")), AdsCampaignVO.class);
            if (null == campaignVOLst || campaignVOLst.isEmpty()) {
                throw new BusinessException(CampaignExceptionCode.CAMPAIGNS_NOT_EXIST_CODE, CampaignExceptionCode.CAMPAIGNS_NOT_EXIST_MSG);
            }
            data = campaignVOLst.stream().collect(Collectors.toMap(AdsCampaignVO::getCampaignId, AdsCampaignVO::getName));
        } catch (IOException e) {
            log.error("活动列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("活动列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
        return data;
    }

    @Override
    public Map<String, Object> adGroupList() {
        Map<String, Object> data = new HashMap<>();
        HttpPost request = new HttpPost(apiUrl+"/sp/adGroups/list");
        // 请求body参数
//        JSONObject reqObj = new JSONObject();
//        reqObj.put("campaignIdFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd(campaignId)));
//        reqObj.put("stateFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 10);
//        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Amazon-Advertising-API-Scope", PROFILEID);
//        request.setHeader("Authorization", "Bearer " + redisUtils.get(CommonConstants.ACCESS_TOKEN_REDIS_KEY).toString());
        request.setHeader("Accept", "application/vnd.spAdGroup.v3+json");
        request.setHeader("Content-Type", "application/vnd.spAdGroup.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = null;
            response = httpClient.execute(request);
            if(response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK){
                log.error("组列表api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            Map maps = (Map) JSON.parse(EntityUtils.toString(response.getEntity(), "UTF-8"));
            List<AdsAdGroupVO> groupVOLst = JSONArray.parseArray(JSONArray.toJSONString(maps.get("adGroups")), AdsAdGroupVO.class);
            if (null == groupVOLst || groupVOLst.isEmpty()) {
                throw new BusinessException(GroupExceptionCode.GROUPS_NOT_EXIST_CODE, GroupExceptionCode.GROUPS_NOT_EXIST_CODE_NOT_EXIST_MSG);
            }
            data = groupVOLst.stream().collect(Collectors.toMap(AdsAdGroupVO::getAdGroupId, AdsAdGroupVO::getName));
        } catch (IOException e) {
            log.error("组列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("组列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
        return data;
    }

    @Override
    public Map<String, Object> queryTopKeywordsList() {
        Map<String, Object> map = new HashMap<>();
        List<AdsTopKeyWordVO> tKeyWordVO = new ArrayList<>();
        Map<String, Object> campaignsMap = this.campaignList();
        Map<String, Object> groupsMap = this.adGroupList();
        HttpPost request = new HttpPost(apiUrl + "/sp/keywords/list");
        JSONObject reqObj = new JSONObject();
//        reqObj.put("campaignIdFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd(campaignId)));
//        reqObj.put("stateFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 10);
        reqObj.put("includeExtendedDataFields", true);
        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Amazon-Advertising-API-Scope", PROFILEID);
//        request.setHeader("Authorization", "Bearer " + redisUtils.get(CommonConstants.ACCESS_TOKEN_REDIS_KEY).toString());
        request.setHeader("Accept", "application/vnd.spKeyword.v3+json");
        request.setHeader("Content-Type", "application/vnd.spKeyword.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = null;
            response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("关键词列表api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            Map maps = (Map) JSON.parse(EntityUtils.toString(response.getEntity(), "UTF-8"));
            tKeyWordVO = JSONArray.parseArray(JSONArray.toJSONString(maps.get("keywords")), AdsTopKeyWordVO.class);
            if (null == tKeyWordVO || tKeyWordVO.isEmpty()) {
                throw new BusinessException(KeywordExceptionCode.KEYWORDS_NOT_EXIST_CODE, KeywordExceptionCode.KEYWORDS_NOT_EXIST_CODE_NOT_EXIST_MSG);
            }
            tKeyWordVO.stream().forEach(word -> {
                word.setCampaignName(campaignsMap.get(word.getCampaignId()).toString());
                word.setGroupName(groupsMap.get(word.getAdGroupId()).toString());
            });
            tKeyWordVO.forEach(a -> a.defaultData(a));
//        tKeyWordVO.forEach(a -> System.out.println(a.toString()));
            map.put("status", HttpStatus.HTTP_OK);
            map.put("data", tKeyWordVO);
        } catch (IOException e) {
            log.error("关键词列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("关键词列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
        return map;
    }

    @Override
    public void setRedis() {
        redisUtils.setAccountAccessTokenAndRefreshToken("A3QXCPZWLQ2IWO", "Atzr|IwEBIKypR9dQL-Ecwq9Jtffp4zKx0KcURyjMrcloH52y3OD-W2IKbunseJgExFF2XBXsUjzJXbKZmdT2TBrMJ5MN3Ee_GwoDpHDHipSBox4RIjpGxQ6Kd9JL1iDpJRtVFfr-NRWog_Qsh3Zu5oeS1rghIewpC4Z8v_550WhZBZA4CWxevaZ5MmHFIQc9rWX7y7c60frSIGr1k1o7scbPZ5SCRRTtLE32tsFpTdzr90GejsxHjb9VBdlY9VLmA5v96mSrkJWK9jPle3knff4bc4ZaR1JxvVHMinCwPqcEjj_TmAPD4qJpKxakJuRiIZ46reFplPicLpDt2S2Ovl7auDbDJ-fIZY1by-V6yQoGy3AwGbbjE39xbK9qkwIy7jXYv8VGY8YP4_ep_-MYFbr8UzEO8lDqs0bh7IYTFcScesTeQoSp6spb7fNQAQK9oaWUskwI6MRZJHaZjU6wty3DFS8FlGbP7lPH6vhtJJZ32nOhgBF_9Q", "", CommonConstants.ADS_PREFIX_KEY);
        redisUtils.setAccountAccessTokenAndRefreshToken("AF96VBWMIRW5L", "Atzr|IwEBIBb_D5hftz0NHB0zrXcKmFvSBJtPfpTK0Kpkhlpsiq1KGBNX58ebbUHgwwdR306xXLe5IKs6tN8aOnc0PgBgYqlnlIJtyri_cZmRHnpNypYKOYt4KTplBK0KFf-dd9VNqDKoE6-0anMoB_XAVDmgeMbxdVcywAmd7vQbfLw8vwSnrYmJCkmi94glwcD-jzq5MTHoI8hp6zRkmfZinsGPlVKkm0fqqTDIc_yy7gYZjn9NuCUKNAsJPGG0kzoloC2qkNfo0qQPp8j5APiwmN95m2ATU-SZMU8y-E5TQAo_GkdYpjhQIUblVmnbRe8kzyp3wQYpsTeJL1ycbWYsJiYdQQ7Mzg0KF9A5bSIr7oBFrxOe7FGgpUjYAZFTKpWGpkNE5_QMJ-MDrGoJPHtV5QqLupNDCXHtsLKKGNFi1Sjrm9Uhmym06f711lg-ft3gzrxEL6ERUM1GV4YQdRbdmK4hMQRe", "", CommonConstants.ADS_PREFIX_KEY);
        redisUtils.setAccountAccessTokenAndRefreshToken("A17DJHZT1NEVL4", "Atzr|IwEBIOlHhRyFOTrt_oD0ysDmUSZ6mHCO6joZKvZMDqr323pYMt3T2AIW8DE8Jdsh1Z2X-wt9NipgEcl7lByF3LdBHAHMZJIjIsVU9M2HNHLKgvMOA5-frFVH4FFlHQu5AwPcjVX5ebLZ0UVeZjHp6Sk2TpQx0-MGbNNZbZTtIhCyUhn3CRBeDb-3WyTaZWa2T3tbfCClLkD0Bp0PvTE7e6I3cy0H2enYHt7EgHtpIBSeqMJBCt09XabERrMlpgunE1ezDbNwt2yc7hNllt5Z-ZnWwdCDkwvXXc69hlfU68ecXpcOqGB_JzlYr5keqayULEaCSv2DY-ACe5zWYjyQwzlQP6LUqNyBlsfQxEI-4sPpYIkmir70e1JK4CvhMNH0SSEQmv0i9cvq-rORg7bfTwkk6_nsMKqETm4FJytkK-dc3dDRlyDkOL518572y89zVfzrl1VWDlvY1xkZ-JN9YqOhYiiN4gZ6aVpeDAgNJGNTgjKXyw", "", CommonConstants.ADS_PREFIX_KEY);
        redisUtils.setAccountAccessTokenAndRefreshToken("A1G4JLDUB3JG9", "Atzr|IwEBIIGsYP2lrkBhoU8BlhW_tuRP8E5PiEMQeWWa9u_GqhQO3N10j8SBts94q3HcQybNaNsqjsmXeWX8tMmZbqkchoT2ShW67LTasCF7bm73ef_MZ_V2ExADGodctB1zvmvfmzfmweERBUY9KCKJF-1iByInDl6X4qRUwCYS-CMyJ3_Ej0DwuJkblENIgLndhIIwm_03MWGoMkKlpIto-GMrgPAyDU-x90c2-1BSUo4omteSBQsDtPQpoWx_7pHzTAAlGDKcbEelyuVdaxANvr0SdhLOigy_Bptl_zvpCSKkJwda6bQaeRQ_nwc-Z8bscpB5TH2ZKq9pkGF_h9aUabtagAgTmb8GwDCdp3Iy6TfmAHqO0NmgBKyVdfhmhdxE6o2nn6-0pRwDSsj9RfKQz4Zvdtj106NmeA2oGxpWbE8vSNUIxc9sokTiHzeRnJLn08JUJsb6DaK0HZtgB7iqlZhprJ6E", "", CommonConstants.ADS_PREFIX_KEY);
        redisUtils.setAccountAccessTokenAndRefreshToken("A1HCWIVLDP3LFS", "Atzr|IwEBILsbnIvlR4nCjN293l9YzhGxCUy0KXQXgnnmq8xnZDPwNLD9p2cFfcxfgCk6NBv4wHymLOaSheIAQqoyobV_Fb9lvh8avEux4TKdZdfhAxzT9VwpjxNoWWJWCggHoteQ6ac3eE2p-2lLqZxNRJCAIGUvx4A3zI5JPNfeugGiRvR1zPNHFdbU4KWpZ452KO1g1cE1RDHcy92kVMVQeOa8IzbqCURaMWbmpJwx3Lw5KNx4Wi35M8HS7COo7SapP_AVtGv_oXd_7rKXuTml6jNJv1eniK7MgJb4DOfx1l50qVcp2G-pplmp9FAliEQm2D_zBNIe5sDWQzEp6n9b3sBGee3b_kv42Qpf7VRqODdeKJLQkIICdbLBLmadLffRTT2e4LrnNTyYCPvwp-GQvdevwvFvaURyLuv6rdi_k2M6BTy3ScJdMtWIi2ECd9DLyLkOrWSCAVGFVpNaflVovY0syslr", "", CommonConstants.ADS_PREFIX_KEY);
    }

    public static void main(String[] args) {
        AmazoServiceImpl aa = new AmazoServiceImpl();
//        aa.accessToken("ANzPanmbLzNrLjmBuvOM");
        aa.refreshToken("");
//        aa.profileList("");
//        aa.campaignList();
//        aa.adGroupList();
//        aa.queryTopKeywordsList();
    }

}
