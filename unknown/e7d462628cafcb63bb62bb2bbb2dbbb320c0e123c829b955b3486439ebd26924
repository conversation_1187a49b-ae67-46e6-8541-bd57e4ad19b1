package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsSpCategoryModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpCategoryMapper.java
 * @Description
 * @createTime 2025年02月18日 13:39:00
 */
public interface AdsSpCategoryMapper extends AbstractBaseMapper<AdsSpCategoryModel> {

    default List<AdsSpCategoryModel> searchByAccIdsByProIdsByCatId(List<String> accountIds, List<String> profileIds, String categoryId) {
        QueryWrapper<AdsSpCategoryModel> queryWrapper = new QueryWrapper<>();
        if (null != accountIds) {
            queryWrapper.in("account_id", accountIds);
        }
        if (null != profileIds) {
            queryWrapper.in("profile_id", profileIds);
        }
        if (null != categoryId) {
            queryWrapper.eq("category_id", categoryId);
        }
        return selectList(queryWrapper);
    }
}
