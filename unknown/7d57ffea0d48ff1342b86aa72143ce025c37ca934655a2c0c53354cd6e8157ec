package com.yiyitech.ads.config;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DataSourceConfig.java
 * @Description
 * @createTime 2023年11月30日 13:42:00
 */

//@Configuration
public class DataSourceConfig {

//    @Primary
//    @Bean
//    @ConfigurationProperties("spring.datasource.dynamic.datasource.master")
//    public DataSource dataSourcePrimary(){
//        return DruidDataSourceBuilder.create().build();
//    }
//
//
//    @Bean
//    @ConfigurationProperties("spring.datasource.dynamic.datasource.wangmeng")
//    public DataSource dataSourceWangmeng(){
//        return DruidDataSourceBuilder.create().build();
//    }
}
