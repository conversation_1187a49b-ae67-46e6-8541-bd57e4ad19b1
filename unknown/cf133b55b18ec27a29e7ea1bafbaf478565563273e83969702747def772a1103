package com.yiyitech.ads.service.api;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.request.AdsPortfolioSearchRequest;
import com.yiyitech.ads.model.response.AdsPortfolioSearchResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsPortfolioApi.java
 * @Description
 * @createTime 2025年02月18日 20:36:00
 */
@Slf4j
@Component
public class AdsPortfolioApi {
    @Value("${client.id}")
    private String clientId;
    @Value("${url.api_url}")
    private String apiUrl;

    /**
     * 获取站点活动夹列表
     * @param accessToken
     * @param profileId
     * @param adsPortfolioSearchRequest
     * @return
     */
    public AdsPortfolioSearchResponse portfolioSearch(String accessToken, String profileId, AdsPortfolioSearchRequest adsPortfolioSearchRequest) {
        HttpPost request = new HttpPost(apiUrl + "/portfolios/list");
//        JSONObject reqObj = new JSONObject();
//        reqObj.put("campaignIdFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd(campaignId)));
//        reqObj.put("stateFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 10);
//        reqObj.put("includeExtendedDataFields", true);
        request.setEntity(new StringEntity(new Gson().toJson(adsPortfolioSearchRequest), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Prefer", "true");
        request.setHeader("Authorization", "Bearer " + accessToken);
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("关键词建议api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsPortfolioSearchResponse portfolioSearchResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsPortfolioSearchResponse.class);
            return portfolioSearchResponse;
        } catch (IOException e) {
            log.error("关键词建议异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("关键词建议异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }
}
