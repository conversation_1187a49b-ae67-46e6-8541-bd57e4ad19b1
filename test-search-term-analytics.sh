#!/bin/bash

# 搜索词分析功能测试脚本
# 用于快速验证搜索词分析系统的各项功能

# 配置变量
BASE_URL="http://localhost:8080"
API_PREFIX="/api/v1/ads/search-term-analytics"
TEST_ACCOUNT_ID="TEST_ACCOUNT"
TEST_PROFILE_ID="123"
TEST_MARKETPLACE_ID="ATVPDKIKX0DER"
TEST_SEARCH_TERM="wireless%20headphones"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试函数
test_api() {
    local test_name="$1"
    local url="$2"
    local expected_status="$3"
    
    log_info "测试: $test_name"
    log_info "URL: $url"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url")
    http_status=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$http_status" -eq "$expected_status" ]; then
        log_success "HTTP状态码正确: $http_status"
        
        # 检查响应是否包含success字段
        if echo "$body" | grep -q '"success":true'; then
            log_success "响应包含success:true"
        elif echo "$body" | grep -q '"success":false'; then
            log_warning "响应包含success:false"
            echo "响应内容: $body"
        else
            log_warning "响应不包含success字段"
        fi
    else
        log_error "HTTP状态码错误: 期望 $expected_status, 实际 $http_status"
        echo "响应内容: $body"
    fi
    
    echo "----------------------------------------"
}

# 主测试函数
main() {
    echo "========================================"
    echo "搜索词分析功能测试开始"
    echo "========================================"
    
    # 1. 测试应用健康检查
    log_info "1. 应用健康检查"
    test_api "健康检查" "${BASE_URL}/actuator/health" 200
    
    # 2. 测试获取搜索词分析数据列表
    log_info "2. 获取搜索词分析数据列表"
    test_api "搜索词列表" "${BASE_URL}${API_PREFIX}/list?accountId=${TEST_ACCOUNT_ID}&profileId=${TEST_PROFILE_ID}&pageNum=1&pageSize=10" 200
    
    # 3. 测试获取搜索词详细信息
    log_info "3. 获取搜索词详细信息"
    test_api "搜索词详情" "${BASE_URL}${API_PREFIX}/details?accountId=${TEST_ACCOUNT_ID}&profileId=${TEST_PROFILE_ID}&searchTerm=${TEST_SEARCH_TERM}" 200
    
    # 4. 测试获取搜索词关联类目
    log_info "4. 获取搜索词关联类目"
    test_api "关联类目" "${BASE_URL}${API_PREFIX}/categories?accountId=${TEST_ACCOUNT_ID}&profileId=${TEST_PROFILE_ID}&searchTerm=${TEST_SEARCH_TERM}" 200
    
    # 5. 测试获取搜索词关联商品
    log_info "5. 获取搜索词关联商品"
    test_api "关联商品" "${BASE_URL}${API_PREFIX}/products?accountId=${TEST_ACCOUNT_ID}&profileId=${TEST_PROFILE_ID}&searchTerm=${TEST_SEARCH_TERM}" 200
    
    # 6. 测试获取热门搜索词
    log_info "6. 获取热门搜索词"
    test_api "热门搜索词" "${BASE_URL}${API_PREFIX}/top-search-terms?accountId=${TEST_ACCOUNT_ID}&profileId=${TEST_PROFILE_ID}&limit=10" 200
    
    # 7. 测试获取搜索词趋势
    log_info "7. 获取搜索词趋势"
    test_api "搜索词趋势" "${BASE_URL}${API_PREFIX}/trends?accountId=${TEST_ACCOUNT_ID}&profileId=${TEST_PROFILE_ID}&searchTerm=${TEST_SEARCH_TERM}&startDate=2025-01-01&endDate=2025-01-31" 200
    
    # 8. 测试竞争分析
    log_info "8. 竞争分析"
    test_api "竞争分析" "${BASE_URL}${API_PREFIX}/competition-analysis?accountId=${TEST_ACCOUNT_ID}&profileId=${TEST_PROFILE_ID}&searchTerm=${TEST_SEARCH_TERM}" 200
    
    # 9. 测试搜索词建议
    log_info "9. 搜索词建议"
    test_api "搜索词建议" "${BASE_URL}${API_PREFIX}/suggestions?accountId=${TEST_ACCOUNT_ID}&profileId=${TEST_PROFILE_ID}&seedKeyword=wireless&limit=10" 200
    
    # 10. 测试错误处理 - 缺少参数
    log_info "10. 错误处理测试 - 缺少参数"
    test_api "缺少参数" "${BASE_URL}${API_PREFIX}/details" 400
    
    # 11. 测试数据同步接口（需要有效的Amazon账户数据）
    log_info "11. 数据同步测试（可能失败，需要有效的Amazon账户）"
    test_api "数据同步" "${BASE_URL}${API_PREFIX}/sync/analytics" 200
    
    echo "========================================"
    echo "搜索词分析功能测试完成"
    echo "========================================"
}

# 数据库测试函数
test_database() {
    echo "========================================"
    echo "数据库功能测试"
    echo "========================================"
    
    log_info "检查数据库表是否存在..."
    
    # 这里需要根据你的数据库配置调整
    DB_HOST="localhost"
    DB_PORT="3306"
    DB_NAME="your_database"
    DB_USER="your_username"
    DB_PASS="your_password"
    
    # 检查表是否存在
    tables=("ads_search_term_analytics" "ads_search_term_category_relation" "ads_search_term_product_ranking")
    
    for table in "${tables[@]}"; do
        log_info "检查表: $table"
        result=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS $DB_NAME -e "SHOW TABLES LIKE '$table';" 2>/dev/null | grep $table)
        if [ -n "$result" ]; then
            log_success "表 $table 存在"
        else
            log_error "表 $table 不存在"
        fi
    done
    
    echo "========================================"
}

# 性能测试函数
test_performance() {
    echo "========================================"
    echo "性能测试"
    echo "========================================"
    
    log_info "执行性能测试..."
    
    # 测试API响应时间
    url="${BASE_URL}${API_PREFIX}/list?accountId=${TEST_ACCOUNT_ID}&profileId=${TEST_PROFILE_ID}&pageNum=1&pageSize=100"
    
    log_info "测试大数据量查询性能..."
    start_time=$(date +%s%N)
    response=$(curl -s "$url")
    end_time=$(date +%s%N)
    
    duration=$(( (end_time - start_time) / 1000000 )) # 转换为毫秒
    
    if [ $duration -lt 1000 ]; then
        log_success "响应时间: ${duration}ms (良好)"
    elif [ $duration -lt 3000 ]; then
        log_warning "响应时间: ${duration}ms (一般)"
    else
        log_error "响应时间: ${duration}ms (较慢)"
    fi
    
    echo "========================================"
}

# 帮助函数
show_help() {
    echo "搜索词分析功能测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示帮助信息"
    echo "  -a, --api       只测试API接口"
    echo "  -d, --database  只测试数据库"
    echo "  -p, --performance 只测试性能"
    echo "  --all           测试所有功能（默认）"
    echo ""
    echo "示例:"
    echo "  $0                # 测试所有功能"
    echo "  $0 --api          # 只测试API接口"
    echo "  $0 --database     # 只测试数据库"
    echo "  $0 --performance  # 只测试性能"
}

# 参数处理
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    -a|--api)
        main
        ;;
    -d|--database)
        test_database
        ;;
    -p|--performance)
        test_performance
        ;;
    --all|"")
        main
        test_database
        test_performance
        ;;
    *)
        echo "未知选项: $1"
        show_help
        exit 1
        ;;
esac
