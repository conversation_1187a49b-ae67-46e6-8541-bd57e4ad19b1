-- 搜索词分析功能测试数据插入脚本
-- 用于快速插入测试数据，验证功能是否正常

-- 清理现有测试数据（可选）
DELETE FROM ads_search_term_analytics WHERE account_id = 'TEST_ACCOUNT';
DELETE FROM ads_search_term_category_relation WHERE account_id = 'TEST_ACCOUNT';
DELETE FROM ads_search_term_product_ranking WHERE account_id = 'TEST_ACCOUNT';

-- 1. 插入搜索词分析测试数据
INSERT INTO ads_search_term_analytics (
    account_id, profile_id, marketplace_id, report_date, search_term,
    search_rank, monthly_search_volume, previous_month_search_volume, search_volume_growth_rate,
    clicks, impressions, click_through_rate, conversion_rate, average_cost_per_click,
    search_frequency_rank, competition_intensity, seasonality_index, relevance_score,
    data_source, status, create_by, create_time, update_time
) VALUES 
-- 热门搜索词
('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', '2025-01-31', 'wireless headphones', 
 3, 85000, 78000, 8.97, 2500, 35000, 7.14, 15.2, 2.50,
 1, 3, 1.2, 85.5, 'SP_API_SEARCH_QUERY_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', '2025-01-31', 'bluetooth speaker',
 5, 65000, 60000, 8.33, 1800, 28000, 6.43, 12.8, 1.80,
 1, 2, 1.1, 78.2, 'SP_API_SEARCH_QUERY_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', '2025-01-31', 'gaming mouse',
 8, 45000, 42000, 7.14, 1200, 18000, 6.67, 18.5, 3.20,
 2, 2, 0.9, 72.8, 'SP_API_SEARCH_QUERY_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', '2025-01-31', 'laptop stand',
 12, 25000, 23000, 8.70, 800, 12000, 6.67, 14.2, 1.50,
 2, 1, 1.0, 68.5, 'SP_API_SEARCH_QUERY_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', '2025-01-31', 'phone case',
 15, 35000, 32000, 9.38, 1500, 22000, 6.82, 11.5, 0.80,
 1, 3, 1.3, 75.2, 'SP_API_SEARCH_QUERY_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW());

-- 2. 插入搜索词与类目关联测试数据
INSERT INTO ads_search_term_category_relation (
    account_id, profile_id, marketplace_id, search_term, category_id, category_name,
    parent_category_id, category_level, category_path, clicks_in_category, impressions_in_category,
    conversions_in_category, sales_in_category, click_rank, relevance_score, market_share,
    competing_products_count, average_product_price, is_primary_category, statistics_date,
    data_source, status, create_by, create_time, update_time
) VALUES 
-- wireless headphones 的类目关联
('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'wireless headphones', 1001, 'Electronics > Audio > Headphones',
 100, 3, 'Electronics > Audio > Headphones', 1800, 25000, 450, 112500.00, 1, 88.5, 45.2,
 156, 125.50, 1, '2025-01-31', 'SP_API_SEARCH_CATALOG_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'wireless headphones', 1002, 'Electronics > Audio > Earbuds',
 100, 3, 'Electronics > Audio > Earbuds', 500, 8000, 120, 24000.00, 2, 72.3, 28.8,
 89, 80.25, 1, '2025-01-31', 'SP_API_SEARCH_CATALOG_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'wireless headphones', 1003, 'Electronics > Audio > Gaming Headsets',
 100, 3, 'Electronics > Audio > Gaming Headsets', 200, 2000, 45, 9000.00, 3, 65.8, 26.0,
 45, 200.00, 1, '2025-01-31', 'SP_API_SEARCH_CATALOG_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW()),

-- bluetooth speaker 的类目关联
('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'bluetooth speaker', 2001, 'Electronics > Audio > Speakers',
 200, 3, 'Electronics > Audio > Speakers', 1200, 18000, 280, 42000.00, 1, 82.5, 52.3,
 78, 150.00, 1, '2025-01-31', 'SP_API_SEARCH_CATALOG_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'bluetooth speaker', 2002, 'Electronics > Audio > Portable Speakers',
 200, 3, 'Electronics > Audio > Portable Speakers', 400, 7000, 85, 12750.00, 2, 75.2, 32.1,
 45, 75.50, 1, '2025-01-31', 'SP_API_SEARCH_CATALOG_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'bluetooth speaker', 2003, 'Electronics > Audio > Smart Speakers',
 200, 3, 'Electronics > Audio > Smart Speakers', 200, 3000, 40, 8000.00, 3, 68.9, 15.6,
 25, 200.00, 1, '2025-01-31', 'SP_API_SEARCH_CATALOG_PERFORMANCE', 'ACTIVE', 'test_user', NOW(), NOW());

-- 3. 插入搜索词商品排名测试数据
INSERT INTO ads_search_term_product_ranking (
    account_id, profile_id, marketplace_id, search_term, asin, product_title,
    main_image_url, thumbnail_image_url, product_url, brand, price, currency,
    rating, review_count, category_id, category_name, clicks, impressions,
    conversions, sales, click_rank, conversion_rate, click_through_rate,
    average_cost_per_click, competitiveness_score, average_position, stock_status,
    is_amazons_choice, is_best_seller, is_prime, seller_type, statistics_date,
    data_source, status, create_by, create_time, update_time
) VALUES 
-- wireless headphones 的商品排名
('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'wireless headphones', 'B08WH001', 'Sony WH-1000XM4 Wireless Noise Canceling Headphones',
 'https://example.com/sony-wh1000xm4.jpg', 'https://example.com/sony-wh1000xm4-thumb.jpg', 
 'https://amazon.com/dp/B08WH001', 'Sony', 349.99, 'USD', 4.6, 15420, 1001, 'Electronics > Audio > Headphones',
 850, 12000, 180, 62997.20, 1, 21.18, 7.08, 2.85, 92.5, 1.2, 'IN_STOCK',
 1, 1, 1, 'FBA', '2025-01-31', 'SP_API_CATALOG_ITEMS', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'wireless headphones', 'B08WH002', 'Bose QuietComfort 45 Wireless Headphones',
 'https://example.com/bose-qc45.jpg', 'https://example.com/bose-qc45-thumb.jpg',
 'https://amazon.com/dp/B08WH002', 'Bose', 329.00, 'USD', 4.4, 8950, 1001, 'Electronics > Audio > Headphones',
 650, 9500, 125, 41125.00, 2, 19.23, 6.84, 3.10, 88.2, 1.8, 'IN_STOCK',
 0, 1, 1, 'FBA', '2025-01-31', 'SP_API_CATALOG_ITEMS', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'wireless headphones', 'B08WH003', 'Apple AirPods Max Wireless Headphones',
 'https://example.com/airpods-max.jpg', 'https://example.com/airpods-max-thumb.jpg',
 'https://amazon.com/dp/B08WH003', 'Apple', 549.00, 'USD', 4.3, 5680, 1001, 'Electronics > Audio > Headphones',
 420, 6800, 85, 46665.00, 3, 20.24, 6.18, 4.20, 85.8, 2.5, 'IN_STOCK',
 1, 0, 1, 'AMAZON', '2025-01-31', 'SP_API_CATALOG_ITEMS', 'ACTIVE', 'test_user', NOW(), NOW()),

-- bluetooth speaker 的商品排名
('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'bluetooth speaker', 'B08BS001', 'JBL Charge 5 Portable Bluetooth Speaker',
 'https://example.com/jbl-charge5.jpg', 'https://example.com/jbl-charge5-thumb.jpg',
 'https://amazon.com/dp/B08BS001', 'JBL', 179.95, 'USD', 4.7, 12350, 2001, 'Electronics > Audio > Speakers',
 680, 10500, 145, 26092.75, 1, 21.32, 6.48, 1.85, 89.5, 1.5, 'IN_STOCK',
 1, 1, 1, 'FBA', '2025-01-31', 'SP_API_CATALOG_ITEMS', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'bluetooth speaker', 'B08BS002', 'Ultimate Ears BOOM 3 Portable Speaker',
 'https://example.com/ue-boom3.jpg', 'https://example.com/ue-boom3-thumb.jpg',
 'https://amazon.com/dp/B08BS002', 'Ultimate Ears', 149.99, 'USD', 4.5, 8920, 2002, 'Electronics > Audio > Portable Speakers',
 520, 8200, 110, 16498.90, 2, 21.15, 6.34, 1.65, 86.2, 2.1, 'IN_STOCK',
 0, 1, 1, 'FBA', '2025-01-31', 'SP_API_CATALOG_ITEMS', 'ACTIVE', 'test_user', NOW(), NOW()),

('TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'bluetooth speaker', 'B08BS003', 'Anker Soundcore 2 Portable Speaker',
 'https://example.com/anker-soundcore2.jpg', 'https://example.com/anker-soundcore2-thumb.jpg',
 'https://amazon.com/dp/B08BS003', 'Anker', 39.99, 'USD', 4.6, 25680, 2002, 'Electronics > Audio > Portable Speakers',
 380, 6500, 95, 3799.05, 3, 25.00, 5.85, 0.95, 82.8, 2.8, 'IN_STOCK',
 1, 1, 1, 'FBA', '2025-01-31', 'SP_API_CATALOG_ITEMS', 'ACTIVE', 'test_user', NOW(), NOW());

-- 验证插入的数据
SELECT '=== 搜索词分析数据 ===' as info;
SELECT account_id, search_term, monthly_search_volume, clicks, search_rank 
FROM ads_search_term_analytics 
WHERE account_id = 'TEST_ACCOUNT' 
ORDER BY monthly_search_volume DESC;

SELECT '=== 类目关联数据 ===' as info;
SELECT account_id, search_term, category_name, clicks_in_category, click_rank 
FROM ads_search_term_category_relation 
WHERE account_id = 'TEST_ACCOUNT' 
ORDER BY search_term, click_rank;

SELECT '=== 商品排名数据 ===' as info;
SELECT account_id, search_term, brand, product_title, clicks, click_rank 
FROM ads_search_term_product_ranking 
WHERE account_id = 'TEST_ACCOUNT' 
ORDER BY search_term, click_rank;

-- 测试查询性能
SELECT '=== 查询性能测试 ===' as info;
EXPLAIN SELECT 
    a.search_term,
    a.monthly_search_volume,
    a.clicks as total_clicks,
    c.category_name,
    c.clicks_in_category,
    p.product_title,
    p.brand,
    p.clicks as product_clicks
FROM ads_search_term_analytics a
LEFT JOIN ads_search_term_category_relation c ON a.search_term = c.search_term AND c.click_rank = 1
LEFT JOIN ads_search_term_product_ranking p ON a.search_term = p.search_term AND p.click_rank = 1
WHERE a.account_id = 'TEST_ACCOUNT'
ORDER BY a.monthly_search_volume DESC;
