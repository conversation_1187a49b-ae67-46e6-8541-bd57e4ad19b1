<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds" debug="false">
    <contextName>ads-spb</contextName>
    <property name="log.charset" value="utf-8" />
    <property name="log.pattern" value="%contextName- %red(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36}) - %msg%n" />

    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${log.charset}</charset>
        </encoder>
    </appender>

    <!--普通日志info级别监控并输出到控制台-->
    <root level="info">
        <appender-ref ref="console" />
    </root>

    <!--ERROR级别监控SQL语句 -->
    <logger name="jdbc.sqlonly" level="ERROR" additivity="false">
        <appender-ref ref="console" />
    </logger>

    <!--ERROR级别监控与JDBC结果集相关的日志-->
    <logger name="jdbc.resultset" level="ERROR" additivity="false">
        <appender-ref ref="console" />
    </logger>

    <!--禁止记录与JDBC结果集以表格形式表示的日志-->
    <logger name="jdbc.resultsettable" level="OFF" additivity="false">
        <appender-ref ref="console" />
    </logger>

    <!--禁止记录与JDBC连接相关的日志-->
    <logger name="jdbc.connection" level="OFF" additivity="false">
        <appender-ref ref="console" />
    </logger>

    <!--禁止记录与SQL执行时间相关的日志-->
    <logger name="jdbc.sqltiming" level="OFF" additivity="false">
        <appender-ref ref="console" />
    </logger>

    <!--禁止记录与JDBC操作的审核日志-->
    <logger name="jdbc.audit" level="OFF" additivity="false">
        <appender-ref ref="console" />
    </logger>

</configuration>