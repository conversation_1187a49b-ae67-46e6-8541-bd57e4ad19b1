-- 搜索词分析相关数据表创建脚本
-- 包括搜索词分析、类目关联、商品排名等表结构
-- 支持分表策略，按月分表以处理大数据量

-- 1. 搜索词分析数据表
CREATE TABLE IF NOT EXISTS `ads_search_term_analytics` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `account_id` VARCHAR(50) NOT NULL COMMENT '账户ID',
    `profile_id` BIGINT(20) NOT NULL COMMENT '配置文件ID',
    `marketplace_id` VARCHAR(20) NOT NULL COMMENT '市场ID',
    `report_date` DATE NOT NULL COMMENT '报告日期',
    `search_term` VARCHAR(500) NOT NULL COMMENT '搜索词',
    `search_rank` INT(11) DEFAULT NULL COMMENT '搜索词排名',
    `monthly_search_volume` BIGINT(20) DEFAULT NULL COMMENT '月搜索量',
    `previous_month_search_volume` BIGINT(20) DEFAULT NULL COMMENT '上月搜索量',
    `search_volume_growth_rate` DECIMAL(10,2) DEFAULT NULL COMMENT '月搜索量环比增长率(%)',
    `clicks` INT(11) DEFAULT 0 COMMENT '点击量',
    `impressions` INT(11) DEFAULT 0 COMMENT '展示量',
    `click_through_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '点击率(%)',
    `conversion_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '转化率(%)',
    `average_cost_per_click` DECIMAL(10,2) DEFAULT NULL COMMENT '平均点击成本',
    `search_frequency_rank` TINYINT(4) DEFAULT NULL COMMENT '搜索频率排名(1-高频,2-中频,3-低频)',
    `competition_intensity` TINYINT(4) DEFAULT NULL COMMENT '竞争强度(1-低,2-中,3-高)',
    `seasonality_index` DECIMAL(10,2) DEFAULT NULL COMMENT '季节性指数',
    `relevance_score` DECIMAL(10,2) DEFAULT NULL COMMENT '相关性得分',
    `data_source` VARCHAR(100) DEFAULT NULL COMMENT '数据来源',
    `status` VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态(ACTIVE-有效,INACTIVE-无效)',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新者',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_search_term_date` (`account_id`, `profile_id`, `search_term`(100), `report_date`),
    KEY `idx_account_profile` (`account_id`, `profile_id`),
    KEY `idx_search_term` (`search_term`(100)),
    KEY `idx_report_date` (`report_date`),
    KEY `idx_search_volume` (`monthly_search_volume`),
    KEY `idx_growth_rate` (`search_volume_growth_rate`),
    KEY `idx_competition` (`competition_intensity`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索词分析数据表';

-- 2. 搜索词与商品类目关联表
CREATE TABLE IF NOT EXISTS `ads_search_term_category_relation` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `account_id` VARCHAR(50) NOT NULL COMMENT '账户ID',
    `profile_id` BIGINT(20) NOT NULL COMMENT '配置文件ID',
    `marketplace_id` VARCHAR(20) NOT NULL COMMENT '市场ID',
    `search_term` VARCHAR(500) NOT NULL COMMENT '搜索词',
    `category_id` BIGINT(20) NOT NULL COMMENT '类目ID',
    `category_name` VARCHAR(200) DEFAULT NULL COMMENT '类目名称',
    `parent_category_id` BIGINT(20) DEFAULT NULL COMMENT '父类目ID',
    `category_level` TINYINT(4) DEFAULT NULL COMMENT '类目层级',
    `category_path` VARCHAR(1000) DEFAULT NULL COMMENT '类目路径',
    `clicks_in_category` INT(11) DEFAULT 0 COMMENT '该搜索词在此类目下的点击量',
    `impressions_in_category` INT(11) DEFAULT 0 COMMENT '该搜索词在此类目下的展示量',
    `conversions_in_category` INT(11) DEFAULT 0 COMMENT '该搜索词在此类目下的转化量',
    `sales_in_category` DECIMAL(15,2) DEFAULT 0.00 COMMENT '该搜索词在此类目下的销售额',
    `click_rank` TINYINT(4) DEFAULT NULL COMMENT '点击量排名(1-最高)',
    `relevance_score` DECIMAL(10,2) DEFAULT NULL COMMENT '关联强度得分(0-100)',
    `market_share` DECIMAL(10,2) DEFAULT NULL COMMENT '类目在该搜索词下的市场份额(%)',
    `competing_products_count` INT(11) DEFAULT 0 COMMENT '竞争商品数量',
    `average_product_price` DECIMAL(15,2) DEFAULT NULL COMMENT '平均商品价格',
    `is_primary_category` TINYINT(1) DEFAULT 0 COMMENT '是否为主要类目(1-是,0-否)',
    `statistics_date` DATE DEFAULT NULL COMMENT '数据统计日期',
    `data_source` VARCHAR(100) DEFAULT NULL COMMENT '数据来源',
    `status` VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新者',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_search_term_category` (`account_id`, `profile_id`, `search_term`(100), `category_id`),
    KEY `idx_account_profile` (`account_id`, `profile_id`),
    KEY `idx_search_term` (`search_term`(100)),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_click_rank` (`click_rank`),
    KEY `idx_clicks_in_category` (`clicks_in_category`),
    KEY `idx_relevance_score` (`relevance_score`),
    KEY `idx_is_primary` (`is_primary_category`),
    KEY `idx_statistics_date` (`statistics_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索词与商品类目关联表';

-- 3. 搜索词商品点击排名表
CREATE TABLE IF NOT EXISTS `ads_search_term_product_ranking` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `account_id` VARCHAR(50) NOT NULL COMMENT '账户ID',
    `profile_id` BIGINT(20) NOT NULL COMMENT '配置文件ID',
    `marketplace_id` VARCHAR(20) NOT NULL COMMENT '市场ID',
    `search_term` VARCHAR(500) NOT NULL COMMENT '搜索词',
    `asin` VARCHAR(20) NOT NULL COMMENT '商品ASIN',
    `product_title` VARCHAR(1000) DEFAULT NULL COMMENT '商品标题',
    `main_image_url` VARCHAR(500) DEFAULT NULL COMMENT '商品主图URL',
    `thumbnail_image_url` VARCHAR(500) DEFAULT NULL COMMENT '商品缩略图URL',
    `product_url` VARCHAR(500) DEFAULT NULL COMMENT '商品详情页URL',
    `brand` VARCHAR(200) DEFAULT NULL COMMENT '商品品牌',
    `price` DECIMAL(15,2) DEFAULT NULL COMMENT '商品价格',
    `currency` VARCHAR(10) DEFAULT NULL COMMENT '货币单位',
    `rating` DECIMAL(3,2) DEFAULT NULL COMMENT '商品评分',
    `review_count` INT(11) DEFAULT 0 COMMENT '评论数量',
    `category_id` BIGINT(20) DEFAULT NULL COMMENT '商品类目ID',
    `category_name` VARCHAR(200) DEFAULT NULL COMMENT '商品类目名称',
    `clicks` INT(11) DEFAULT 0 COMMENT '该搜索词下的点击量',
    `impressions` INT(11) DEFAULT 0 COMMENT '该搜索词下的展示量',
    `conversions` INT(11) DEFAULT 0 COMMENT '该搜索词下的转化量',
    `sales` DECIMAL(15,2) DEFAULT 0.00 COMMENT '该搜索词下的销售额',
    `click_rank` TINYINT(4) DEFAULT NULL COMMENT '点击量排名(1-最高)',
    `conversion_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '转化率(%)',
    `click_through_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '点击率(%)',
    `average_cost_per_click` DECIMAL(10,2) DEFAULT NULL COMMENT '平均点击成本',
    `competitiveness_score` DECIMAL(10,2) DEFAULT NULL COMMENT '商品竞争力得分(0-100)',
    `average_position` DECIMAL(10,2) DEFAULT NULL COMMENT '商品在搜索结果中的平均位置',
    `stock_status` VARCHAR(20) DEFAULT NULL COMMENT '库存状态',
    `is_amazons_choice` TINYINT(1) DEFAULT 0 COMMENT '是否为Amazon Choice(1-是,0-否)',
    `is_best_seller` TINYINT(1) DEFAULT 0 COMMENT '是否为Best Seller(1-是,0-否)',
    `is_prime` TINYINT(1) DEFAULT 0 COMMENT '是否为Prime商品(1-是,0-否)',
    `seller_type` VARCHAR(20) DEFAULT NULL COMMENT '卖家类型(FBA,FBM,AMAZON)',
    `statistics_date` DATE DEFAULT NULL COMMENT '数据统计日期',
    `data_source` VARCHAR(100) DEFAULT NULL COMMENT '数据来源',
    `status` VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新者',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_search_term_asin` (`account_id`, `profile_id`, `search_term`(100), `asin`),
    KEY `idx_account_profile` (`account_id`, `profile_id`),
    KEY `idx_search_term` (`search_term`(100)),
    KEY `idx_asin` (`asin`),
    KEY `idx_click_rank` (`click_rank`),
    KEY `idx_clicks` (`clicks`),
    KEY `idx_brand` (`brand`(50)),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_competitiveness_score` (`competitiveness_score`),
    KEY `idx_statistics_date` (`statistics_date`),
    KEY `idx_rating` (`rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索词商品点击排名表';

-- 4. 创建分表示例（按月分表）
-- 搜索词分析数据表 - 2025年1月
CREATE TABLE IF NOT EXISTS `ads_search_term_analytics_202501` LIKE `ads_search_term_analytics`;

-- 搜索词类目关联表 - 2025年1月
CREATE TABLE IF NOT EXISTS `ads_search_term_category_relation_202501` LIKE `ads_search_term_category_relation`;

-- 搜索词商品排名表 - 2025年1月
CREATE TABLE IF NOT EXISTS `ads_search_term_product_ranking_202501` LIKE `ads_search_term_product_ranking`;

-- 5. 创建视图用于跨表查询
CREATE OR REPLACE VIEW `v_search_term_analytics_current_month` AS
SELECT * FROM `ads_search_term_analytics_202501`
WHERE `report_date` >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
  AND `report_date` < DATE_ADD(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 1 MONTH);

-- 6. 创建存储过程用于自动创建分表
DELIMITER $$

CREATE PROCEDURE `CreateMonthlyTables`(IN table_prefix VARCHAR(100), IN target_date DATE)
BEGIN
    DECLARE table_suffix VARCHAR(10);
    DECLARE new_table_name VARCHAR(150);
    DECLARE sql_stmt TEXT;
    
    -- 生成表名后缀 (YYYYMM格式)
    SET table_suffix = DATE_FORMAT(target_date, '%Y%m');
    SET new_table_name = CONCAT(table_prefix, '_', table_suffix);
    
    -- 检查表是否已存在
    SET @table_exists = 0;
    SELECT COUNT(*) INTO @table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
      AND table_name = new_table_name;
    
    -- 如果表不存在则创建
    IF @table_exists = 0 THEN
        SET sql_stmt = CONCAT('CREATE TABLE IF NOT EXISTS `', new_table_name, '` LIKE `', table_prefix, '`');
        SET @sql = sql_stmt;
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        SELECT CONCAT('Table created: ', new_table_name) AS result;
    ELSE
        SELECT CONCAT('Table already exists: ', new_table_name) AS result;
    END IF;
END$$

DELIMITER ;

-- 7. 创建索引优化查询性能
-- 为主表添加复合索引
ALTER TABLE `ads_search_term_analytics`
ADD INDEX `idx_search_term_date_volume` (`search_term`(100), `report_date`, `monthly_search_volume`);

ALTER TABLE `ads_search_term_category_relation`
ADD INDEX `idx_search_term_click_rank` (`search_term`(100), `click_rank`, `clicks_in_category`);

ALTER TABLE `ads_search_term_product_ranking`
ADD INDEX `idx_search_term_click_rank` (`search_term`(100), `click_rank`, `clicks`);

-- 8. 创建定时清理过期数据的事件
-- 注意：需要开启事件调度器 SET GLOBAL event_scheduler = ON;
CREATE EVENT IF NOT EXISTS `cleanup_expired_search_term_data`
ON SCHEDULE EVERY 1 WEEK
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    -- 删除90天前的数据
    DELETE FROM `ads_search_term_analytics` 
    WHERE `report_date` < DATE_SUB(CURDATE(), INTERVAL 90 DAY);
    
    DELETE FROM `ads_search_term_category_relation` 
    WHERE `statistics_date` < DATE_SUB(CURDATE(), INTERVAL 90 DAY);
    
    DELETE FROM `ads_search_term_product_ranking` 
    WHERE `statistics_date` < DATE_SUB(CURDATE(), INTERVAL 90 DAY);
END;
