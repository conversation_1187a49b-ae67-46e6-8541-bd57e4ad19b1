### \u914D\u7F6E\u4E2D\u5FC3\u5730\u5740
#spring.cloud.nacos.config.server-addr=${yiyitech.config.server-addr}
### \u914D\u7F6E\u4E2D\u5FC3\u7528\u6237\u540D
#spring.cloud.nacos.config.username=${yiyitech.config.username}
### \u914D\u7F6E\u4E2D\u5FC3\u5BC6\u7801
#spring.cloud.nacos.config.password=${yiyitech.config.password}
## \u670D\u52A1\u4FE1\u606F\u914D\u7F6E
#spring.application.name=yiyitech-duck-bff-ads
#spring.cloud.nacos.config.prefix=${spring.application.name}
#spring.cloud.nacos.config.file-extension=properties
#spring.cloud.nacos.config.enabled=true
#spring.cloud.nacos.config.refresh-enabled=true
### \u547D\u540D\u7A7A\u95F4
#spring.cloud.nacos.config.namespace=${yiyitech.config.namespace}
### \u73AF\u5883
#spring.cloud.nacos.config.group=${yiyitech.config.group}
### \u5171\u4EAB\u914D\u7F6E\u6587\u4EF6\u914D\u7F6E
#spring.cloud.nacos.config.shared-configs[0].data-id=register.properties
#spring.cloud.nacos.config.shared-configs[0].group=${yiyitech.config.group}
#spring.cloud.nacos.config.shared-configs[0].refresh=true
#spring.cloud.nacos.config.shared-configs[1].data-id=log.properties
#spring.cloud.nacos.config.shared-configs[1].group=${yiyitech.config.group}
#spring.cloud.nacos.config.shared-configs[1].refresh=true
#spring.cloud.nacos.config.shared-configs[2].data-id=datasource.properties
#spring.cloud.nacos.config.shared-configs[2].group=${yiyitech.config.group}
#spring.cloud.nacos.config.shared-configs[2].refresh=true
#spring.cloud.nacos.config.shared-configs[3].data-id=redis.properties
#spring.cloud.nacos.config.shared-configs[3].group=${yiyitech.config.group}
#spring.cloud.nacos.config.shared-configs[3].refresh=true
#spring.servlet.multipart.max-file-size=3MB