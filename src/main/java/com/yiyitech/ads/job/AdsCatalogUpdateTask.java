package com.yiyitech.ads.job;

import cn.hutool.core.date.DateUtil;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.mapper.AdsDailyAdvertiserReportMapper;
import com.yiyitech.ads.model.AdsDailyAdvertiserReportModel;
import com.yiyitech.ads.model.response.SpCatalogItemResponse;
import com.yiyitech.ads.service.apisp.SpServicesApi;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.support.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsDailyAdvertiserTask.java
 * @Description
 * @createTime 2025年07月07日 17:05:00
 */
@Slf4j
@Component
public class AdsCatalogUpdateTask {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private AdsDailyAdvertiserReportMapper advertiserReportMapper;
    @Autowired
    private SpServicesApi spServicesApi;

    @Scheduled(cron = "${job.corn.daily_advertiser}")
    public void updateAsinCatalogInfoTask() {
        //获取今天日期
//        String reportDate = DateUtil.today();
        //获取昨天日期
//        String reportDate = DateUtils.format(DateUtil.yesterday(), "yyyy-MM-dd");
        //获取前天日期
        String reportDate = DateUtils.format(DateUtil.offsetDay(DateUtil.date(), -1), "yyyy-MM-dd");
        updateAsinCatalogInfo("ATVPDKIKX0DER", reportDate, 60);
    }


    private static final String LOCK_KEY = "updateAsinCatalogLock";
    private static final Duration LOCK_EXPIRE_TIME = Duration.ofMinutes(10);
    private static final long LOCK_RENEW_INTERVAL = 5;
    private static final int MAX_CONCURRENT_REQUESTS = 2;
    private static final Semaphore SEMAPHORE = new Semaphore(MAX_CONCURRENT_REQUESTS);

    public void updateAsinCatalogInfo(String marketplaceId, String endDate, Integer countingBack) {
        String startDate = LocalDate.parse(endDate).minusDays(countingBack - 1).toString();
        //分布锁，防止重复执行
        ValueOperations<String, String> ops = redisTemplate.opsForValue();
        Boolean locked = ops.setIfAbsent(LOCK_KEY, "locked");
        if (Boolean.FALSE.equals(locked)) {
            log.info("已有任务在执行，跳过本次");
            return;
        }
        //设置过期时间
        redisTemplate.expire(LOCK_KEY, LOCK_EXPIRE_TIME.getSeconds(), TimeUnit.SECONDS);
        //锁续期线程(每5分钟续期一次)
        ScheduledExecutorService lockRenewScheduler = Executors.newScheduledThreadPool(1);
        Runnable renewTask = () -> {
            //检查锁是否仍然存在且获取锁的当前值，确保是本实例设置的锁
            String lockValue = ops.get(LOCK_KEY);
            if ("locked".equals(lockValue)) {
                redisTemplate.expire(LOCK_KEY, LOCK_EXPIRE_TIME.getSeconds(), TimeUnit.SECONDS);
                log.info("锁续期成功...");
            } else {
                log.warn("锁已被释放，停止续期");
                //释放定时任务线程池
                lockRenewScheduler.shutdown();
            }
        };
        lockRenewScheduler.scheduleAtFixedRate(renewTask, LOCK_RENEW_INTERVAL, LOCK_RENEW_INTERVAL, TimeUnit.MINUTES);

        //主任务执行
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(10);
        //查询今日需要更新的产品
        List<AdsDailyAdvertiserReportModel> adverTiserModelLst = advertiserReportMapper.searchNeedUpdateByDate(startDate, endDate);
        if (null == adverTiserModelLst || adverTiserModelLst.size() <= 0) {
            log.info("暂无需要更新的ASIN");
            releaseLock(lockRenewScheduler, scheduler);
            return;
        }

        log.info("准备并发更新 {} 个 ASIN 的 Catalog 信息", adverTiserModelLst.size());
        AtomicInteger index = new AtomicInteger(0);
        List<ScheduledFuture<?>> futures = new ArrayList<>();

        for (AdsDailyAdvertiserReportModel advertiser : adverTiserModelLst) {
            String spAccessToken = redisUtils.getAccountAccessTokenAndRefreshToken(advertiser.getAccountId(), CommonConstants.SP_PREFIX_KEY);
            ScheduledFuture<?> future = scheduler.schedule(() -> {
                processUpdateAsinCatalog(advertiser, marketplaceId, spAccessToken);
            }, index.getAndIncrement() * 500, TimeUnit.MILLISECONDS);
            futures.add(future);
        }

        //等待全部完成
        for (ScheduledFuture<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("ASIN 更新任务异常: {}", e.getMessage(), e);
            }
        }
        releaseLock(lockRenewScheduler, scheduler);
        log.info("Catalog ASIN 更新任务完成，共处理：{}", adverTiserModelLst.size());
    }

    private void processUpdateAsinCatalog(AdsDailyAdvertiserReportModel advertiser, String marketplaceId, String spAccessToken) {
        try {
            SEMAPHORE.acquire();
            String asin = advertiser.getAdvertisedAsin();
            int retry = 0;
            while (retry < 2) {
                try {
                    //获取asin的图片以及父asin信息
                    SpCatalogItemResponse response = spServicesApi.getSpCatalogItem(asin, marketplaceId, spAccessToken);
                    String mainImageUrl = response.getMainImageUrl();
                    String parentAsin = response.getParentAsin();
                    String itemName = response.getTitle();
                    if (parentAsin != null || mainImageUrl != null || itemName != null) {
                        //保存asin对应的图片url以及其父asin
                        advertiser.setAdvertisedParentAsin(parentAsin);
                        advertiser.setPictureUrl(mainImageUrl);
                        advertiser.setAdvertisedItemName(itemName);
                        advertiserReportMapper.updateById(advertiser);
                        log.info("ASIN [{}] 更新成功", asin);
                    } else {
                        log.warn("ASIN [{}] 获取信息为空", asin);
                    }
                    return;
                } catch (Exception e) {
                    retry++;
                    log.warn("第 {} 次尝试 ASIN [{}] 失败: {}", retry, asin, e.getMessage());
                    Thread.sleep(2000);
                }
            }
            log.error("ASIN [{}] 最终失败", asin);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            //释放信号，允许新的请求进入
            SEMAPHORE.release();
        }
    }

    private void releaseLock(ScheduledExecutorService lockRenewScheduler, ExecutorService taskScheduler) {
        redisTemplate.delete(LOCK_KEY);
        lockRenewScheduler.shutdownNow();
        taskScheduler.shutdown();
        log.info("任务锁已释放，线程池关闭");
    }

}
