package com.yiyitech.ads.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yiyitech.ads.constants.AdsReportConstants;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.mapper.AdsCreateReportMapper;
import com.yiyitech.ads.mapper.AdsDailyAdvertiserReportMapper;
import com.yiyitech.ads.mapper.AdsDailySearchTermReportMapper;
import com.yiyitech.ads.mapper.AdsDailyTargetingReportMapper;
import com.yiyitech.ads.model.AdsCreateReportModel;
import com.yiyitech.ads.model.AdsDailyAdvertiserReportModel;
import com.yiyitech.ads.model.AdsDailySearchTermReportModel;
import com.yiyitech.ads.model.AdsDailyTargetingReportModel;
import com.yiyitech.ads.model.response.AdsReportsResponse;
import com.yiyitech.ads.service.api.AdsReportsAPi;
import com.yiyitech.ads.util.FileUtil;
import com.yiyitech.ads.util.OssUtil;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.support.exception.BusinessException;
import com.yiyitech.support.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsGetReportTask.java
 * @Description
 * @createTime 2023年12月22日 13:31:00
 */
@Slf4j
@Component
public class AdsGetReportTask {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private OssUtil ossUtil;
    @Autowired
    private FileUtil fileUtil;
    @Autowired
    private AdsReportsAPi adsReportsAPi;
    @Autowired
    private AdsCreateReportMapper adsCreateReportMapper;
    @Autowired
    private AdsDailySearchTermReportMapper dailySearchTermReportMapper;
    @Autowired
    private AdsDailyTargetingReportMapper dailyTargetingReportMapper;
    @Autowired
    private AdsDailyAdvertiserReportMapper dailyAdvertiserReportMapper;

    @Scheduled(cron = "${job.corn.get_report}")
    public void getReportTask(){
        //获取今天日期
//        String reportDate = DateUtil.today();
        //获取昨天日期
//        String reportDate = DateUtils.format(DateUtil.yesterday(), "yyyy-MM-dd");
        //获取前天日期
        String reportDate = DateUtils.format(DateUtil.offsetDay(DateUtil.date(), -1), "yyyy-MM-dd");
        getReport(reportDate, 60);
    }

//    public void getReport(String reportDate) {
//        log.info(reportDate + "日获取报告任务开始");
//        //查询所有创建的报告(状态为生成中的)
//        List<AdsCreateReportModel> createReportLst = adsCreateReportMapper.searchByStatusByDate(AdsReportConstants.PENDING, reportDate);
//        //如果报告创建完成，获取每份报告的数据
//        String accessToken = "";
//        List<AdsDailySearchTermReportModel> batchSearchTermReportLst = new ArrayList<>();
//        List<AdsDailyTargetingReportModel> batchTargetingReportLst = new ArrayList<>();
//        List<AdsDailyAdvertiserReportModel> batchAdvertiserReportLst = new ArrayList<>();
//        ossUtil.openOss();//（后续需要多线程时，考虑开启位置）
//        for (AdsCreateReportModel report : createReportLst) {
//            String jsonContent = null;
//            accessToken = redisUtils.getAccountAccessTokenAndRefreshToken(report.getAccoutnId(), CommonConstants.ADS_PREFIX_KEY);
//            AdsReportsResponse apiResponse = adsReportsAPi.getReport(accessToken, report.getProfileId(), report.getReportId());
//            switch (apiResponse.getStatus()) {
//                case AdsReportConstants.PENDING:
//                    continue;
//                case AdsReportConstants.FAILED:
//                    report.setStatus(AdsReportConstants.FAILED)
//                          .setFailureReason(apiResponse.getFailureReason());
//                    break;
//                case AdsReportConstants.COMPLETED:
//                    try {
//                        jsonContent = downLoadAndReadReport(report.getReportId(), apiResponse.getUrl(), "", "");
//                    } catch (Exception e) {
//                        log.info("文件：" + report.getReportId() + " 下载异常: " + e.getMessage());
//                        continue;
//                    }
//                    break;
//                default:
//                    report.setStatus(AdsReportConstants.UNKNOWN_ERROR);
//                    break;
//            }
//            if (jsonContent != null) {
//                report.setStatus(AdsReportConstants.COMPLETED);
//                if (jsonContent.length() > 2) {
//                    JSONArray.parseArray(jsonContent).forEach(obj -> {
//                        JSONObject jsonObj = (JSONObject) obj;
//                        jsonObj.put("accountId", report.getAccoutnId());
//                        jsonObj.put("profileId", Long.valueOf(report.getProfileId()));
//                        switch (report.getReportTypeId()) {
//                            case AdsReportConstants.SP_SEARCH_TERM:
//                                batchSearchTermReportLst.add(jsonObj.toJavaObject(AdsDailySearchTermReportModel.class));
//                                break;
//                            case AdsReportConstants.SP_TARGETING:
//                                batchTargetingReportLst.add(jsonObj.toJavaObject(AdsDailyTargetingReportModel.class));
//                                break;
//                            case AdsReportConstants.SP_ADVERTISED:
//                                batchAdvertiserReportLst.add(jsonObj.toJavaObject(AdsDailyAdvertiserReportModel.class));
//                                break;
//                        }
//                    });
//                }
//            }
//            try {
//                Thread.sleep(5000);
//            } catch (InterruptedException e) {
//                log.error("站点：" + report.getProfileId() + "获取：" + report.getReportId() + "报告task异常：" + e.getMessage());
//                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
//            }
//        }
//
//        //所有报告数据要事务控制 TODO
//        //保存搜索词报告数据
//        dailySearchTermReportMapper.batchInsert(AdsDailySearchTermReportModel.class, batchSearchTermReportLst, 100);
//        //保存关键词报告数据
//        dailyTargetingReportMapper.batchInsert(AdsDailyTargetingReportModel.class, batchTargetingReportLst, 100);
//        //保存广告产品报告数据
//        dailyAdvertiserReportMapper.batchInsert(AdsDailyAdvertiserReportModel.class, batchAdvertiserReportLst, 100);
//        //更新创建的报告状态
//        adsCreateReportMapper.updateBatchById(AdsCreateReportModel.class, createReportLst, 100);
//        ossUtil.closeOss();
//        log.info(reportDate + "日获取报告任务结束");
//    }



    public void getReport(String reportDate, Integer countingBack) {
        log.info("{} 日获取报告任务开始（并发）", reportDate);
        LocalDate endDate = LocalDate.parse(reportDate);
        LocalDate startDate = endDate.minusDays(countingBack-1);
        final int BATCH_SIZE = 100;
        final int THREAD_COUNT = 6;

        List<AdsCreateReportModel> createReportLst = adsCreateReportMapper.searchByStatusBetweenDates(
                AdsReportConstants.PENDING, startDate.toString(), endDate.toString());

        Map<String, List<AdsCreateReportModel>> profileReportMap = createReportLst.stream()
                .collect(Collectors.groupingBy(AdsCreateReportModel::getProfileId));

        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        ossUtil.openOss();
        for (Map.Entry<String, List<AdsCreateReportModel>> entry : profileReportMap.entrySet()) {
            String profileId = entry.getKey();
            List<AdsCreateReportModel> reports = entry.getValue();

            executor.submit(() -> {
                log.info("开始处理 profileId={}, 待处理报告数={}", profileId, reports.size());
                Map<String, List<AdsDailySearchTermReportModel>> searchTermMap = new HashMap<>();
                Map<String, List<AdsDailyTargetingReportModel>> targetingMap = new HashMap<>();
                Map<String, List<AdsDailyAdvertiserReportModel>> advertiserMap = new HashMap<>();
                for (AdsCreateReportModel report : reports) {
                    String date = report.getJobDate();
                    String accountId = report.getAccoutnId();
                    String accessToken = redisUtils.getAccountAccessTokenAndRefreshToken(accountId, CommonConstants.ADS_PREFIX_KEY);
                    //加分布锁
                    String redisLockKey = CommonConstants.ADS_PREFIX_KEY.concat(CommonConstants.GETREPORT_LOCK)+report.getId();
                    String lockValue = redisUtils.tryLock(redisLockKey, 3, TimeUnit.MINUTES);
                    if (lockValue == null) {
                        log.warn("任务锁获取失败，跳过执行，reportId={}, profileId={}", report.getId(), profileId);
                        continue;
                    }
                    try {
                        log.debug("开始处理报告 reportId={}, jobDate={}, reportTypeId={}", report.getReportId(), report.getJobDate(), report.getReportTypeId());
                        AdsReportsResponse apiResponse = adsReportsAPi.getReport(accessToken, profileId, report.getReportId());
                        log.debug("报告状态获取完成，reportId={}, status={}", report.getReportId(), apiResponse.getStatus());
                        if (AdsReportConstants.PENDING.equals(apiResponse.getStatus())) {
                            continue;
                        }
                        if (AdsReportConstants.FAILED.equals(apiResponse.getStatus())) {
                            report.setStatus(AdsReportConstants.FAILED).setFailureReason(apiResponse.getFailureReason());
                            log.error("报告生成失败，reportId={}, reason={}", report.getReportId(), apiResponse.getFailureReason());
                            continue;
                        }
                        if (!AdsReportConstants.COMPLETED.equals(apiResponse.getStatus())) {
                            report.setStatus(AdsReportConstants.UNKNOWN_ERROR);
                            continue;
                        }

                        String jsonContent = downLoadAndReadReport(report.getReportId(), apiResponse.getUrl(), "", "");
                        if (StringUtils.isBlank(jsonContent)) continue;

                        JSONArray arr = JSONArray.parseArray(jsonContent);
                        log.info("报告解析完成，reportId={}, 数据条数={}", report.getReportId(), arr.size());
                        arr.forEach(obj -> {
                            JSONObject jsonObj = (JSONObject) obj;
                            jsonObj.put("accountId", accountId);
                            jsonObj.put("profileId", Long.valueOf(profileId));
                            jsonObj.put("date", date);
                            switch (report.getReportTypeId()) {
                                case AdsReportConstants.SP_SEARCH_TERM:
                                    AdsDailySearchTermReportModel stModel = jsonObj.toJavaObject(AdsDailySearchTermReportModel.class);
                                    searchTermMap.computeIfAbsent(date, k -> new ArrayList<>()).add(stModel);
                                    break;
                                case AdsReportConstants.SP_TARGETING:
                                    AdsDailyTargetingReportModel tgModel = jsonObj.toJavaObject(AdsDailyTargetingReportModel.class);
                                    targetingMap.computeIfAbsent(date, k -> new ArrayList<>()).add(tgModel);
                                    break;
                                case AdsReportConstants.SP_ADVERTISED:
                                    AdsDailyAdvertiserReportModel adModel = jsonObj.toJavaObject(AdsDailyAdvertiserReportModel.class);
                                    advertiserMap.computeIfAbsent(date, k -> new ArrayList<>()).add(adModel);
                                    break;
                            }
                        });
                        report.setStatus(AdsReportConstants.COMPLETED);
                        Thread.sleep(1500);
                    } catch (Exception e) {
                        log.error("拉取报告失败 [reportId={}, profileId={}, date={}]: {}", report.getReportId(), profileId, report.getJobDate(), e.getMessage());
                    } finally {
                        boolean released = redisUtils.releaseLock(redisLockKey, lockValue);
                        if (!released) {
                            log.warn("释放锁失败，锁key={}, 锁value={}", redisLockKey, lockValue);
                        }
                    }
                }
                //每日搜索词报告更新
                searchTermMap.forEach((date, list) -> {
                    int deleted = dailySearchTermReportMapper.deleteByProfileIdAndDate(profileId, date);
                    log.info("删除【搜索词】旧数据：profileId={}, date={}, type=searchTerm, 删除条数={}", profileId, date, deleted);
                    for (int i = 0; i < list.size(); i += BATCH_SIZE) {
                        List<AdsDailySearchTermReportModel> batch = list.subList(i, Math.min(i + BATCH_SIZE, list.size()));
                        dailySearchTermReportMapper.batchInsert(AdsDailySearchTermReportModel.class, batch, BATCH_SIZE);
                        log.debug("插入【搜索词】报告数据成功：profileId={}, date={}, type=searchTerm, 插入条数={}", profileId, date, batch.size());
                    }
                });
                //每日关键词报告更新
                targetingMap.forEach((date, list) -> {
                    int deleted = dailyTargetingReportMapper.deleteByProfileIdAndDate(profileId, date);
                    log.info("删除【关键词】旧数据：profileId={}, date={}, type=targeting, 删除条数={}", profileId, date, deleted);
                    for (int i = 0; i < list.size(); i += BATCH_SIZE) {
                        List<AdsDailyTargetingReportModel> batch = list.subList(i, Math.min(i + BATCH_SIZE, list.size()));
                        dailyTargetingReportMapper.batchInsert(AdsDailyTargetingReportModel.class, batch, BATCH_SIZE);
                        log.debug("插入【关键词】报告数据成功：profileId={}, date={}, type=targeting, 插入条数={}", profileId, date, batch.size());
                    }
                });
                //每日商品报告更新
                advertiserMap.forEach((date, list) -> {
                    int deleted = dailyAdvertiserReportMapper.deleteByProfileIdAndDate(profileId, date);
                    log.info("删除【商品】旧数据：profileId={}, date={}, type=advertiser, 删除条数={}", profileId, date, deleted);
                    for (int i = 0; i < list.size(); i += BATCH_SIZE) {
                        List<AdsDailyAdvertiserReportModel> batch = list.subList(i, Math.min(i + BATCH_SIZE, list.size()));
                        dailyAdvertiserReportMapper.batchInsert(AdsDailyAdvertiserReportModel.class, batch, BATCH_SIZE);
                        log.debug("插入【商品】报告数据成功：profileId={}, date={}, type=advertiser, 插入条数={}", profileId, date, batch.size());
                    }
                });

                adsCreateReportMapper.updateBatchById(AdsCreateReportModel.class, reports, BATCH_SIZE);
                log.info("线程任务完成：profileId={}, 共处理报告数={}", profileId, reports.size());
            });
        }
        log.info("{} 日获取报告任务提交完成，等待线程执行", reportDate);
        executor.shutdown();
        try {
            if (!executor.awaitTermination(1, TimeUnit.HOURS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        ossUtil.closeOss();
        log.info("{} 日获取报告任务结束", reportDate);
    }




    public String downLoadAndReadReport(String fileName, String sourceUrl, String gzPath, String jsonPath) throws Exception{
        gzPath = AdsReportConstants.TEMP_FILE_PATH.concat(fileName).concat(AdsReportConstants.GZ);
        jsonPath = AdsReportConstants.TEMP_FILE_PATH.concat(fileName).concat(AdsReportConstants.JSON);
        //下载文件临时存放本地
        fileUtil.downloadFile(sourceUrl, gzPath);
        //解压文件临时存放本地
        fileUtil.decompressGzipFile(gzPath, jsonPath);
        //读取json文件数据
        byte[] encoded = Files.readAllBytes(Paths.get(jsonPath));
        String jsonContent = new String(encoded);
        //删除临时文件
        fileUtil.deleteFile(gzPath, jsonPath);
        //下载报告文件并上传到oss（1.后续考虑是否要先查询是否有同名文件，否则会覆盖之前文件）
        ossUtil.downloadFileToOss(fileName.concat(AdsReportConstants.GZ), sourceUrl);
        return jsonContent;
    }

}
