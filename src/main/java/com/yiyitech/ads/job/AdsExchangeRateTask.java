package com.yiyitech.ads.job;

import cn.hutool.core.bean.BeanUtil;
import com.yiyitech.ads.mapper.AdsExchangeRateMapper;
import com.yiyitech.ads.model.AdsExchangeRateModel;
import com.yiyitech.support.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsExchangeRateTask.java
 * @Description
 * @createTime 2024年03月04日 14:41:00
 */
@Slf4j
@Component
public class AdsExchangeRateTask {
    @Autowired
    private AdsExchangeRateMapper adsExchangeRateMapper;

//    @Scheduled(cron = "${job.corn.exchange_rate}")
    public void adsExchangeRateTask() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate currentDate = LocalDate.now();
        LocalDate firstDayOfLastMonth = currentDate.minusMonths(1).withDayOfMonth(1);
        LocalDate firstDayOfCurrentMonth = currentDate.withDayOfMonth(1);
        //上月第一天
        String lastMonthFirstDay = firstDayOfLastMonth.format(formatter);
        //当月第一天
        String currentMonthFirstDay = firstDayOfCurrentMonth.format(formatter);
        updateExchangeRate(lastMonthFirstDay, currentMonthFirstDay);
    }

    public void updateExchangeRate(String lastMonthFirstDay, String currentMonthFirstDay) {
        log.info(currentMonthFirstDay + "更新汇率列表开始");
        //查询上个月汇率表
        List<AdsExchangeRateModel> exchangeRateModelLst = adsExchangeRateMapper.searchByStatusByDate(lastMonthFirstDay, lastMonthFirstDay);
        List<AdsExchangeRateModel> newModleLst = new ArrayList<>();
        String[] ignoreProperties = {"id", "effectDate"};
        exchangeRateModelLst.stream().forEach(model -> {
            AdsExchangeRateModel newModel = new AdsExchangeRateModel();
            BeanUtil.copyProperties(model, newModel, ignoreProperties);
            newModel.setEffectDate(currentMonthFirstDay);
            newModleLst.add(newModel);
        });
        if (newModleLst.size() > 0) {
            adsExchangeRateMapper.batchInsert(AdsExchangeRateModel.class, newModleLst, 100);
        }
        log.info(currentMonthFirstDay + "更新汇率列表结束");
    }

}
