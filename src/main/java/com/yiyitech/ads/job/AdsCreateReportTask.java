package com.yiyitech.ads.job;

import cn.hutool.core.date.DateUtil;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.mapper.AdsAccountInfoMapper;
import com.yiyitech.ads.mapper.AdsAccountProfileMapper;
import com.yiyitech.ads.mapper.AdsCreateReportMapper;
import com.yiyitech.ads.mapper.AdsReportTasksMapper;
import com.yiyitech.ads.model.AdsAccountInfoModel;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.AdsCreateReportModel;
import com.yiyitech.ads.model.AdsReportTasksModel;
import com.yiyitech.ads.model.response.AdsReportsResponse;
import com.yiyitech.ads.service.api.AdsReportsAPi;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.support.exception.BusinessException;
import com.yiyitech.support.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCreateReportTask.java
 * @Description
 * @createTime 2023年12月22日 13:31:00
 */
@Slf4j
@Component
public class AdsCreateReportTask {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsReportsAPi adsReportsAPi;
    @Autowired
    private AdsAccountInfoMapper adsAccountInfoMapper;
    @Autowired
    private AdsReportTasksMapper adsReportTasksMapper;
    @Autowired
    private AdsAccountProfileMapper adsAccountProfileMapper;
    @Autowired
    private AdsCreateReportMapper adsCreateReportMapper;

    @Scheduled(cron = "${job.corn.create_report}")
    public void createReportTask(){
        //获取今天日期
//        String reportDate = DateUtil.today();
        //获取昨天日期
//        String reportDate = DateUtils.format(DateUtil.yesterday(), "yyyy-MM-dd");
        //获取前天日期
        String reportDate = DateUtils.format(DateUtil.offsetDay(DateUtil.date(), -1), "yyyy-MM-dd");
        createReport(reportDate, 60);
    }

//    public void createReport(String reportDate) {
//        log.info(reportDate + "日请求创建报告任务开始");
//        //获取所有有效的账户
//        List<AdsAccountInfoModel> accountInfoModelLst = adsAccountInfoMapper.searchAllAccountInfo(true);
//        //有效的账户accId集合
//        List<String> accountIdLst = accountInfoModelLst.stream().map(AdsAccountInfoModel::getAccountId).collect(Collectors.toList());
//        //获取所有的账户站点文件信息
//        List<AdsAccountProfileModel> accountProfilelLst = adsAccountProfileMapper.searchByAccountId(accountIdLst);
//        //查询所有有效的报告任务
//        List<AdsReportTasksModel> reportTasksModelLst = adsReportTasksMapper.searchAllAccountInfo(true);
//        //请求生成每个站点文件对应的报告
//        String accountId = "";
//        String profileId = "";
//        String accessToken = "";
//        List<AdsCreateReportModel> batchCreateReportModelLst = new ArrayList<>();
//        for (AdsAccountProfileModel profile : accountProfilelLst) {
//            accountId = profile.getAccountId();
//            profileId = String.valueOf(profile.getProfileId());
//            accessToken = redisUtils.getAccountAccessTokenAndRefreshToken(accountId, CommonConstants.ADS_PREFIX_KEY);
//            for (AdsReportTasksModel task : reportTasksModelLst) {
//                AdsReportsResponse apiResponse = adsReportsAPi.createReport(accessToken, profileId, task.getReportName().concat(reportDate), reportDate, task.getGroupBy(), task.getReportTypeId(), Arrays.stream(task.getColumns().split(",")).collect(Collectors.toList()));
//                AdsCreateReportModel adscreateReportModel = new AdsCreateReportModel(reportDate,
//                        accountId,
//                        profileId,
//                        apiResponse.getReportId(),
//                        task.getReportTypeId(),
//                        apiResponse.getName(),
//                        apiResponse.getCreatedAt(),
//                        apiResponse.getStartDate(),
//                        apiResponse.getEndDate(),
//                        apiResponse.getStatus(),
//                        apiResponse.getFailureReason());
//                batchCreateReportModelLst.add(adscreateReportModel);
//                try {
//                    Thread.sleep(3000);
//                } catch (InterruptedException e) {
//                    log.error("站点：" + profileId + "生成：" + task.getReportTypeId() + "类型报告task异常：" + e.getMessage());
//                    throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
//                }
//            }
//        }
//        //保存创建的报告信息
//        adsCreateReportMapper.batchInsert(AdsCreateReportModel.class, batchCreateReportModelLst, 100);
//        log.info(reportDate + "日请求创建报告任务结束");
//    }


    public void createReport(String reportDate, Integer countingBack) {
        log.info(reportDate + " 日请求创建报告任务（并发）开始");
        //获取所有有效的账户
        List<AdsAccountInfoModel> accountInfoModelLst = adsAccountInfoMapper.searchAllAccountInfo(true);
        //有效的账户accId集合
        List<String> accountIdLst = accountInfoModelLst.stream().map(AdsAccountInfoModel::getAccountId).collect(Collectors.toList());
        //获取所有的账户站点文件信息
        List<AdsAccountProfileModel> accountProfilelLst = adsAccountProfileMapper.searchByAccountId(accountIdLst);
        //查询所有有效的报告任务
        List<AdsReportTasksModel> reportTasksModelLst = adsReportTasksMapper.searchAllAccountInfo(true);

        LocalDate endDate = LocalDate.parse(reportDate);
        LocalDate startDate = endDate.minusDays(countingBack-1);

        final int BATCH_SIZE = 100;
        final int THREAD_COUNT = 6;

        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);

        for (AdsAccountProfileModel profile : accountProfilelLst) {
            executor.submit(() -> {
                String accountId = profile.getAccountId();
                String profileId = String.valueOf(profile.getProfileId());
                String accessToken = redisUtils.getAccountAccessTokenAndRefreshToken(accountId, CommonConstants.ADS_PREFIX_KEY);

                for (LocalDate currentDate = startDate; !currentDate.isAfter(endDate); currentDate = currentDate.plusDays(1)) {
                    String currentReportDate = currentDate.toString();
                    //profileId + currentReportDate先删除旧记录
                    try {
                        synchronized (adsCreateReportMapper) {
                            adsCreateReportMapper.deleteByProfileIdAndJobDate(profileId, currentReportDate);
                        }
                    } catch (Exception e) {
                        log.error("删除旧报告失败 [profileId={}, jobDate={}]: {}", profileId, currentReportDate, e.getMessage());
                        continue; //删除失败就跳过本轮，防止数据重复
                    }

                    List<AdsCreateReportModel> tempBatch = new ArrayList<>(BATCH_SIZE);

                    for (AdsReportTasksModel task : reportTasksModelLst) {
                        try {
                            AdsReportsResponse apiResponse = adsReportsAPi.createReport(
                                    accessToken,
                                    profileId,
                                    task.getReportName().concat(currentReportDate),
                                    currentReportDate,
                                    task.getGroupBy(),
                                    task.getReportTypeId(),
                                    Arrays.stream(task.getColumns().split(",")).collect(Collectors.toList())
                            );
                            AdsCreateReportModel reportModel = new AdsCreateReportModel(
                                    currentReportDate,
                                    accountId,
                                    profileId,
                                    apiResponse.getReportId(),
                                    task.getReportTypeId(),
                                    apiResponse.getName(),
                                    apiResponse.getCreatedAt(),
                                    apiResponse.getStartDate(),
                                    apiResponse.getEndDate(),
                                    apiResponse.getStatus(),
                                    apiResponse.getFailureReason()
                            );
                            reportModel.setJobDate(currentReportDate);
                            tempBatch.add(reportModel);

                            if (tempBatch.size() >= BATCH_SIZE) {
                                synchronized (adsCreateReportMapper) {
                                    adsCreateReportMapper.batchInsert(AdsCreateReportModel.class, tempBatch, BATCH_SIZE);
                                }
                                tempBatch.clear();
                            }
                            Thread.sleep(1500);
                        } catch (Exception e) {
                            log.error("创建报告失败 [profileId={}, reportTypeId={}, date={}]: {}", profileId, task.getReportTypeId(), currentReportDate, e.getMessage());
                        }
                    }
                    if (!tempBatch.isEmpty()) {
                        try {
                            synchronized (adsCreateReportMapper) {
                                adsCreateReportMapper.batchInsert(AdsCreateReportModel.class, tempBatch, BATCH_SIZE);
                            }
                        } catch (Exception e) {
                            log.error("插入剩余数据失败 [profileId={}, jobDate={}]: {}", profileId, currentReportDate, e.getMessage());
                        }
                    }
                }
            });
        }

        executor.shutdown();
        try {
            if (!executor.awaitTermination(1, TimeUnit.HOURS)) {
                executor.shutdownNow();
                log.warn("createReport 超时强制关闭线程池");
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            log.error("createReport 线程被中断: {}", e.getMessage());
            Thread.currentThread().interrupt();
        }
        log.info(reportDate + " 日请求创建报告任务结束（并发）");
    }


}
