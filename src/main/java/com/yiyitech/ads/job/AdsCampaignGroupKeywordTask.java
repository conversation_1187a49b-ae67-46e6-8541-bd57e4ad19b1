package com.yiyitech.ads.job;

import cn.hutool.core.date.DateUtil;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.mapper.AdsAccountProfileMapper;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.AdsAdGroupModel;
import com.yiyitech.ads.model.AdsCampaignModel;
import com.yiyitech.ads.model.AdsKeywordModel;
import com.yiyitech.ads.model.response.AdsSpCampaignsListResponse;
import com.yiyitech.ads.model.response.AdsSpGroupsListResponse;
import com.yiyitech.ads.model.response.AdsSpKeywordsListResponse;
import com.yiyitech.ads.service.AdsAdGroupService;
import com.yiyitech.ads.service.AdsCampaignService;
import com.yiyitech.ads.service.AdsKeywordService;
import com.yiyitech.ads.service.api.AdsSpCampaignsAPi;
import com.yiyitech.ads.service.api.AdsSpGroupsAPi;
import com.yiyitech.ads.service.api.AdsSpKeywordsAPi;
import com.yiyitech.ads.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCampaignGroupKeywordTask.java
 * @Description
 * @createTime 2023年12月22日 11:53:00
 */
@Slf4j
@Component
public class AdsCampaignGroupKeywordTask {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsAccountProfileMapper adsAccountProfileMapper;
    @Autowired
    private AdsSpCampaignsAPi adsSpCampaignsAPi;
    @Autowired
    private AdsSpGroupsAPi adsSpGroupsAPi;
    @Autowired
    private AdsSpKeywordsAPi adsSpKeywordsAPi;
    @Autowired
    private AdsCampaignService adsCampaignService;
    @Autowired
    private AdsAdGroupService adsAdGroupService;
    @Autowired
    private AdsKeywordService adsKeywordService;

//    @Scheduled(cron = "${job.corn.campaign_group_keyword}")
    public void refreshAdsCampaignGroupKeyword() {
        String date = DateUtil.today();
        log.info(date + "日刷新账户活动、活动组、关键词任务开始");
        //获取所有的账户站点文件信息
        List<AdsAccountProfileModel> accountProfilelLst = adsAccountProfileMapper.searchAllAccountProfile();
        List<AdsCampaignModel> batchCampaignLst = new ArrayList<>();
        List<AdsAdGroupModel> batchGroupLst = new ArrayList<>();
        List<AdsKeywordModel> batchKeywordLst = new ArrayList<>();
        String accessToken = "";
        //获取各个账户站点的活动、活动组、关键词数据
        for (AdsAccountProfileModel profile : accountProfilelLst) {
            accessToken = redisUtils.getAccountAccessTokenAndRefreshToken(profile.getAccountId(), CommonConstants.ADS_PREFIX_KEY);
            List<AdsSpCampaignsListResponse.CampaignsData> campaignsDataLst = adsSpCampaignsAPi.campaignsList(accessToken, String.valueOf(profile.getProfileId())).getCampaigns();
            List<AdsSpGroupsListResponse.GroupsData> groupsDataLst = adsSpGroupsAPi.groupsList(accessToken, String.valueOf(profile.getProfileId()), null).getAdGroups();
            List<AdsSpKeywordsListResponse.KeywordsDat> keywordsDatLst = adsSpKeywordsAPi.keywordsList(accessToken, String.valueOf(profile.getProfileId()), null, null).getKeywords();
            campaignsDataLst.forEach(data ->{
                AdsCampaignModel model = new AdsCampaignModel()
                        .setAccountId(profile.getAccountId())
                        .setProfileId(profile.getProfileId())
                        .setPortfolioId(data.getPortfolioId())
                        .setCampaignId(data.getCampaignId())
                        .setName(data.getName())
                        .setStartDate(data.getStartDate())
                        .setState(data.getState())
                        .setTargetingType(data.getTargetingType());
                batchCampaignLst.add(model);
            });
            groupsDataLst.forEach(data ->{
                AdsAdGroupModel model = new AdsAdGroupModel()
                        .setAccoutnId(profile.getAccountId())
                        .setProfileId(profile.getProfileId())
                        .setCampaignId(data.getCampaignId())
                        .setAdGroupId(data.getAdGroupId())
                        .setName(data.getName())
                        .setState(data.getState());
                batchGroupLst.add(model);
            });
            keywordsDatLst.forEach(data ->{
                AdsKeywordModel model = new AdsKeywordModel()
                        .setAccoutnId(profile.getAccountId())
                        .setProfileId(profile.getProfileId())
                        .setCampaignId(data.getCampaignId())
                        .setAdGroupId(data.getAdGroupId())
                        .setKeywordId(data.getKeywordId())
                        .setKeywordText(data.getKeywordText())
                        .setMatchType(data.getMatchType())
                        .setState(data.getState());
                batchKeywordLst.add(model);
            });
        }
        //新增或更新广告活动、活动组、关键词（账户站点纬度）
        adsCampaignService.batchSaveOrUpdateByUniqueKey(batchCampaignLst);
        adsAdGroupService.batchSaveOrUpdateByUniqueKey(batchGroupLst);
        adsKeywordService.batchSaveOrUpdateByUniqueKey(batchKeywordLst);
        log.info(date + "日刷新账户活动、活动组、关键词任务结束");
    }
}
