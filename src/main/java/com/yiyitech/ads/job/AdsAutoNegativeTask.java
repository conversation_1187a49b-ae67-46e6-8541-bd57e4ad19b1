package com.yiyitech.ads.job;

import cn.hutool.core.date.DateUtil;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.enums.*;
import com.yiyitech.ads.mapper.*;
import com.yiyitech.ads.model.*;
import com.yiyitech.ads.model.assembler.AdsNegativeOperLogAssembler;
import com.yiyitech.ads.service.api.AdsNegativeApi;
import com.yiyitech.ads.util.AdsAutoNegSeachTermAggregatorUtil;
import com.yiyitech.ads.util.AsyncTask;
import com.yiyitech.ads.util.AsyncTaskUtil;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.ads.vo.AdsAutoNegSearchTerm;
import com.yiyitech.support.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCatalogUpdateTask2.java
 * @Description
 * @createTime 2025年07月16日 18:50:00
 */
@Slf4j
@Component
public class AdsAutoNegativeTask {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private AdsNegativeApi adsNegativeApi;
    @Autowired
    private AdsSvcConfigurationMapper svcConfigurationMapper;
    @Autowired
    private AdsDailySearchTermReportMapper dailySearchTermReportMapper;
    @Autowired
    private AdsMonitorRulesMapper rulesMapper;
    @Autowired
    private AdsConditionGroupMapper conditionGroupMapper;
    @Autowired
    private AdsConditionMapper conditionMapper;
    @Autowired
    private AdsNegativeOperLogMapper negativeOperLogMapper;
    @Autowired
    private AdsDailyAdvertiserReportMapper advertiserReportMapper;

    @Scheduled(cron = "${job.corn.auto_negative}")
    public void autoNegativeTask() {
        String date = DateUtils.format(DateUtil.offsetDay(DateUtil.date(), -1), "yyyy-MM-dd");
        autoNegative(date);
    }

    private static final String LOCK_KEY = CommonConstants.ADS_PREFIX_KEY.concat(CommonConstants.ANTO_NEG_LOCK);
    private static final Duration LOCK_EXPIRE_TIME = Duration.ofMinutes(10);
    private static final long LOCK_RENEW_INTERVAL = 5; // 分钟

    public void autoNegative(String endDate) {
        log.info(endDate + " 自动否词任务开始");
        String lockValue = redisUtils.tryLock(LOCK_KEY, LOCK_EXPIRE_TIME.getSeconds(), TimeUnit.SECONDS);
        if (lockValue == null) {
            log.info("已有否词任务在执行，跳过本次");
            return;
        }
        log.info("成功获取自动否词锁，开始执行任务，锁值：{}", lockValue);
        ScheduledExecutorService lockRenewScheduler = Executors.newScheduledThreadPool(1);
        Runnable renewTask = () -> {
            String currentValue = redisTemplate.opsForValue().get(LOCK_KEY);
            if (lockValue.equals(currentValue)) {
                redisTemplate.expire(LOCK_KEY, LOCK_EXPIRE_TIME.getSeconds(), TimeUnit.SECONDS);
                log.info("否词任务锁续期成功");
            } else {
                log.warn("否词任务锁已被其他实例释放，停止续期线程");
                lockRenewScheduler.shutdown();
            }
        };
        lockRenewScheduler.scheduleAtFixedRate(renewTask, LOCK_RENEW_INTERVAL, LOCK_RENEW_INTERVAL, TimeUnit.MINUTES);

        try {
            String startDate = LocalDate.parse(endDate).minusDays(29).toString();
            // 获取所有有效配置
            List<AdsSvcConfigurationModel> svcConfigurationModelLst = svcConfigurationMapper.searchByStatus(true);
            if (CollectionUtils.isEmpty(svcConfigurationModelLst)) {
                log.info("无有效配置，任务结束");
                return;
            }
            // 提取 accountId + profileId 组合
            List<AdsAccountProfileModel> profileLst = svcConfigurationModelLst.stream()
                    .map(cfg -> new AdsAccountProfileModel()
                            .setAccountId(cfg.getAccountId())
                            .setProfileId(cfg.getProfileId()))
                    .collect(Collectors.toList());

            // 查询所有满足条件的搜索词报告
            List<AdsDailySearchTermReportModel> allSearchTermReportModelLst = dailySearchTermReportMapper.selectByProLstByDate(profileLst, startDate, endDate);

            //当前用户拥有店铺下否词日志，并按店铺纬度分组
            List<AdsNegativeOperLogModel> negLogLst = negativeOperLogMapper.searchByProfileLstByStatus(profileLst, AdsNegLogStatusEnum.getNonAutoFailedCodes());
            Map<String, List<String>> negLogMap = negLogLst.stream()
                    .collect(Collectors.groupingBy(
                            log -> String.join("#", log.getAccountId(), String.valueOf(log.getProfileId()),
                                    log.getCampaignId(), log.getAdGroupId()),
                            Collectors.mapping(AdsNegativeOperLogModel::getNegativeValue, Collectors.toList())
                    ));

            //筛选范围内的商品报告（获取asin），并按店铺、广告活动+广告组 纬度两次分组
            List<AdsDailyAdvertiserReportModel> advertiserModelLst = advertiserReportMapper.selectByProLstByDate(profileLst, startDate, endDate);
            Map<String, Map<String, AdsDailyAdvertiserReportModel>> advertiserMap = advertiserModelLst.stream()
                    .collect(Collectors.groupingBy(
                            adv -> adv.getAccountId() + "-" + String.valueOf(adv.getProfileId()),//外层key: 店铺标识
                            Collectors.toMap(
                                    adv -> adv.getCampaignId() + "-" + adv.getAdGroupId(),//内层key:广告活动+组
                                    Function.identity(),
                                    (existing, replacement) -> replacement
                            )
                    ));

            //configId 下的 rules
            List<Long> configIds = svcConfigurationModelLst.stream().map(AdsSvcConfigurationModel::getId).collect(Collectors.toList());
            List<AdsMonitorRulesModel> rulesModelLst = rulesMapper.searchByConfigIdsByStatus(configIds, true);
            //rules Map
            Map<Long, List<AdsMonitorRulesModel>> rulesMap = rulesModelLst.stream()
                    .filter(rule -> rule.getSvcConfigId() != null)
                    .collect(Collectors.groupingBy(AdsMonitorRulesModel::getSvcConfigId));

            //ruleId 下的 groups
            List<Long> ruleIds = rulesModelLst.stream().map(AdsMonitorRulesModel::getId).collect(Collectors.toList());
            List<AdsConditionGroupModel> groupModelLst = conditionGroupMapper.searchByRuleIdsByStatus(ruleIds, true);
            //groups Map
            Map<Long, List<AdsConditionGroupModel>> groupsMap = groupModelLst.stream()
                    .filter(group -> group.getRuleId() != null)
                    .collect(Collectors.groupingBy(AdsConditionGroupModel::getRuleId));

            //groupId 下的 conditions
            List<Long> groupIds = groupModelLst.stream().map(AdsConditionGroupModel::getId).collect(Collectors.toList());
            List<AdsConditionModel> conditionModelLst = conditionMapper.searchByGroupIdsByStatus(groupIds, true);
            //conditions Map
            Map<Long, List<AdsConditionModel>> conditionsMap = conditionModelLst.stream()
                    .filter(cond -> cond.getGroupId() != null)
                    .collect(Collectors.groupingBy(AdsConditionModel::getGroupId));

            //构造AsyncTask列表多线程执行
            List<AsyncTask> asyncTasks = svcConfigurationModelLst.stream()
                    .map(config -> (AsyncTask) new AsyncTask() {
                        @Override
                        public Object run() {
                            log.info("开始处理配置：accountId={}, profileId={}, configId={}", config.getAccountId(), config.getProfileId(), config.getId());
                            processConfig(config, endDate, allSearchTermReportModelLst, advertiserMap, negLogMap, rulesMap, groupsMap, conditionsMap);
                            log.info("配置处理完成：accountId={}, profileId={}, configId={}", config.getAccountId(), config.getProfileId(), config.getId());
                            return null;
                        }
                        @Override
                        public void callback(Object result) {
                            //TODO 后续根据需求做兜底机制
                        }
                    })
                    .collect(Collectors.toList());
            AsyncTaskUtil.runAll(asyncTasks);
        } catch (Exception e) {
            log.error("autoNegative任务执行异常", e);
        } finally {
            boolean released = redisUtils.releaseLock(LOCK_KEY, lockValue);
            if (released) {
                log.info("否词任务锁已释放");
            } else {
                log.warn("否词任务锁释放失败 或 已被其他实例释放");
            }
            lockRenewScheduler.shutdownNow();
        }
        log.info(endDate + " 自动否词任务结束");
    }

    private void processConfig(AdsSvcConfigurationModel config,
                               String endDate,
                               List<AdsDailySearchTermReportModel> allSearchTermReportModelLst,
                               Map<String, Map<String, AdsDailyAdvertiserReportModel>> advertiserMap,
                               Map<String, List<String>> negLogMap,
                               Map<Long, List<AdsMonitorRulesModel>> rulesMap,
                               Map<Long, List<AdsConditionGroupModel>> groupsMap,
                               Map<Long, List<AdsConditionModel>> conditionsMap) {
        try {
            //获取本config所对应店铺最高天数内的所有搜索词集合
            List<AdsDailySearchTermReportModel> currentConfigSearchTermReportModelLst = allSearchTermReportModelLst.stream()
                    .filter(m -> m.getAccountId().equals(config.getAccountId()) && m.getProfileId().equals(config.getProfileId()))
                    .collect(Collectors.toList());
            //按7天，30天合并数据（针对花费，点击量，销售量字段，其余字段根据后续需要），并剔除已否过的词
            AdsAutoNegSeachTermAggregatorUtil.MergedSearchTermGroup merged =
                    AdsAutoNegSeachTermAggregatorUtil.buildMerged7And30(currentConfigSearchTermReportModelLst, endDate,
                            advertiserMap.get(config.getAccountId() + "-" + config.getProfileId()), negLogMap);
            List<AdsAutoNegSearchTerm> merged7DayList = merged.getMerged7DayList();
            List<AdsAutoNegSearchTerm> merged30DayList = merged.getMerged30DayList();
            //各个天数合并list转map
            Map<String, AdsAutoNegSearchTerm> map7d = merged7DayList.stream()
                    .collect(Collectors.toMap(AdsAutoNegSearchTerm::getUniqueSign, Function.identity(), (a, b) -> a));
            Map<String, AdsAutoNegSearchTerm> map30d = merged30DayList.stream()
                    .collect(Collectors.toMap(AdsAutoNegSearchTerm::getUniqueSign, Function.identity(), (a, b) -> a));
            //TODO 循环判断词当前配置所匹配店铺下的所有搜索词是否满足条件组（30天的搜索词，后续配置升级的话，考虑根据每个配置内条件的最高天数动态生成要匹配的聚合组）
            for (AdsAutoNegSearchTerm term : merged30DayList) {
                AdsMonitorRulesModel matchedRule = matchedConfig(term, config, rulesMap, groupsMap, conditionsMap, map7d, map30d);
                if (matchedRule != null) {
                    log.info("满足否定条件：accountId={}, profileId={}, searchTerm={}", term.getAccountId(), term.getProfileId(), term.getSearchTerm());
                    //判断是关键词否还是商品否
                    Boolean isAsinNeg = term.getSearchTerm() != null && term.getSearchTerm().startsWith("b0") && term.getSearchTerm().length() == 10;
                    //添加否词日志
                    AdsNegativeOperLogModel logModel = AdsNegativeOperLogAssembler.build(null, term.getAccountId(), term.getProfileId(),
                            term.getCampaignId(), term.getCampaignName(), term.getAdGroupId(), term.getAdGroupName(),
                            term.getUniqueSign(), term.getSearchTerm(), null, AdsNegSourceEnum.SEARCHTERM.getCode(),
                            term.getParentAsin(), term.getAsin(), term.getSku(), term.getPicture(), term.getAsinItemName(),
                            null, isAsinNeg ? AdsNegOperTypeEnum.ASIN_NEG.getCode() : AdsNegOperTypeEnum.KEYWORD_NEG.getCode(),
                            null, null, AdsNegJudgeTypeEnum.SYSTEM_AND_ARTIFICIAL.getCode(), AdsNegLogStatusEnum.UNTREATED.getCode());
                    //若规则为自动执行则调用ads接口进行否词
                    boolean success = false;
                    if (matchedRule.getActionType() == AdsNegJudgeTypeEnum.SYSTEM.getCode()) {
                        //接口否词
                        String adsAccessToken = redisUtils.getAccountAccessTokenAndRefreshToken(term.getAccountId(), CommonConstants.ADS_PREFIX_KEY);
                        int retry = 0;
                        while (retry < 3) {
                            try {
                                if (isAsinNeg) {
                                    adsNegativeApi.negativeTargeting(adsAccessToken, String.valueOf(term.getProfileId()), term.getCampaignId(),
                                            term.getAdGroupId(), term.getSearchTerm(), AdsMatchTypeEnum.ADV_ASIN.getCode());
                                    logModel.setNegativeType(AdsMatchTypeEnum.ADV_ASIN.getCode());
                                } else {
                                    adsNegativeApi.negativeKeywords(adsAccessToken, String.valueOf(term.getProfileId()), term.getCampaignId(),
                                            term.getAdGroupId(), term.getSearchTerm(), AdsMatchTypeEnum.EXACT.getCode());
                                    logModel.setNegativeType(AdsMatchTypeEnum.EXACT.getCode());
                                }
                                //自动否词时覆盖的日志字段
                                logModel.setOperLevel(AdsNegOperLevelEnum.CAMPAIGN.getCode());
                                logModel.setAfterOper(AdsMatchTypeEnum.of(logModel.getNegativeType()).getDesc() + "-" + logModel.getNegativeValue());
                                logModel.setJudgeType(AdsNegJudgeTypeEnum.SYSTEM.getCode());
                                logModel.setStatus(AdsNegLogStatusEnum.NOTVIEWED.getCode());
                                success = true;
                                log.info("满足否定规则：configId={}, ruleId={}, term={}", config.getId(), matchedRule.getId(), term.getSearchTerm());
                                break; //成功跳出重试
                            } catch (Exception e) {
                                retry++;
                                log.warn("否词接口调用失败，第{}次重试，原因：{}", retry, e.getMessage());
                                Thread.sleep(2000);
                            }
                        }
                        if (!success) {
                            logModel.setStatus(AdsNegLogStatusEnum.AUTO_FAILED.getCode());
                        }
                    }
                    negativeOperLogMapper.insert(logModel);
                }
            }
        } catch (Exception e) {
            log.error("配置处理异常，configId={} : {}", config.getId(), e.getMessage(), e);
        }
    }

    public static AdsMonitorRulesModel matchedConfig(AdsAutoNegSearchTerm data,
                                                     AdsSvcConfigurationModel config,
                                                     Map<Long, List<AdsMonitorRulesModel>> configIdToRules,
                                                     Map<Long, List<AdsConditionGroupModel>> ruleIdToGroups,
                                                     Map<Long, List<AdsConditionModel>> groupIdToConditions,
                                                     Map<String, AdsAutoNegSearchTerm> map7d,
                                                     Map<String, AdsAutoNegSearchTerm> map30d) {
        List<AdsMonitorRulesModel> rules = configIdToRules.getOrDefault(config.getId(), Collections.emptyList());
        for (AdsMonitorRulesModel rule : rules) {
            List<AdsConditionGroupModel> groups = ruleIdToGroups.getOrDefault(rule.getId(), Collections.emptyList());
            for (AdsConditionGroupModel group : groups) {
                List<AdsConditionModel> conditions = groupIdToConditions.getOrDefault(group.getId(), Collections.emptyList());
                boolean groupMatched = true;
                for (AdsConditionModel condition : conditions) {
                    boolean singleMatched = matchCondition(data, condition, map7d, map30d);
                    if (!singleMatched) {
                        groupMatched = false;
                        break;
                    }
                }
                if (groupMatched) return rule;
            }
        }
        return null;
    }

    private static boolean matchCondition(AdsAutoNegSearchTerm currentData,
                                          AdsConditionModel condition,
                                          Map<String, AdsAutoNegSearchTerm> map7d,
                                          Map<String, AdsAutoNegSearchTerm> map30d) {
        Map<String, AdsAutoNegSearchTerm> targetMap = (condition.getDays() == 30) ? map30d : map7d;
        AdsAutoNegSearchTerm matched = targetMap.get(currentData.getUniqueSign());
        if (matched == null) return false;

        try {
            int actual;
            //TODO 数据库已经有个ads_conditions_field表了，后续条件配置升级支持多字段的话，考虑根据匹配值从数据库去除字段，反射取获匹配值
            if (condition.getFieldId() == 1) {
                actual = Optional.ofNullable(matched.getClicks()).orElse(0);
            } else if (condition.getFieldId() == 2) {
                actual = Optional.ofNullable(matched.getPurchases1d()).orElse(0);
            } else {
                return false;
            }
            int expected = Integer.parseInt(condition.getFieldValue());
            return compare(actual, expected, condition.getOperator());
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private static boolean compare(int actual, int expected, int operator) {
        switch (operator) {
            case 1:
                return actual > expected;
            case 2:
                return actual >= expected;
            case 3:
                return actual < expected;
            case 4:
                return actual <= expected;
            case 5:
                return actual == expected;
            default:
                return false;
        }
    }
}
