package com.yiyitech.ads.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.yiyitech.ads.mapper.AdsAccountInfoMapper;
import com.yiyitech.ads.mapper.AdsAccountProfileMapper;
import com.yiyitech.ads.model.AdsAccountInfoModel;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.service.AdsSearchTermAnalyticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 搜索词分析数据同步定时任务
 * 定期从Amazon SP-API同步搜索词分析数据
 * 包括搜索词数据、类目关联、商品排名等
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermAnalyticsTask.java
 * @Description 搜索词分析数据同步定时任务
 * @createTime 2025年01月31日
 */
@Slf4j
@Component
public class AdsSearchTermAnalyticsTask {

    @Autowired
    private AdsSearchTermAnalyticsService searchTermAnalyticsService;

    @Autowired
    private AdsAccountInfoMapper accountInfoMapper;

    @Autowired
    private AdsAccountProfileMapper accountProfileMapper;

    /**
     * 每日同步搜索词分析数据
     * 每天凌晨2点执行，同步前一天的数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncDailySearchTermAnalytics() {
        log.info("开始执行每日搜索词分析数据同步任务");
        
        try {
            // 获取昨天的日期
            String yesterday = LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
            
            // 获取所有有效的账户信息
            List<AdsAccountInfoModel> accountList = accountInfoMapper.searchAllAccountInfo(true);

            if (CollectionUtil.isEmpty(accountList)) {
                log.warn("没有找到有效的账户信息，跳过同步任务");
                return;
            }
            
            int totalSuccess = 0;
            int totalFail = 0;
            
            for (AdsAccountInfoModel account : accountList) {
                try {
                    // 检查账户是否有有效的SP-API访问令牌
                    if (StrUtil.isBlank(account.getRefreshTokenSp())) {
                        log.warn("账户 {} 没有有效的SP-API访问令牌，跳过", account.getAccountId());
                        continue;
                    }

                    // 获取账户的配置文件列表
                    List<String> accountIds = new ArrayList<>();
                    accountIds.add(account.getAccountId());
                    List<AdsAccountProfileModel> profileList = accountProfileMapper.searchByAccountId(accountIds);

                    if (CollectionUtil.isEmpty(profileList)) {
                        log.warn("账户 {} 没有配置文件，跳过", account.getAccountId());
                        continue;
                    }
                    
                    for (AdsAccountProfileModel profile : profileList) {
                        try {
                            // 同步搜索词分析数据
                            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermAnalyticsData(
                                    account.getAccountId(),
                                    profile.getProfileId(),
                                    profile.getCountryCode(),
                                    yesterday,
                                    yesterday
                            );
                            
                            if ((Boolean) result.get("success")) {
                                int successCount = (Integer) result.get("successCount");
                                int failCount = (Integer) result.get("failCount");
                                totalSuccess += successCount;
                                totalFail += failCount;
                                
                                log.info("账户 {} 配置 {} 搜索词数据同步完成 - 成功: {}, 失败: {}", 
                                        account.getAccountId(), profile.getProfileId(), successCount, failCount);
                            } else {
                                log.error("账户 {} 配置 {} 搜索词数据同步失败: {}", 
                                        account.getAccountId(), profile.getProfileId(), result.get("message"));
                                totalFail++;
                            }
                            
                        } catch (Exception e) {
                            log.error("账户 {} 配置 {} 搜索词数据同步异常: {}", 
                                    account.getAccountId(), profile.getProfileId(), e.getMessage(), e);
                            totalFail++;
                        }
                        
                        // 避免API调用过于频繁，添加延迟
                        Thread.sleep(5000);
                    }
                    
                } catch (Exception e) {
                    log.error("处理账户 {} 时发生异常: {}", account.getAccountId(), e.getMessage(), e);
                    totalFail++;
                }
            }
            
            log.info("每日搜索词分析数据同步任务完成 - 总成功: {}, 总失败: {}", totalSuccess, totalFail);
            
        } catch (Exception e) {
            log.error("每日搜索词分析数据同步任务执行异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 每日同步搜索词类目关联数据
     * 每天凌晨3点执行，同步前一天的数据
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void syncDailySearchTermCategoryRelations() {
        log.info("开始执行每日搜索词类目关联数据同步任务");
        
        try {
            String yesterday = LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
            List<AdsAccountInfoModel> accountList = accountInfoMapper.searchAllAccountInfo(true);

            int totalSuccess = 0;
            int totalFail = 0;

            for (AdsAccountInfoModel account : accountList) {
                if (StrUtil.isBlank(account.getRefreshTokenSp())) {
                    continue;
                }

                List<String> accountIds = new ArrayList<>();
                accountIds.add(account.getAccountId());
                List<AdsAccountProfileModel> profileList = accountProfileMapper.searchByAccountId(accountIds);
                
                for (AdsAccountProfileModel profile : profileList) {
                    try {
                        Map<String, Object> result = searchTermAnalyticsService.syncSearchTermCategoryRelations(
                                account.getAccountId(),
                                profile.getProfileId(),
                                profile.getCountryCode(),
                                yesterday,
                                yesterday
                        );
                        
                        if ((Boolean) result.get("success")) {
                            int successCount = (Integer) result.get("successCount");
                            int failCount = (Integer) result.get("failCount");
                            totalSuccess += successCount;
                            totalFail += failCount;
                            
                            log.info("账户 {} 配置 {} 类目关联数据同步完成 - 成功: {}, 失败: {}", 
                                    account.getAccountId(), profile.getProfileId(), successCount, failCount);
                        } else {
                            log.error("账户 {} 配置 {} 类目关联数据同步失败: {}", 
                                    account.getAccountId(), profile.getProfileId(), result.get("message"));
                            totalFail++;
                        }
                        
                    } catch (Exception e) {
                        log.error("账户 {} 配置 {} 类目关联数据同步异常: {}", 
                                account.getAccountId(), profile.getProfileId(), e.getMessage(), e);
                        totalFail++;
                    }
                    
                    Thread.sleep(5000);
                }
            }
            
            log.info("每日搜索词类目关联数据同步任务完成 - 总成功: {}, 总失败: {}", totalSuccess, totalFail);
            
        } catch (Exception e) {
            log.error("每日搜索词类目关联数据同步任务执行异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 每日同步搜索词商品排名数据
     * 每天凌晨4点执行，同步前一天的数据
     */
    @Scheduled(cron = "0 0 4 * * ?")
    public void syncDailySearchTermProductRankings() {
        log.info("开始执行每日搜索词商品排名数据同步任务");
        
        try {
            String yesterday = LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
            List<AdsAccountInfoModel> accountList = accountInfoMapper.searchAllAccountInfo(true);

            int totalSuccess = 0;
            int totalFail = 0;

            for (AdsAccountInfoModel account : accountList) {
                if (StrUtil.isBlank(account.getRefreshTokenSp())) {
                    continue;
                }

                List<String> accountIds = new ArrayList<>();
                accountIds.add(account.getAccountId());
                List<AdsAccountProfileModel> profileList = accountProfileMapper.searchByAccountId(accountIds);
                
                for (AdsAccountProfileModel profile : profileList) {
                    try {
                        Map<String, Object> result = searchTermAnalyticsService.syncSearchTermProductRankings(
                                account.getAccountId(),
                                profile.getProfileId(),
                                profile.getCountryCode(),
                                yesterday,
                                yesterday
                        );
                        
                        if ((Boolean) result.get("success")) {
                            int successCount = (Integer) result.get("successCount");
                            int failCount = (Integer) result.get("failCount");
                            totalSuccess += successCount;
                            totalFail += failCount;
                            
                            log.info("账户 {} 配置 {} 商品排名数据同步完成 - 成功: {}, 失败: {}", 
                                    account.getAccountId(), profile.getProfileId(), successCount, failCount);
                        } else {
                            log.error("账户 {} 配置 {} 商品排名数据同步失败: {}", 
                                    account.getAccountId(), profile.getProfileId(), result.get("message"));
                            totalFail++;
                        }
                        
                    } catch (Exception e) {
                        log.error("账户 {} 配置 {} 商品排名数据同步异常: {}", 
                                account.getAccountId(), profile.getProfileId(), e.getMessage(), e);
                        totalFail++;
                    }
                    
                    Thread.sleep(5000);
                }
            }
            
            log.info("每日搜索词商品排名数据同步任务完成 - 总成功: {}, 总失败: {}", totalSuccess, totalFail);
            
        } catch (Exception e) {
            log.error("每日搜索词商品排名数据同步任务执行异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 每周数据清理任务
     * 每周日凌晨1点执行，清理过期数据
     */
    @Scheduled(cron = "0 0 1 * * SUN")
    public void weeklyDataCleanup() {
        log.info("开始执行每周数据清理任务");
        
        try {
            // 清理90天前的数据
            String cleanupDate = LocalDate.now().minusDays(90).format(DateTimeFormatter.ISO_LOCAL_DATE);
            
            // 这里可以调用相应的清理方法
            // 例如：searchTermAnalyticsMapper.deleteExpiredData(cleanupDate);
            
            log.info("每周数据清理任务完成");
            
        } catch (Exception e) {
            log.error("每周数据清理任务执行异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 手动触发数据同步
     * 用于测试或紧急数据同步
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    public Map<String, Object> manualSyncSearchTermData(String accountId, Long profileId, 
                                                       String marketplaceId, String startDate, String endDate) {
        log.info("手动触发搜索词数据同步 - 账户: {}, 配置: {}, 日期: {} 到 {}", 
                accountId, profileId, startDate, endDate);
        
        try {
            // 同步搜索词分析数据
            Map<String, Object> analyticsResult = searchTermAnalyticsService.syncSearchTermAnalyticsData(
                    accountId, profileId, marketplaceId, startDate, endDate);
            
            // 同步类目关联数据
            Map<String, Object> categoryResult = searchTermAnalyticsService.syncSearchTermCategoryRelations(
                    accountId, profileId, marketplaceId, startDate, endDate);
            
            // 同步商品排名数据
            Map<String, Object> productResult = searchTermAnalyticsService.syncSearchTermProductRankings(
                    accountId, profileId, marketplaceId, startDate, endDate);
            
            // 汇总结果
            Map<String, Object> totalResult = new HashMap<>();
            totalResult.put("success", true);
            totalResult.put("analyticsResult", analyticsResult);
            totalResult.put("categoryResult", categoryResult);
            totalResult.put("productResult", productResult);
            totalResult.put("message", "手动同步完成");
            
            log.info("手动搜索词数据同步完成");
            return totalResult;
            
        } catch (Exception e) {
            log.error("手动搜索词数据同步异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "同步异常: " + e.getMessage());
            return errorResult;
        }
    }
}
