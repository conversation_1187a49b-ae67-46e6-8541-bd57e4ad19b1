package com.yiyitech.ads.job;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.mapper.AdsAccountInfoMapper;
import com.yiyitech.ads.model.AdsAccountInfoModel;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.response.AdsProfileResponse;
import com.yiyitech.ads.service.AdsAccountProfileService;
import com.yiyitech.ads.service.AdsAccountService;
import com.yiyitech.ads.service.api.AdsProfilesApi;
import com.yiyitech.ads.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;



/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DemoTask.java
 * @Description
 * @createTime 2023年12月20日 17:57:00
 */
@Slf4j
@Component
public class AdsProfileTask {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsProfilesApi adsProfilesApi;
    @Autowired
    private AdsAccountService adsAccountService;
    @Autowired
    private AdsAccountInfoMapper adsAccountInfoMapper;
    @Autowired
    private AdsAccountProfileService adsAccountProfileService;


    @Scheduled(cron = "${job.corn.profile}")
    public void refreshAdsAccountProfile() {
        String date = DateUtil.today();
        log.info(date + "日刷新账户站点文件任务开始");
        //获取所有有效的账户信息
        List<AdsAccountInfoModel> accountInfoModelLst = adsAccountInfoMapper.searchAllAccountInfo(true);
        String accessToken = "";
        List<AdsAccountInfoModel> batchAccountLst = new ArrayList<>();
        List<AdsAccountProfileModel> batchProfileLst = new ArrayList<>();
//        Map<String, String> accoutnInfoMapping = new HashMap<>(1);
//        accoutnInfoMapping.put("id", "accoutnId");
//        Map<String, String> profileMapping = new HashMap<>(1);
//        profileMapping.put("id", "accoutnId");
        for (AdsAccountInfoModel accountInfo : accountInfoModelLst) {
            accessToken = redisUtils.getAccountAccessTokenAndRefreshToken(accountInfo.getAccountId(), CommonConstants.ADS_PREFIX_KEY);
            List<AdsProfileResponse> profileApiResponseLst = adsProfilesApi.AdsProfilesApi(accessToken);
            if(CollectionUtils.isNotEmpty(profileApiResponseLst)){
                AdsProfileResponse.accoutnInfoData apiAccountInfo = profileApiResponseLst.get(0).getAccountInfo();
                AdsAccountInfoModel updateMode = new AdsAccountInfoModel()
                        .setAccountId(apiAccountInfo.getId())
                        .setType(apiAccountInfo.getType())
                        .setName(apiAccountInfo.getName())
                        .setValidPayment(apiAccountInfo.getValidPaymentMethod())
                        .setRefreshToken(accountInfo.getRefreshToken());
                batchAccountLst.add(updateMode);
//                adsAccountInfoMapper.insertOnDuplicateKeyUpdate(updateMode);
                profileApiResponseLst.forEach(response ->{
                    AdsAccountProfileModel saveUpdateModel = new AdsAccountProfileModel()
                            .setAccountId(response.getAccountInfo().getId())
                            .setProfileId(response.getProfileId())
                            .setCountryCode(response.getCountryCode())
                            .setCurrencyCode(response.getCurrencyCode());
                    batchProfileLst.add(saveUpdateModel);
                });
            }
        }
        //更新账户信息
        adsAccountService.batchSaveOrUpdateByUniqueKey(batchAccountLst);
        //新增或更新账户站点文件信息
        adsAccountProfileService.batchSaveOrUpdateByUniqueKey(batchProfileLst);
//        adsAccountProfileMapper.batchInsert(AdsAccountProfileModel.class, saveUpdateModelLst, 100);
        log.info(date + "日刷新账户站点文件任务结束");
    }

}
