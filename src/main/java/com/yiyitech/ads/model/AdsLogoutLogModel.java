package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsLogoutLogModel.java
 * @Description
 * @createTime 2025年07月08日 11:50:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_logout_log")
public class AdsLogoutLogModel implements Serializable {
    private static final long serialVersionUID = 2376250673587083685L;

    @TableId(type = IdType.AUTO)
    private Long id;

    // 站点主键id（REFERENCES ads_account_profile）
    @TableField(value = "acc_pro_id")
    private Long accProId;

    // 账户id
    @TableField(value = "account_id")
    private String accountId;

    // 文件（站点）id
    @TableField(value = "profile_id")
    private Long profileId;

    // 用户id（REFERENCES ads_user）
    @TableField(value = "user_id")
    private Long userId;

    // 类型
    @TableField(value = "type")
    private Integer type;

    // 创建者
    @TableField("create_by")
    private String createBy;

    // 更新者
    @TableField("update_by")
    private String updateBy;

    // 创建时间
    @TableField(value = "create_time")
    private Date createTime;

    // 更新时间
    @TableField(value = "update_time")
    private Date updateTime;
}
