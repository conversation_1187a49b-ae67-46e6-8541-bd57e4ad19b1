package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsDailySearchTermReportModel.java
 * @Description
 * @createTime 2023年12月19日 15:48:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_daily_search_term_report_info")
public class AdsDailySearchTermReportModel implements Serializable, MergeableModel<AdsDailySearchTermReportModel>{
    private static final long serialVersionUID = 2856300195939320260L;

    @TableId(type = IdType.AUTO)
    private Long id;

    //报告日期
    @TableField(value = "date")
    private Date date;

    //搜索词
    @TableField(value = "search_term")
    private String searchTerm;

    //账户id（REFERENCES ads_account_info）
    @TableField(value = "account_id")
    private String accountId;

    //账户文件id（REFERENCES ads_account_profile）
    @TableField(value = "profile_id")
    private Long profileId;

    //广告组合id
    @TableField(value = "portfolio_id")
    private Long portfolioId;

    //活动id
    @TableField(value = "campaign_id")
    private String campaignId;

    //活动名称
    @TableField(value = "campaign_name")
    private String campaignName;

    //活动状态
    @TableField(value = "campaign_status")
    private String campaignStatus;

    //活动预算货币编码
    @TableField(value = "campaign_budget_currency_code")
    private String campaignBudgetCurrencyCode;

    //活动预算类型
    @TableField(value = "campaign_budget_type")
    private String campaignBudgetType;

    //活动预算金额
    @TableField(value = "campaign_budget_amount")
    private Double campaignBudgetAmount;

    //组id
    @TableField(value = "ad_group_id")
    private String adGroupId;

    //组名称
    @TableField(value = "ad_group_name")
    private String adGroupName;

    //关键字id
    @TableField(value = "keyword_id")
    private String keywordId;

    //关键字内容
    @TableField(value = "keyword")
    private String keyword;

    //关键字类型
    @TableField(value = "keyword_type")
    private String keywordType;

    //关键字状态
    @TableField(value = "keyword_status")
    private String adKeywordStatus;

    //关键字出价
    @TableField(value = "keyword_bid")
    private Double keywordBid;

    //投放目标
    @TableField(value = "targeting")
    private String targeting;

    //匹配类型（Broad广泛、Phrase词组、Exact精准）
    @TableField(value = "match_type")
    private String matchType;

    //曝光量
    @TableField(value = "impressions")
    private Integer impressions;

    //点击量
    @TableField(value = "clicks")
    private Integer clicks;

    //销售量（1d）
    @TableField(value = "purchases")
    private Integer purchases1d;

    //相同sku的销售量（1d）
    @TableField(value = "purchases_same_sku")
    private Integer purchasesSameSku1d;

    //销售额（1d）
    @TableField(value = "sales")
    private Double sales1d;

    //花费
    @TableField(value = "cost")
    private Double cost;

    //单次点击成本（CPC）
    @TableField(value = "cost_per_click")
    private Double costPerClick;

    //点击率（CTR）
    @TableField(value = "click_through_rate")
    private Double clickThroughRate;

    //单位销售点击量（1d）
    @TableField(value = "units_sold_clicks")
    private Integer unitsSoldClicks1d;

    //相同Sku的单位销量（1d）
    @TableField(value = "units_sold_same_sku")
    private Integer unitsSoldSameSku1d;

    //相同sku的归因销售额（1d）
    @TableField(value = "attributed_sales_same_sku")
    private Double attributedSalesSameSku1d;

    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    public AdsDailySearchTermReportModel(){}

    public AdsDailySearchTermReportModel(Integer impressions, Integer clicks, Double cost, Double sales1d, Integer purchases1d){
        this.impressions = impressions;
        this.clicks = clicks;
        this.cost = cost;
        this.sales1d = sales1d;
        this.purchases1d = purchases1d;
    }

    @Override
    public AdsDailySearchTermReportModel mergeGroup(List<AdsDailySearchTermReportModel> group) {
        if (group == null || group.isEmpty()) return null;
        AdsDailySearchTermReportModel first = group.get(0);

        AdsDailySearchTermReportModel merged = new AdsDailySearchTermReportModel();
        merged.setAccountId(first.getAccountId());
        merged.setProfileId(first.getProfileId());
        merged.setCampaignId(first.getCampaignId());
        merged.setCampaignName(first.getCampaignName());
        merged.setAdGroupId(first.getAdGroupId());
        merged.setAdGroupName(first.getAdGroupName());
        merged.setKeywordId(first.getKeywordId());
        merged.setKeyword(first.getKeyword());
        merged.setKeywordType(first.getKeywordType());
        merged.setSearchTerm(first.getSearchTerm());
        merged.setMatchType(first.getMatchType());
        merged.setTargeting(first.getTargeting());

        //int求和
        merged.setImpressions(group.stream().mapToInt(e -> Optional.ofNullable(e.getImpressions()).orElse(0)).sum());
        merged.setClicks(group.stream().mapToInt(e -> Optional.ofNullable(e.getClicks()).orElse(0)).sum());
        merged.setPurchases1d(group.stream().mapToInt(e -> Optional.ofNullable(e.getPurchases1d()).orElse(0)).sum());
        merged.setPurchasesSameSku1d(group.stream().mapToInt(e -> Optional.ofNullable(e.getPurchasesSameSku1d()).orElse(0)).sum());
        merged.setUnitsSoldClicks1d(group.stream().mapToInt(e -> Optional.ofNullable(e.getUnitsSoldClicks1d()).orElse(0)).sum());
        merged.setUnitsSoldSameSku1d(group.stream().mapToInt(e -> Optional.ofNullable(e.getUnitsSoldSameSku1d()).orElse(0)).sum());

        // BigDecimal 精确求和
        merged.setSales1d(sumDoubleField(group, AdsDailySearchTermReportModel::getSales1d));
        merged.setCost(sumDoubleField(group, AdsDailySearchTermReportModel::getCost));
        merged.setAttributedSalesSameSku1d(sumDoubleField(group, AdsDailySearchTermReportModel::getAttributedSalesSameSku1d));

        // 以下字段可选处理
        merged.setClickThroughRate(null);
        merged.setCostPerClick(null);
        merged.setCampaignBudgetAmount(null);

        return merged;
    }

    private double sumDoubleField(List<AdsDailySearchTermReportModel> group, Function<AdsDailySearchTermReportModel, Double> getter) {
        BigDecimal sum = group.stream()
                .map(e -> BigDecimal.valueOf(Optional.ofNullable(getter.apply(e)).orElse(0.0)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return sum.setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

}
