package com.yiyitech.ads.model.request;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsPortfolioRequest.java
 * @Description
 * @createTime 2025年02月18日 20:38:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsPortfolioSearchRequest {
    private AdsPortfolioSearchRequest.StateFilterData stateFilter;
    private boolean includeExtendedDataFields;

    @Getter
    @Setter
    public static class StateFilterData{
        private List<String> include;
    }
}
