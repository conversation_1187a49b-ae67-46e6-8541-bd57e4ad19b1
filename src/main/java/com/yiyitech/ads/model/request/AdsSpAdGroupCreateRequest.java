package com.yiyitech.ads.model.request;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpAdGroupCreateRequest.java
 * @Description
 * @createTime 2025年02月19日 18:25:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsSpAdGroupCreateRequest {
    private List<AdsSpAdGroupCreateRequest.AdGroupsData> adGroups;

    @Getter
    @Setter
    public static class AdGroupsData{
        private String campaignId;
        private String name;
        private String state;
        private double defaultBid;
    }
}
