package com.yiyitech.ads.model.request;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpKeywordsCreateRequest.java
 * @Description
 * @createTime 2025年02月11日 19:47:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsSpKeywordsCreateRequest {
    private List<AdsSpKeywordsCreateRequest.KeywordData> keywords;

    @Getter
    @Setter
    public static class KeywordData{
        private String campaignId;
        private String matchType;
        private String state;
        private double bid;
        private String adGroupId;
        private String keywordText;
    }

}
