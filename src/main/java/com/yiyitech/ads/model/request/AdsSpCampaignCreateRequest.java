package com.yiyitech.ads.model.request;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpCampaignCreateRequest.java
 * @Description
 * @createTime 2025年02月19日 17:07:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsSpCampaignCreateRequest {
    private List<AdsSpCampaignCreateRequest.CampaignData> campaigns;

    @Getter
    @Setter
    public static class CampaignData{
        private String name;
        private String portfolioId;
        private AdsSpCampaignCreateRequest.BudgetData budget;
        private String startDate;
        private String endDate;
        private String state;
        private String targetingType;
        private AdsSpCampaignCreateRequest.DynamicBiddingData dynamicBidding;
    }

    @Getter
    @Setter
    public static class BudgetData{
        private String budgetType;
        private double budget;
    }

    @Getter
    @Setter
    public static class DynamicBiddingData{
        private String strategy;
        private List<AdsSpCampaignCreateRequest.PlacementBiddingData> placementBidding;
    }

    @Getter
    @Setter
    public static class PlacementBiddingData{
        private int percentage;
        private String placement;
    }
}
