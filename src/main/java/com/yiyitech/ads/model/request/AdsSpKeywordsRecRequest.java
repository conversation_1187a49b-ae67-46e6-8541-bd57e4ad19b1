package com.yiyitech.ads.model.request;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpKeywordsRecRequest.java
 * @Description
 * @createTime 2025年02月11日 19:46:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsSpKeywordsRecRequest {
    private String campaignId;
    private String recommendationType;
    private String adGroupId;
    private List<AdsSpKeywordsRecRequest.TargetsData> targets;
    private String maxRecommendations;
    private String sortDimension;
    private String locale;

    @Getter
    @Setter
    public static class TargetsData{
        private String matchType;
        private String keyword;
        private boolean userSelectedKeyword;
    }

}
