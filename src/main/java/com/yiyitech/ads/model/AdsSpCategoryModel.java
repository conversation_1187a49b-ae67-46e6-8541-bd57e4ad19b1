package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpCategoryModel.java
 * @Description
 * @createTime 2025年02月17日 16:46:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_sp_category")
public class AdsSpCategoryModel implements Serializable {
    private static final long serialVersionUID = -7863691749491020850L;

    @TableId(type = IdType.AUTO)
    private Long id;
    //账户id（REFERENCES ads_account_info）
    @TableField(value = "account_id")
    private String accoutnId;
    //账户文件id（REFERENCES ads_account_profile）
    @TableField(value = "profile_id")
    private Long profileId;
    //品类id
    @TableField(value = "category_id")
    private Long categoryId;
    //品类名称
    @TableField(value = "category_name")
    private String categoryName;
    //父类id（品类的父品类）
    @TableField(value = "parent_id")
    private Long parentId;
    //创建者
    @TableField(value = "create_by")
    private String createBy;
    //更新者
    @TableField(value = "update_by")
    private String updateBy;
    //创建时间
    @TableField(value = "create_time")
    private Date createTime;
    //更新时间
    @TableField(value = "update_time")
    private Date updateTime;
}
