package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 搜索词分析报告模型
 * 用于存储从Amazon SP-API获取的搜索词分析数据
 * 包含搜索词的排名、搜索量、增长率等关键指标
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermAnalyticsModel.java
 * @Description 搜索词分析数据模型，对应Amazon SP-API的Search Query Performance Report
 * @createTime 2025年01月31日
 */
@Data
@Accessors(chain = true)
@TableName("ads_search_term_analytics")
public class AdsSearchTermAnalyticsModel implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    // 账户id（REFERENCES ads_account_info）
    @TableField(value = "account_id")
    private String accountId;

    // 账户文件id（REFERENCES ads_account_profile）
    @TableField(value = "profile_id")
    private Long profileId;

    // 市场ID
    @TableField(value = "marketplace_id")
    private String marketplaceId;

    // 报告日期
    @TableField(value = "report_date")
    private Date reportDate;

    // 搜索词
    @TableField(value = "search_term")
    private String searchTerm;

    // 搜索词排名
    @TableField(value = "search_rank")
    private Integer searchRank;

    // 月搜索量
    @TableField(value = "monthly_search_volume")
    private Long monthlySearchVolume;

    // 上月搜索量
    @TableField(value = "previous_month_search_volume")
    private Long previousMonthSearchVolume;

    // 月搜索量环比增长率（百分比）
    @TableField(value = "search_volume_growth_rate")
    private Double searchVolumeGrowthRate;

    // 点击量
    @TableField(value = "clicks")
    private Integer clicks;

    // 展示量
    @TableField(value = "impressions")
    private Integer impressions;

    // 点击率（CTR）
    @TableField(value = "click_through_rate")
    private Double clickThroughRate;

    // 转化率
    @TableField(value = "conversion_rate")
    private Double conversionRate;

    // 平均点击成本
    @TableField(value = "average_cost_per_click")
    private Double averageCostPerClick;

    // 搜索频率排名（1-高频，2-中频，3-低频）
    @TableField(value = "search_frequency_rank")
    private Integer searchFrequencyRank;

    // 竞争强度（1-低，2-中，3-高）
    @TableField(value = "competition_intensity")
    private Integer competitionIntensity;

    // 季节性指数
    @TableField(value = "seasonality_index")
    private Double seasonalityIndex;

    // 相关性得分
    @TableField(value = "relevance_score")
    private Double relevanceScore;

    // 数据来源（SP_API_SEARCH_QUERY_PERFORMANCE, SP_API_SEARCH_TERMS等）
    @TableField(value = "data_source")
    private String dataSource;

    // 数据状态（ACTIVE-有效，INACTIVE-无效，PENDING-待处理）
    @TableField(value = "status")
    private String status;

    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 计算月搜索量环比增长率
     * @return 增长率百分比
     */
    public Double calculateGrowthRate() {
        if (previousMonthSearchVolume == null || previousMonthSearchVolume == 0) {
            return null;
        }
        if (monthlySearchVolume == null) {
            return null;
        }
        return ((double) (monthlySearchVolume - previousMonthSearchVolume) / previousMonthSearchVolume) * 100;
    }

    /**
     * 判断是否为高频搜索词
     * @return true-高频，false-非高频
     */
    public boolean isHighFrequencySearchTerm() {
        return searchFrequencyRank != null && searchFrequencyRank == 1;
    }

    /**
     * 判断是否为高竞争搜索词
     * @return true-高竞争，false-非高竞争
     */
    public boolean isHighCompetitionSearchTerm() {
        return competitionIntensity != null && competitionIntensity == 3;
    }
}
