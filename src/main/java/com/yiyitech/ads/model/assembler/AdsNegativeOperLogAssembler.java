package com.yiyitech.ads.model.assembler;

import com.yiyitech.ads.enums.AdsMatchTypeEnum;
import com.yiyitech.ads.enums.AdsNegJudgeTypeEnum;
import com.yiyitech.ads.enums.AdsNegLogStatusEnum;
import com.yiyitech.ads.model.AdsMonitorRulesModel;
import com.yiyitech.ads.model.AdsNegativeOperLogModel;
import com.yiyitech.ads.model.AdsSvcConfigurationModel;
import com.yiyitech.ads.vo.AdsAutoNegSearchTerm;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperLogAssembler.java
 * @Description
 * @createTime 2025年07月16日 14:45:00
 */
public class AdsNegativeOperLogAssembler {

    public static AdsNegativeOperLogModel build(Long PortfolioId, String accountId, Long profileId, String campaignId,
                                                String campaignName, String adGroupId, String adGroupName, String uniqueSign,
                                                String value, Integer negativeType, Integer source, String parentAsin,
                                                String asin, String sku, String picture, String itemName, Integer operLevel,
                                                Integer operType, String beforeOper, String afterOper, Integer judgeType, Integer status) {
        AdsNegativeOperLogModel negativeOperLogModel = new AdsNegativeOperLogModel();
        //portfolio_id
        negativeOperLogModel.setPortfolioId(PortfolioId);
        //account_id
        negativeOperLogModel.setAccountId(accountId);
        //profile_id
        negativeOperLogModel.setProfileId(profileId);
        //campaign_id
        negativeOperLogModel.setCampaignId(campaignId);
        //campaign_name
        negativeOperLogModel.setCampaignName(campaignName);
        //ad_group_id
        negativeOperLogModel.setAdGroupId(adGroupId);
        //ad_group_name
        negativeOperLogModel.setAdGroupName(adGroupName);
        //negative_value_sign
        negativeOperLogModel.setNegativeValueSign(uniqueSign);
        //negative_value
        negativeOperLogModel.setNegativeValue(value);
        //negative_type
        negativeOperLogModel.setNegativeType(negativeType);
        //source
        negativeOperLogModel.setSource(source);
        //parent_asin
        negativeOperLogModel.setParentAsin(parentAsin);
        //asin
        negativeOperLogModel.setAsin(asin);
        //sku
        negativeOperLogModel.setSku(sku);
        //picture_url
        negativeOperLogModel.setPictureUrl(picture);
        //asin_Item_name
        negativeOperLogModel.setAsinItemName(itemName);
        //oper_level
        negativeOperLogModel.setOperLevel(operLevel);
        //oper_type
        negativeOperLogModel.setOperType(operType);
        //before_oper
        negativeOperLogModel.setBeforeOper(beforeOper);
        //after_oper
        negativeOperLogModel.setAfterOper(afterOper);
        //judge_type
        negativeOperLogModel.setJudgeType(judgeType);
        //judge_type
        negativeOperLogModel.setStatus(status);
        return negativeOperLogModel;
    }
}

