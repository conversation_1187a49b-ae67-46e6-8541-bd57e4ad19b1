package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsConditionsFieldModel.java
 * @Description
 * @createTime 2025年06月27日 15:28:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_conditions_field")
public class AdsConditionsFieldModel implements Serializable {
    private static final long serialVersionUID = -3583649522702393708L;

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("field")
    private String field;

    @TableField("field_name")
    private String fieldName;

    @TableField("status")
    private Integer status;

    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

