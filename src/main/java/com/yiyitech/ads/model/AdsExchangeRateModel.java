package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsExchangeRateModel.java
 * @Description
 * @createTime 2024年01月24日 17:52:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_exchange_rate")
public class AdsExchangeRateModel implements Serializable {
    private static final long serialVersionUID = 8074142925781375161L;

    @TableId(type = IdType.AUTO)
    private Long id;
    //原始币种
    @TableField(value = "original_currency_code")
    private String originalCurrencyCode;
    //目标币种
    @TableField(value = "target_currency_code")
    private String targetCurrencyCode;
    //标准汇率
    @TableField(value = "standard_exchange_rate")
    private String sExchangeRate;
    //自定义汇率
    @TableField(value = "custom_exchange_rate")
    private String cExchangeRate;
    //生效日期
    @TableField(value = "effect_date")
    private String effectDate;
    //汇率状态（0无效、1有效）
    @TableField(value = "status")
    private Boolean status;
    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private java.util.Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
