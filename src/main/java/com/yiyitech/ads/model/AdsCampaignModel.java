package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AmCampaignModel.java
 * @Description
 * @createTime 2023年11月23日 13:36:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_campaign")
public class AdsCampaignModel implements Serializable {
    private static final long serialVersionUID = 7590377604565845345L;

    //主键id
    @TableId(type = IdType.AUTO)
    private Long id;
    //账户id（REFERENCES ads_account_info）
    @TableField(value = "account_id")
    private String accountId;
    //账户文件id（REFERENCES ads_account_profile）
    @TableField(value = "profile_id")
    private Long profileId;
    //广告活动组合id
    @TableField(value = "portfolio_id")
    private String portfolioId;
    //活动id
    @TableField(value = "campaign_id")
    private String campaignId;
    //活动名称
    private String name;
    //开始日期
    @TableField(value = "start_date")
    private String startDate;
    //开始日期
    @TableField(value = "end_date")
    private Date endDate;
    //活动状态（ENABLED启用、PAUSED暂停）
    @TableField(value = "state")
    private String state;
    //目标类型（MANUAL手动、AUTO自动）
    @TableField(value = "targeting_type")
    private String targetingType;
    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private java.util.Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private java.util.Date updateTime;

}
