package com.yiyitech.ads.model.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SpCatalogItemResponse.java
 * @Description
 * @createTime 2025年07月06日 18:50:00
 */
@Data
public class SpCatalogItemResponse {
    private String asin;
    private List<MarketplaceImages> images;
    private List<MarketplaceVariations> variations;
    private List<ItemSummary> summaries;
    // 其他字段

    // 获取主图URL的方法
    public String getMainImageUrl() {
        if (this.images == null) return null;

        for (MarketplaceImages marketplaceImages : this.images) {
            if (marketplaceImages.getImages() != null) {
                for (CatalogImage image : marketplaceImages.getImages()) {
                    if ("MAIN".equals(image.getVariant())) {
                        return image.getLink();
                    }
                }
            }
        }
        return null;
    }

    // 获取父ASIN的方法
    public String getParentAsin() {
        if (this.variations == null) return null;

        for (MarketplaceVariations variation : this.variations) {
            if ("PARENT".equals(variation.getType()) &&
                    variation.getAsins() != null &&
                    !variation.getAsins().isEmpty()) {
                return variation.getAsins().get(0);
            }
        }
        return null;
    }

    // 获取商品标题
    public String getTitle() {
        if (this.summaries == null) return null;
        for (ItemSummary summary : summaries) {
            if (summary.getItemName() != null) {
                return summary.getItemName();
            }
        }
        return null;
    }

    @Data
    public static class MarketplaceImages {
        private String marketplaceId;
        private List<CatalogImage> images;
    }

    @Data
    public static class CatalogImage {
        private String variant;  // MAIN, PT1, etc.
        private String link;      // 图片URL
        private Integer height;
        private Integer width;
    }

    @Data
    public static class MarketplaceVariations {
        private String marketplaceId;
        private List<String> asins;  // 子ASIN或父ASIN列表
        private String type;         // PARENT或CHILD
    }

    @Data
    public static class ItemSummary {
        private String marketplaceId;
        private String itemName;
        // 可根据需要加更多字段，如 browseClassification 等
    }
}



