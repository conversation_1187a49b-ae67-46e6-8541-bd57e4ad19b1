package com.yiyitech.ads.model.response;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpCampaignsListResponse.java
 * @Description
 * @createTime 2023年12月11日 15:09:00
 */
@Getter
@Setter
public class AdsSpCampaignsListResponse {
    private List<AdsSpCampaignsListResponse.CampaignsData> campaigns;
    private Integer totalResults;

    @Getter
    @Setter
    public static class CampaignsData{
        private String portfolioId;
        private String campaignId;
        private String name;
        private String startDate;
        private String state;
        private String targetingType;
    }
}
