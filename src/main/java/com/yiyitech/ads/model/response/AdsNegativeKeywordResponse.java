package com.yiyitech.ads.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeKeywordResponse.java
 * @Description
 * @createTime 2025年07月07日 11:22:00
 */
@Data
public class AdsNegativeKeywordResponse {

    @JsonProperty("negativeKeywords")
    private NegativeKeywordsResult negativeKeywords;

    @Data
    public static class NegativeKeywordsResult {
        private List<NegativeKeywordSuccess> success;
        private List<NegativeKeywordError> error;
    }

    @Data
    public static class NegativeKeywordSuccess {
        private int index;

        @JsonProperty("negativeKeyword")
        private NegativeKeywordEntity negativeKeyword;

        private String negativeKeywordId;
    }

    @Data
    public static class NegativeKeywordEntity {
        private String keywordId;
        private String nativeLanguageKeyword;
        private String nativeLanguageLocale;
        private String campaignId;
        private String matchType;
        private String state;
        private String adGroupId;
        private String keywordText;
        private Map<String, Object> extendedData;
    }

    @Data
    public static class NegativeKeywordError {
        private int index;
        private List<KeywordErrorDetail> errors;
    }

    @Data
    public static class KeywordErrorDetail {
        private String errorType;
        private ErrorValue errorValue;
    }

    @Data
    public static class ErrorValue {
        private MalformedValueError malformedValueError;
        private EntityStateError entityStateError;
        private MissingValueError missingValueError;
        private DuplicateValueError duplicateValueError;
        private RangeError rangeError;
        private ParentEntityError parentEntityError;
        private OtherError otherError;
        private ThrottledError throttledError;
        private EntityNotFoundError entityNotFoundError;
        private TargetingClauseSetupError targetingClauseSetupError;
        private BillingError billingError;
        private EntityQuotaError entityQuotaError;
        private InternalServerError internalServerError;
    }

    //错误类型类（后续根据实际需要以及返回只保留常用项）
    @Data public static class MalformedValueError { private String message; }
    @Data public static class EntityStateError { private String message; }
    @Data public static class MissingValueError { private String message; }
    @Data public static class DuplicateValueError { private String message; }
    @Data public static class RangeError { private String message; }
    @Data public static class ParentEntityError { private String message; }
    @Data public static class OtherError { private String message; }
    @Data public static class ThrottledError { private String message; }
    @Data public static class EntityNotFoundError { private String message; }
    @Data public static class TargetingClauseSetupError { private String message; }
    @Data public static class BillingError { private String message; }
    @Data public static class EntityQuotaError { private String message; }
    @Data public static class InternalServerError { private String message; }
}
