package com.yiyitech.ads.model.response;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpKeywordsRecResponse.java
 * @Description
 * @createTime 2025年02月11日 19:48:00
 */
@Getter
@Setter
public class AdsSpKeywordsRecResponse {
    private List<AdsSpKeywordsRecResponse.KeywordTargetData> keywordTargetList;

    @Getter
    @Setter
    public static class KeywordTargetData{
        private String keyword;
        private List<AdsSpKeywordsRecResponse.BidInfoData> bidInfo;
//        private String translation;
        private String searchTermImpressionRank;
        private String searchTermImpressionShare;
        private String recId;
    }

    @Getter
    @Setter
    public static class BidInfoData{
        private String matchType;
        private int rank;
        private double bid;
        private List<AdsSpKeywordsRecResponse.SuggestedBidData> suggestedBid;
    }

    @Getter
    @Setter
    public static class SuggestedBidData{
        private double rangeStart;
        private double rangeMedian;
        private double rangeEnd;
    }
}
