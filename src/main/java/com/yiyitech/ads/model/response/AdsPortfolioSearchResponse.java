package com.yiyitech.ads.model.response;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsPortfolioResponse.java
 * @Description
 * @createTime 2025年02月18日 20:36:00
 */
@Getter
@Setter
public class AdsPortfolioSearchResponse {
    private List<AdsPortfolioSearchResponse.PortfoliosData> portfolios;
    private int totalResults;

    @Getter
    @Setter
    public static class PortfoliosData{
        private AdsPortfolioSearchResponse.BudgetData budget;
        private AdsPortfolioSearchResponse.ExtendedData extendedData;
        private boolean inBudget;
        private String name;
        private String portfolioId;
        private String state;
    }

    @Getter
    @Setter
    public static class BudgetData{
        private String currencyCode;
        private String policy;
    }

    @Getter
    @Setter
    public static class ExtendedData{
        private String creationDateTime;
        private String lastUpdateDateTime;
        private String servingStatus;
    }
}
