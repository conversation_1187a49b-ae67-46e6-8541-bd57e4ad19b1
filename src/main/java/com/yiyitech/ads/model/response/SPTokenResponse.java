package com.yiyitech.ads.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SPTokenResponse.java
 * @Description
 * @createTime 2025年06月18日 15:48:00
 */
@Getter
@Setter
public class SPTokenResponse {
    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("refresh_token")
    private String refreshToken;

    @JsonProperty("expires_in")
    private int expiresIn;

    @JsonProperty("token_type")
    private String tokenType;
}
