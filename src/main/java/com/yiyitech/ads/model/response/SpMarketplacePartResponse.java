package com.yiyitech.ads.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SpMarketplacePartResponse.java
 * @Description
 * @createTime 2025年06月19日 19:15:00
 */
@Data
public class SpMarketplacePartResponse {
    private List<Participation> payload;

    @Data
    public static class Participation {
        private Marketplace marketplace;
        private ParticipationDetail participation;
        private String storeName;
//        private Seller seller;

    }

    @Data
    public static class Marketplace {
        private String id;
        private String countryCode;
        private String name;
    }

    @Data
    public static class Seller {
        private String sellerId;
        private boolean hasSuspendedListings;
    }

    @Data
    public static class ParticipationDetail {
        private boolean isParticipating;
        private boolean hasSuspendedListings;
    }
}