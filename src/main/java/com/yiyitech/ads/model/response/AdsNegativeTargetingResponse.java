package com.yiyitech.ads.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeTargetingResponse.java
 * @Description
 * @createTime 2025年07月07日 11:55:00
 */
@Data
public class AdsNegativeTargetingResponse {

    @JsonProperty("negativeTargetingClauses")
    private NegativeTargetingResult negativeTargetingClauses;

    @Data
    public static class NegativeTargetingResult {
        private List<NegativeTargetSuccess> success;
        private List<NegativeTargetError> error;
    }

    @Data
    public static class NegativeTargetSuccess {
        private String targetId;
        private int index;

        @JsonProperty("negativeTargetingClause")
        private NegativeTargetingClause clause;
    }

    @Data
    public static class NegativeTargetingClause {
        private List<Expression> expression;
        private String targetId;
        private List<Expression> resolvedExpression;
        private String campaignId;
        private String state;
        private String adGroupId;
        private ExtendedData extendedData;
    }

    @Data
    public static class Expression {
        private String type;
        private String value;
    }

    @Data
    public static class ExtendedData {
        private String lastUpdateDateTime;
        private String creationDateTime;
        private String servingStatus;
        private List<ServingStatusDetail> servingStatusDetails;
    }

    @Data
    public static class ServingStatusDetail {
        private String name;
        private String helpUrl;
        private String message;
    }

    @Data
    public static class NegativeTargetError {
        private int index;
        private List<NegativeTargetErrorDetail> errors;
    }

    @Data
    public static class NegativeTargetErrorDetail {
        private String errorType;
        private ErrorValue errorValue;
    }

    @Data
    public static class ErrorValue {
        private EntityStateError entityStateError;
        private MissingValueError missingValueError;
        private DuplicateValueError duplicateValueError;
        private RangeError rangeError;
        private ParentEntityError parentEntityError;
        private OtherError otherError;
        private ThrottledError throttledError;
        private EntityNotFoundError entityNotFoundError;
        private TargetingClauseSetupError targetingClauseSetupError;
        private MalformedValueError malformedValueError;
        private BillingError billingError;
        private EntityQuotaError entityQuotaError;
        private InternalServerError internalServerError;
    }

    // 简化版错误类型类
    @Data public static class EntityStateError { private String message; }
    @Data public static class MissingValueError { private String message; }
    @Data public static class DuplicateValueError { private String message; }
    @Data public static class RangeError { private String message; }
    @Data public static class ParentEntityError { private String message; }
    @Data public static class OtherError { private String message; }
    @Data public static class ThrottledError { private String message; }
    @Data public static class EntityNotFoundError { private String message; }
    @Data public static class TargetingClauseSetupError { private String message; }
    @Data public static class MalformedValueError { private String message; }
    @Data public static class BillingError { private String message; }
    @Data public static class EntityQuotaError { private String message; }
    @Data public static class InternalServerError { private String message; }
}