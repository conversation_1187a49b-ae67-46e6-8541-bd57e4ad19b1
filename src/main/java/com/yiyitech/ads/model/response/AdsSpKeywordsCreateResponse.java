package com.yiyitech.ads.model.response;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpKeywordsCreateResponse.java
 * @Description
 * @createTime 2025年02月12日 11:11:00
 */
public class AdsSpKeywordsCreateResponse {
    private List<AdsSpKeywordsCreateResponse.KeywordsData> keywords;

    @Getter
    @Setter
    public static class KeywordsData{
        private List<AdsSpKeywordsCreateResponse.ErrorData> error;
        private List<AdsSpKeywordsCreateResponse.SuccessData> success;
    }

    @Getter
    @Setter
    public static class ErrorData{
        private List<AdsSpKeywordsCreateResponse.ErrorsData> errors;
        private int index;
    }

    @Getter
    @Setter
    public static class ErrorsData{
        private String errorType;
        private AdsSpKeywordsCreateResponse.ErrorValueData errorValue;
    }

    @Getter
    @Setter
    public static class ErrorValueData{
        private AdsSpKeywordsCreateResponse.DuplicateValueErrorData duplicateValueError;
    }

    @Getter
    @Setter
    public static class DuplicateValueErrorData{
        private String message;
        private String reason;
    }


    @Getter
    @Setter
    public static class SuccessData{
        private int index;
        private String keywordId;
    }
}
