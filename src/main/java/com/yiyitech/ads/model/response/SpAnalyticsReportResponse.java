package com.yiyitech.ads.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Amazon SP-API 分析报告响应模型
 * 用于接收Amazon SP-API分析报告的响应数据
 * 包括报告创建、状态查询、数据下载等操作的响应
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName SpAnalyticsReportResponse.java
 * @Description Amazon SP-API分析报告响应模型
 * @createTime 2025年01月31日
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpAnalyticsReportResponse {
    
    // 报告ID
    private String reportId;
    
    // 报告类型
    private String reportType;
    
    // 处理状态 (IN_QUEUE, IN_PROGRESS, DONE, CANCELLED, FATAL)
    private String processingStatus;
    
    // 报告创建时间
    private String createdTime;
    
    // 处理开始时间
    private String processingStartTime;
    
    // 处理结束时间
    private String processingEndTime;
    
    // 数据开始时间
    private String dataStartTime;
    
    // 数据结束时间
    private String dataEndTime;
    
    // 市场ID列表
    private List<String> marketplaceIds;
    
    // 报告文档ID（用于下载报告数据）
    private String reportDocumentId;
    
    // 报告选项
    private ReportOptions reportOptions;
    
    // 错误信息（当处理失败时）
    private String errorMessage;
    
    // 报告大小（字节）
    private Long reportSize;
    
    // 报告行数
    private Integer recordCount;
    
    /**
     * 报告选项内部类
     */
    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReportOptions {
        // 报告周期 (DAY, WEEK, MONTH)
        private String reportPeriod;
        
        // 日期粒度
        private String dateGranularity;
        
        // ASIN粒度
        private String asinGranularity;
        
        // 分销商视图
        private String distributorView;
        
        // 销售程序
        private String sellingProgram;
    }
    
    /**
     * 检查报告是否处理完成
     * @return true-已完成，false-未完成
     */
    public boolean isProcessingCompleted() {
        return "DONE".equals(processingStatus);
    }
    
    /**
     * 检查报告是否处理失败
     * @return true-失败，false-未失败
     */
    public boolean isProcessingFailed() {
        return "FATAL".equals(processingStatus) || "CANCELLED".equals(processingStatus);
    }
    
    /**
     * 检查报告是否正在处理中
     * @return true-处理中，false-非处理中
     */
    public boolean isProcessing() {
        return "IN_QUEUE".equals(processingStatus) || "IN_PROGRESS".equals(processingStatus);
    }
    
    /**
     * 获取处理状态描述
     * @return 状态描述
     */
    public String getProcessingStatusDescription() {
        if (processingStatus == null) {
            return "未知状态";
        }
        
        switch (processingStatus) {
            case "IN_QUEUE":
                return "排队中";
            case "IN_PROGRESS":
                return "处理中";
            case "DONE":
                return "已完成";
            case "CANCELLED":
                return "已取消";
            case "FATAL":
                return "处理失败";
            default:
                return "未知状态: " + processingStatus;
        }
    }
    
    /**
     * 检查是否可以下载报告数据
     * @return true-可下载，false-不可下载
     */
    public boolean canDownload() {
        return isProcessingCompleted() && reportDocumentId != null && !reportDocumentId.trim().isEmpty();
    }
}
