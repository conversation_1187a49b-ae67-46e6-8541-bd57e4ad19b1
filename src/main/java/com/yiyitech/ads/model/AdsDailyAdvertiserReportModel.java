package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsDailyAdvertiserReportModel.java
 * @Description
 * @createTime 2025年07月05日 18:29:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_daily_advertiser_report_info")
public class AdsDailyAdvertiserReportModel implements Serializable {
    private static final long serialVersionUID = 7422228050521589037L;

    @TableId(type = IdType.AUTO)
    private Long id;

    // 报告日期
    @TableField(value = "date")
    private Date date;

    // 广告产品的父asin
    @TableField("advertised_parent_asin")
    private String advertisedParentAsin;

    // 广告产品asin
    @TableField(value = "advertised_asin")
    private String advertisedAsin;

    // 广告产品sku
    @TableField(value = "advertised_sku")
    private String advertisedSku;

    // 商品asin的名称
    @TableField(value = "advertised_Item_name")
    private String advertisedItemName;

    // 商品图片url
    @TableField("picture_url")
    private String pictureUrl;

    // 广告产品投放实例id（adId）
    @TableField(value = "ad_id")
    private String adId;

    // 账户id（REFERENCES ads_account_info）
    @TableField(value = "account_id")
    private String accountId;

    // 账户文件id（REFERENCES ads_account_profile）
    @TableField(value = "profile_id")
    private Long profileId;

    // 广告组合id
    @TableField(value = "portfolio_id")
    private Long portfolioId;

    // 活动id
    @TableField(value = "campaign_id")
    private String campaignId;

    // 活动名称
    @TableField(value = "campaign_name")
    private String campaignName;

    // 活动状态
    @TableField(value = "campaign_status")
    private String campaignStatus;

    // 活动预算货币编码
    @TableField(value = "campaign_budget_currency_code")
    private String campaignBudgetCurrencyCode;

    // 活动预算类型
    @TableField(value = "campaign_budget_type")
    private String campaignBudgetType;

    // 活动预算金额
    @TableField(value = "campaign_budget_amount")
    private Double campaignBudgetAmount;

    // 广告组id
    @TableField(value = "ad_group_id")
    private String adGroupId;

    // 广告组名称
    @TableField(value = "ad_group_name")
    private String adGroupName;

    // 关键字id
    @TableField(value = "keyword_id")
    private String keywordId;

    // 关键字内容
    @TableField(value = "keyword")
    private String keyword;

    // 曝光量
    @TableField(value = "impressions")
    private Integer impressions;

    // 点击量
    @TableField(value = "clicks")
    private Integer clicks;

    // 销售量（1d）
    @TableField(value = "purchases")
    private Integer purchases1d;

    // 相同sku的销售量（1d）
    @TableField(value = "purchases_same_sku")
    private Integer purchasesSameSku1d;

    // 销售额（1d）
    @TableField(value = "sales")
    private Double sales1d;

    // 花费
    @TableField(value = "cost")
    private Double cost;

    // 单次点击成本（CPC）
    @TableField(value = "cost_per_click")
    private Double costPerClick;

    // 点击率（CTR）
    @TableField(value = "click_through_rate")
    private Double clickThroughRate;

    // 单位销售点击量（1d）
    @TableField(value = "units_sold_clicks")
    private Integer unitsSoldClicks1d;

    // 相同Sku的单位销量（1d）
    @TableField(value = "units_sold_same_sku")
    private Integer unitsSoldSameSku1d;

    // 相同sku的归因销售额（1d）
    @TableField(value = "attributed_sales_same_sku")
    private Double attributedSalesSameSku1d;

    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    public AdsDailyAdvertiserReportModel(){}

    public AdsDailyAdvertiserReportModel(Integer impressions, Integer clicks, Double cost, Double sales1d, Integer purchases1d){
        this.impressions = impressions;
        this.clicks = clicks;
        this.cost = cost;
        this.sales1d = sales1d;
        this.purchases1d = purchases1d;
    }
}
