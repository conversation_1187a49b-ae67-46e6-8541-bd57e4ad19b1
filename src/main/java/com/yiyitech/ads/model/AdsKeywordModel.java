package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AmKeywordModel.java
 * @Description
 * @createTime 2023年12月06日 14:38:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_keyword")
public class AdsKeywordModel implements Serializable {
    private static final long serialVersionUID = -355779872105657736L;

    //主键id
    @TableId(type = IdType.AUTO)
    private Long id;
    //账户id（REFERENCES ads_account_info）
    @TableField(value = "account_id")
    private String accoutnId;
    //账户文件id（REFERENCES ads_account_profile）
    @TableField(value = "profile_id")
    private Long profileId;
    //活动id
    @TableField(value = "campaign_id")
    private String campaignId;
    //活动组id
    @TableField(value = "ad_group_id")
    private String adGroupId;
    //关键词id
    @TableField(value = "keyword_id")
    private String keywordId;
    //关键字内容
    @TableField(value = "keyword_text")
    private String keywordText;
    //关键字匹配类型（Broad广泛、Phrase词组、Exact精准）
    @TableField(value = "match_type")
    private String matchType;
    //关键字状态（ENABLED启用、PAUSED暂停）
    @TableField(value = "state")
    private String state;
    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private java.util.Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
