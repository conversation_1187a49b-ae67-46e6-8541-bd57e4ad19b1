package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAccountProfileModel.java
 * @Description
 * @createTime 2023年12月07日 17:34:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_account_profile")
public class AdsAccountProfileModel implements Serializable {
    private static final long serialVersionUID = 5656257763147149839L;

    @TableId(type = IdType.AUTO)
    private Long id;
    //账户id
    @TableField(value = "account_id")
    private String accountId;
    //账户文件id
    @TableField(value = "profile_id")
    private Long profileId;
    //站点名称
    @TableField(value = "name")
    private String name;
    //国家编码
    @TableField(value = "country_code")
    private String countryCode;
    //货币编码
    @TableField(value = "currency_code")
    private String currencyCode;
    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
