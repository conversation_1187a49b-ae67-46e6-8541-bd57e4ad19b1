package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAccountInfoModel.java
 * @Description
 * @createTime 2023年12月15日 13:37:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_account_info")
public class AdsAccountInfoModel implements Serializable {
    private static final long serialVersionUID = -5320434443921099248L;

    @TableId(type = IdType.AUTO)
    private Long id;
    //用户id，仅为店铺管理员的id（REFERENCES ads_user）
    @TableField(value = "user_id")
    private Long userId;
    //账户id（REFERENCES ads_account_info）
    @TableField(value = "account_id")
    private String accountId;
    //账户类型
    @TableField(value = "type")
    private String type;
    //账户名称
    @TableField(value = "name")
    private String name;
    //账户有效付款状态（0无效、1有效）
    @TableField(value = "valid_payment")
    private Boolean validPayment;
    //ads刷新令牌
    @TableField(value = "refresh_token")
    private String refreshToken;
    //sp刷新令牌
    @TableField(value = "refresh_token_sp")
    private String refreshTokenSp;
    //账户状态（0无效、1有效）
    @TableField(value = "status")
    private Boolean status;
    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


//    public AdsAccountInfoModel(String accoutnId, String type, String name, Boolean validPayment, String refreshToken, Boolean status) {
//        this.accoutnId = accoutnId;
//        this.type = type;
//        this.name = name;
//        this.validPayment = validPayment;
//        this.refreshToken = refreshToken;
//        this.status = status;
//    }
}
