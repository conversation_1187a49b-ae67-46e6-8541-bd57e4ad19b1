package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsMonitorRuleModel.java
 * @Description
 * @createTime 2025年06月27日 15:27:00
 */
@Data
@Accessors(chain = true)
@TableName("ads_svc_configuration")
public class AdsSvcConfigurationModel implements Serializable {
    private static final long serialVersionUID = -2350463051602777235L;

    @TableId(type = IdType.AUTO)
    private Long id;

    //账户id
    @TableField(value = "account_id")
    private String accountId;

    //账户文件id
    @TableField(value = "profile_id")
    private Long profileId;

    @TableField("name")
    private String name;

    @TableField("description")
    private String description;

    @TableField("notification_method")
    private Integer notificationMethod;

    @TableField("monitor_obj")
    private String monitorObj;

    @TableField("status")
    private Boolean status;

    // 创建者
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    // 更新者
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

