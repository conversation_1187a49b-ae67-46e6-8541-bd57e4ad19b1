package com.yiyitech.ads.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yiyitech.ads.model.AdsSpCategoryModel;
import com.yiyitech.ads.vo.search.AdsSpCategorySearchVO;
import com.yiyitech.support.exception.BusinessException;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpCategoryService.java
 * @Description
 * @createTime 2025年02月18日 13:40:00
 */
public interface AdsSpCategoryService extends IService<AdsSpCategoryModel> {

    Map<String, Object> insertSpCategorys(String accountId, AdsSpCategorySearchVO param) throws BusinessException;

    Map<String, Object> searchSpCategorys(AdsSpCategorySearchVO param) throws BusinessException;
}
