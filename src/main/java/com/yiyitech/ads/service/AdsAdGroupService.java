package com.yiyitech.ads.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yiyitech.ads.model.AdsAdGroupModel;
import com.yiyitech.ads.vo.AdsSpAdGroupCreateVO;
import com.yiyitech.support.exception.BusinessException;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAdGroupService.java
 * @Description
 * @createTime 2023年12月22日 13:26:00
 */
public interface AdsAdGroupService extends IService<AdsAdGroupModel> {

    boolean batchSaveOrUpdateByUniqueKey(List<AdsAdGroupModel> list);

    List<AdsAdGroupModel> searchBySearchTermReportParam(List<String> accountId, List<String> profileId, String adGroupName);

    Map<String, Object> createAdGroup(AdsSpAdGroupCreateVO param) throws BusinessException;
}
