package com.yiyitech.ads.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yiyitech.ads.model.AdsAccountInfoModel;
import com.yiyitech.ads.model.AuthState;
import com.yiyitech.ads.vo.AdsAccountVO;
import com.yiyitech.ads.vo.search.AdsAccountSearchVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAccountService.java
 * @Description
 * @createTime 2023年12月13日 16:34:00
 */
public interface AdsAccountService extends IService<AdsAccountInfoModel> {

    void accessToken(String code, AuthState state);

    void accessSpToken(String code, AuthState state, String sellerId);

    boolean batchSaveOrUpdateByUniqueKey(List<AdsAccountInfoModel> list);

    Map<String, Object> getAccList(Long userId, AdsAccountSearchVO param);
}
