package com.yiyitech.ads.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据库分表策略服务
 * 用于处理大数据量情况下的分表逻辑
 * 支持按时间、账户等维度进行分表
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsTableShardingService.java
 * @Description 数据库分表策略服务
 * @createTime 2025年01月31日
 */
@Slf4j
@Service
public class AdsTableShardingService {

    // 分表策略枚举
    public enum ShardingStrategy {
        BY_MONTH,    // 按月分表
        BY_QUARTER,  // 按季度分表
        BY_YEAR,     // 按年分表
        BY_ACCOUNT   // 按账户分表
    }

    /**
     * 获取搜索词分析数据表名
     * 按月分表策略：ads_search_term_analytics_YYYYMM
     * 
     * @param accountId 账户ID
     * @param date 日期
     * @return 表名
     */
    public String getSearchTermAnalyticsTableName(String accountId, String date) {
        return getShardedTableName("ads_search_term_analytics", ShardingStrategy.BY_MONTH, accountId, date);
    }

    /**
     * 获取搜索词类目关联表名
     * 按月分表策略：ads_search_term_category_relation_YYYYMM
     * 
     * @param accountId 账户ID
     * @param date 日期
     * @return 表名
     */
    public String getSearchTermCategoryRelationTableName(String accountId, String date) {
        return getShardedTableName("ads_search_term_category_relation", ShardingStrategy.BY_MONTH, accountId, date);
    }

    /**
     * 获取搜索词商品排名表名
     * 按月分表策略：ads_search_term_product_ranking_YYYYMM
     * 
     * @param accountId 账户ID
     * @param date 日期
     * @return 表名
     */
    public String getSearchTermProductRankingTableName(String accountId, String date) {
        return getShardedTableName("ads_search_term_product_ranking", ShardingStrategy.BY_MONTH, accountId, date);
    }

    /**
     * 根据分表策略获取表名
     * 
     * @param baseTableName 基础表名
     * @param strategy 分表策略
     * @param accountId 账户ID
     * @param date 日期
     * @return 分表后的表名
     */
    public String getShardedTableName(String baseTableName, ShardingStrategy strategy, String accountId, String date) {
        if (StrUtil.isBlank(baseTableName)) {
            throw new IllegalArgumentException("基础表名不能为空");
        }

        StringBuilder tableName = new StringBuilder(baseTableName);

        switch (strategy) {
            case BY_MONTH:
                tableName.append("_").append(getMonthSuffix(date));
                break;
            case BY_QUARTER:
                tableName.append("_").append(getQuarterSuffix(date));
                break;
            case BY_YEAR:
                tableName.append("_").append(getYearSuffix(date));
                break;
            case BY_ACCOUNT:
                tableName.append("_").append(getAccountSuffix(accountId));
                break;
            default:
                // 默认不分表
                break;
        }

        return tableName.toString();
    }

    /**
     * 获取月份后缀
     * 格式：YYYYMM
     * 
     * @param date 日期字符串 (YYYY-MM-DD)
     * @return 月份后缀
     */
    private String getMonthSuffix(String date) {
        if (StrUtil.isBlank(date)) {
            date = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        
        try {
            LocalDate localDate = LocalDate.parse(date);
            return localDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
        } catch (Exception e) {
            log.error("解析日期失败: {}", date, e);
            return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        }
    }

    /**
     * 获取季度后缀
     * 格式：YYYYQN (如：2025Q1)
     * 
     * @param date 日期字符串
     * @return 季度后缀
     */
    private String getQuarterSuffix(String date) {
        if (StrUtil.isBlank(date)) {
            date = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        
        try {
            LocalDate localDate = LocalDate.parse(date);
            int year = localDate.getYear();
            int month = localDate.getMonthValue();
            int quarter = (month - 1) / 3 + 1;
            return year + "Q" + quarter;
        } catch (Exception e) {
            log.error("解析日期失败: {}", date, e);
            LocalDate now = LocalDate.now();
            int quarter = (now.getMonthValue() - 1) / 3 + 1;
            return now.getYear() + "Q" + quarter;
        }
    }

    /**
     * 获取年份后缀
     * 格式：YYYY
     * 
     * @param date 日期字符串
     * @return 年份后缀
     */
    private String getYearSuffix(String date) {
        if (StrUtil.isBlank(date)) {
            date = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        
        try {
            LocalDate localDate = LocalDate.parse(date);
            return String.valueOf(localDate.getYear());
        } catch (Exception e) {
            log.error("解析日期失败: {}", date, e);
            return String.valueOf(LocalDate.now().getYear());
        }
    }

    /**
     * 获取账户后缀
     * 使用账户ID的哈希值取模
     * 
     * @param accountId 账户ID
     * @return 账户后缀
     */
    private String getAccountSuffix(String accountId) {
        if (StrUtil.isBlank(accountId)) {
            return "default";
        }
        
        // 使用哈希值取模，分成16个分片
        int hash = Math.abs(accountId.hashCode());
        int shardIndex = hash % 16;
        return String.format("%02d", shardIndex);
    }

    /**
     * 获取指定时间范围内的所有分表名
     * 
     * @param baseTableName 基础表名
     * @param strategy 分表策略
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param accountId 账户ID（可选）
     * @return 分表名列表
     */
    public List<String> getShardedTableNames(String baseTableName, ShardingStrategy strategy, 
                                           String startDate, String endDate, String accountId) {
        List<String> tableNames = new ArrayList<>();
        
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            switch (strategy) {
                case BY_MONTH:
                    tableNames = getMonthlyTableNames(baseTableName, start, end);
                    break;
                case BY_QUARTER:
                    tableNames = getQuarterlyTableNames(baseTableName, start, end);
                    break;
                case BY_YEAR:
                    tableNames = getYearlyTableNames(baseTableName, start, end);
                    break;
                case BY_ACCOUNT:
                    tableNames.add(getShardedTableName(baseTableName, strategy, accountId, startDate));
                    break;
                default:
                    tableNames.add(baseTableName);
                    break;
            }
            
        } catch (Exception e) {
            log.error("获取分表名列表失败: {}", e.getMessage(), e);
            tableNames.add(baseTableName); // 降级到基础表名
        }
        
        return tableNames;
    }

    /**
     * 获取月度分表名列表
     * 
     * @param baseTableName 基础表名
     * @param start 开始日期
     * @param end 结束日期
     * @return 月度分表名列表
     */
    private List<String> getMonthlyTableNames(String baseTableName, LocalDate start, LocalDate end) {
        List<String> tableNames = new ArrayList<>();
        
        LocalDate current = start.withDayOfMonth(1); // 从月初开始
        LocalDate endOfMonth = end.withDayOfMonth(end.lengthOfMonth()); // 到月末结束
        
        while (!current.isAfter(endOfMonth)) {
            String suffix = current.format(DateTimeFormatter.ofPattern("yyyyMM"));
            tableNames.add(baseTableName + "_" + suffix);
            current = current.plusMonths(1);
        }
        
        return tableNames;
    }

    /**
     * 获取季度分表名列表
     * 
     * @param baseTableName 基础表名
     * @param start 开始日期
     * @param end 结束日期
     * @return 季度分表名列表
     */
    private List<String> getQuarterlyTableNames(String baseTableName, LocalDate start, LocalDate end) {
        List<String> tableNames = new ArrayList<>();
        
        LocalDate current = start;
        
        while (!current.isAfter(end)) {
            int year = current.getYear();
            int quarter = (current.getMonthValue() - 1) / 3 + 1;
            String suffix = year + "Q" + quarter;
            
            String tableName = baseTableName + "_" + suffix;
            if (!tableNames.contains(tableName)) {
                tableNames.add(tableName);
            }
            
            current = current.plusMonths(3);
        }
        
        return tableNames;
    }

    /**
     * 获取年度分表名列表
     * 
     * @param baseTableName 基础表名
     * @param start 开始日期
     * @param end 结束日期
     * @return 年度分表名列表
     */
    private List<String> getYearlyTableNames(String baseTableName, LocalDate start, LocalDate end) {
        List<String> tableNames = new ArrayList<>();
        
        int startYear = start.getYear();
        int endYear = end.getYear();
        
        for (int year = startYear; year <= endYear; year++) {
            tableNames.add(baseTableName + "_" + year);
        }
        
        return tableNames;
    }

    /**
     * 检查是否需要创建新的分表
     * 
     * @param baseTableName 基础表名
     * @param strategy 分表策略
     * @param accountId 账户ID
     * @param date 日期
     * @return 是否需要创建新表
     */
    public boolean needCreateNewTable(String baseTableName, ShardingStrategy strategy, String accountId, String date) {
        String tableName = getShardedTableName(baseTableName, strategy, accountId, date);
        
        // 这里可以实现检查表是否存在的逻辑
        // 例如查询数据库元数据或维护一个表名缓存
        
        log.info("检查是否需要创建新表: {}", tableName);
        return false; // 暂时返回false，实际实现需要查询数据库
    }

    /**
     * 生成创建分表的SQL语句
     * 
     * @param baseTableName 基础表名
     * @param shardedTableName 分表名
     * @return 创建表的SQL语句
     */
    public String generateCreateTableSQL(String baseTableName, String shardedTableName) {
        // 这里可以根据基础表结构生成创建分表的SQL
        // 实际实现需要根据具体的表结构来生成
        
        String sql = String.format(
                "CREATE TABLE IF NOT EXISTS %s LIKE %s", 
                shardedTableName, 
                baseTableName
        );
        
        log.info("生成创建分表SQL: {}", sql);
        return sql;
    }
}
