package com.yiyitech.ads.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yiyitech.ads.model.AdsExchangeRateModel;
import com.yiyitech.ads.vo.AdsExchangeRateVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsExchangeRateService.java
 * @Description
 * @createTime 2024年01月29日 15:31:00
 */
public interface AdsExchangeRateService extends IService<AdsExchangeRateModel> {

    Map<String, Object> searchExchangeRateLst(String original, String target, String startDate, String endDate);

    Map<String, Object> saveOrUpdateExchangeRate(AdsExchangeRateVO adsExchangeRateVO);
}
