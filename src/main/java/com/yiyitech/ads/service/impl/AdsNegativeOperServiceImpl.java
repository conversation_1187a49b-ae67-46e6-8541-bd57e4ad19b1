package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.enums.*;
import com.yiyitech.ads.mapper.AdsDailyAdvertiserReportMapper;
import com.yiyitech.ads.mapper.AdsDailySearchTermReportMapper;
import com.yiyitech.ads.mapper.AdsDailyTargetingReportMapper;
import com.yiyitech.ads.mapper.AdsNegativeOperLogMapper;
import com.yiyitech.ads.model.*;
import com.yiyitech.ads.service.AdsNegativeOperService;
import com.yiyitech.ads.service.CommonService;
import com.yiyitech.ads.service.api.AdsNegativeApi;
import com.yiyitech.ads.util.AcosStatUtils;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.ads.vo.*;
import com.yiyitech.ads.vo.search.AdsNegOperSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperServiceImpl.java
 * @Description
 * @createTime 2025年07月10日 13:16:00
 */
@Slf4j
@Service
public class AdsNegativeOperServiceImpl implements AdsNegativeOperService {
    @Autowired
    private CommonService commonService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsNegativeApi adsNegativeApi;
    @Autowired
    private AdsNegativeOperLogMapper negativeOperLogMapper;
    @Autowired
    private AdsDailyTargetingReportMapper dailyTargetingReportMapper;
    @Autowired
    private AdsDailySearchTermReportMapper dailySearchTermReportMapper;
    @Autowired
    private AdsDailyAdvertiserReportMapper advertiserReportMapper;

    private static double roundDouble(Double value) {
        if (value == null) return 0.0;
        return BigDecimal.valueOf(value).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    @Override
    public Map<String, Object> searhControlChartData(LoginUser loginUser, AdsNegOperSearchVO param) {
        Map<String, Object> map = new HashMap<>();
        AdsNegOperControlChartVO result = new AdsNegOperControlChartVO();
        //筛选条件
        List<String> campaignIds = StringUtils.hasText(param.getCampaign()) ? Arrays.asList(param.getCampaign().split(",")) : null;
        List<String> adGroupIds = StringUtils.hasText(param.getAdGroup()) ? Arrays.asList(param.getAdGroup().split(",")) : null;
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        Integer acosType = param.getAcosType();
        //当前用户拥有的店铺
        List<AdsAccountProfileModel> profileLst = (List<AdsAccountProfileModel>) commonService.searchFilterOrCurrentProfileInfo(Long.valueOf(loginUser.getId()), param.getAccProId()).get("profiles");
        //筛选范围内的商品报告（获取asin）
        List<AdsDailyAdvertiserReportModel> advertiserModelLst = advertiserReportMapper.searchByProLstByCampByGroupByAsinByDate(profileLst, campaignIds, adGroupIds, param.getAsin(), startDate, endDate);
        //商品以campaignId和adGroupId分组
        Map<String, AdsDailyAdvertiserReportModel> resultMap = advertiserModelLst.stream()
                .collect(Collectors.toMap(
                        profile -> profile.getCampaignId() + "-" + profile.getAdGroupId(),
                        profile -> profile,
//                        (existing, replacement) -> existing  保留第一条
                        (existing, replacement) -> replacement //保留最后一条
                ));
        //当前用户拥有店铺下否词日志，并按店铺纬度分组
        List<AdsNegativeOperLogModel> negLogLst = negativeOperLogMapper.searchByProfileLstByStatus(profileLst, AdsNegLogStatusEnum.getNonAutoFailedCodes());
        Map<String, List<String>> negLogMap = negLogLst.stream()
                .collect(Collectors.groupingBy(
                        log -> String.join("#", log.getAccountId(), String.valueOf(log.getProfileId()),
                                log.getCampaignId(), log.getAdGroupId()),
                        Collectors.mapping(AdsNegativeOperLogModel::getNegativeValue, Collectors.toList())
                ));

        //筛选范围内关键词报告
        if(AdsReportCreateTypeEnum.TARGETING.getCode() == param.getType()){
            List<AdsDailyTargetingReportModel> targatIngModelLst = dailyTargetingReportMapper.searchByProLstByCampByGroupByStatusByDate(profileLst, campaignIds, adGroupIds, param.getStatus(), startDate, endDate);

            List<AdsDailyTargetingReportModel> matchedModels = new ArrayList<>();
            AdsNegOperControlChartVO vo = AcosStatUtils.calculateChartVO(
                    false,
                    targatIngModelLst,
                    resultMap,
                    negLogMap,
                    AdsDailyTargetingReportModel::getKeyword,
                    m -> String.join("#", m.getAccountId(), String.valueOf(m.getProfileId()),
                            m.getCampaignId(), m.getAdGroupId(), m.getKeywordId()),
                    AdsDailyTargetingReportModel::getCost,
                    AdsDailyTargetingReportModel::getSales1d,
                    AdsDailyTargetingReportModel::getClicks,
                    AdsDailyTargetingReportModel::getPurchases1d,
                    AdsDailyTargetingReportModel::getImpressions,
                    m -> m.getCampaignId() + "-" + m.getAdGroupId(),
                    param.getAcosType(),
                    matchedModels
            );

            List<AdsNegOperControlChartVO.Data> voData = AcosStatUtils.convertModelList(matchedModels, resultMap, acosType);
            vo.setDataLst(voData);
            result = vo;
        }
        //筛选范围内搜索词报告
        if(AdsReportCreateTypeEnum.SEARCHTERM.getCode() == param.getType()){
            List<AdsDailySearchTermReportModel> searchTermModelLst = dailySearchTermReportMapper.searchByProLstByCampByGroupByStatusByDate(profileLst, campaignIds, adGroupIds, param.getStatus(), startDate, endDate);

            List<AdsDailySearchTermReportModel> matchedModels = new ArrayList<>();
            AdsNegOperControlChartVO vo = AcosStatUtils.calculateChartVO(
                    false,
                    searchTermModelLst,
                    resultMap,
                    negLogMap,
                    AdsDailySearchTermReportModel::getSearchTerm,
                        m -> String.join("#", m.getAccountId(), String.valueOf(m.getProfileId()),
                            m.getCampaignId(), m.getAdGroupId(), m.getKeywordId(), m.getSearchTerm()),
                    AdsDailySearchTermReportModel::getCost,
                    AdsDailySearchTermReportModel::getSales1d,
                    AdsDailySearchTermReportModel::getClicks,
                    AdsDailySearchTermReportModel::getPurchases1d,
                    AdsDailySearchTermReportModel::getImpressions,
                    m -> m.getCampaignId() + "-" + m.getAdGroupId(),
                    param.getAcosType(),
                    matchedModels
            );

            List<AdsNegOperControlChartVO.Data> voData = AcosStatUtils.convertModelList(matchedModels, resultMap, acosType);
            vo.setDataLst(voData);
            result = vo;
        }

        map.put("data", result);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> keywordToSechTerm(LoginUser loginUser, AdsNegOperSearchVO param) {
        Map<String, Object> map = new HashMap<>();
        List<AdsKeywordToSechTermVO> result = new ArrayList<>();
        List<AdsDailySearchTermReportModel> searchTermModelLst = dailySearchTermReportMapper.searchByUniqueSignByDate(param.getUniqueSign(), param.getStartDate(), param.getEndDate());
        //合并搜索词
        List<AdsDailySearchTermReportModel> matchedModels = new ArrayList<>();
        AcosStatUtils.calculateChartVO(
                true,
                searchTermModelLst,
                null,
                null,
                null,
                m -> String.join("#", m.getAccountId(), String.valueOf(m.getProfileId()),
                        m.getCampaignId(), m.getAdGroupId(), m.getKeywordId(), m.getSearchTerm()),
                AdsDailySearchTermReportModel::getCost,
                AdsDailySearchTermReportModel::getSales1d,
                AdsDailySearchTermReportModel::getClicks,
                AdsDailySearchTermReportModel::getPurchases1d,
                AdsDailySearchTermReportModel::getImpressions,
                null,
                null,
                matchedModels
        );

        if (CollectionUtils.isNotEmpty(matchedModels)) {
            result = matchedModels.stream().map(model -> {
                AdsKeywordToSechTermVO resultVO = new AdsKeywordToSechTermVO();
                resultVO.setUniqueSign(model.getAccountId()+"-"+model.getProfileId()+"-"+model.getCampaignId()+"-"+model.getAdGroupId()+"-"+model.getKeywordId()+"-"+model.getSearchTerm());
                resultVO.setValue(model.getSearchTerm());
                if (model.getSearchTerm() != null && model.getSearchTerm().startsWith("b0") && model.getSearchTerm().length() == 10) {
                    resultVO.setSearchTermType(2);
                } else {
                    resultVO.setSearchTermType(1);
                }
                resultVO.setClicks(model.getClicks());
                resultVO.setPurchases1d(model.getPurchases1d());
                return resultVO;
            }).collect(Collectors.toList());

        }

        map.put("data", result);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> sechTermToKeyword(LoginUser loginUser, AdsNegOperSearchVO param) {
        Map<String, Object> map = new HashMap<>();
        List<AdsSechTermToKeywordVO> result = new ArrayList<>();
        List<AdsDailyTargetingReportModel> targatIngModelLst = dailyTargetingReportMapper.searchByUniqueSignByDate(param.getUniqueSign(), param.getStartDate(), param.getEndDate());

        List<AdsDailyTargetingReportModel> matchedModels = new ArrayList<>();
        AcosStatUtils.calculateChartVO(
                true,
                targatIngModelLst,
                null,
                null,
                null,
                m -> String.join("#", m.getAccountId(), String.valueOf(m.getProfileId()),
                        m.getCampaignId(), m.getAdGroupId(), m.getKeywordId()),
                AdsDailyTargetingReportModel::getCost,
                AdsDailyTargetingReportModel::getSales1d,
                AdsDailyTargetingReportModel::getClicks,
                AdsDailyTargetingReportModel::getPurchases1d,
                AdsDailyTargetingReportModel::getImpressions,
                null,
                null,
                matchedModels
        );

        for (AdsDailyTargetingReportModel model : matchedModels) {
            AdsSechTermToKeywordVO vo = new AdsSechTermToKeywordVO();
            vo.setCampaignName(model.getCampaignName());
            vo.setAdGroupName(model.getAdGroupName());
            vo.setValue(model.getKeyword());
            vo.setImpressions(model.getImpressions());
            vo.setClicks(model.getClicks());
            vo.setCost(model.getCost());
            vo.setPurchases1d(model.getPurchases1d());
            vo.setSales1d(model.getSales1d());

            if (model.getClicks() != null && model.getClicks() > 0 && model.getCost() != null) {
                vo.setCostPerClick(vo.getClicks() > 0 ? new BigDecimal(vo.getCost()).divide(new BigDecimal(vo.getClicks()), 2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
            } else {
                vo.setCostPerClick(0.0);
            }
            result.add(vo);
        }

        map.put("data", result);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    //TODO 事务配置以及后续优化这个方法
    @Override
    public void negativeProcessing(LoginUser loginUser, AdsNegProcessingVO param) {
        String userId = loginUser.getId();
        Date date = new Date();

        if (1 == param.getOperSource()) {
            List<AdsNegProcessingVO.OperateInfo> operateLst = param.getOperateLst();
            for (AdsNegProcessingVO.OperateInfo operate : operateLst) {
                //日志处理：更新日志信息
                AdsNegativeOperLogModel negativeOperLogModel = negativeOperLogMapper.selectById(operate.getRecordId());

                negativeOperLogModel.setNegativeType(null == operate.getSubInfo() ? operate.getMatchType() : AdsMatchTypeEnum.EXACT_AND_PHRASE.getCode());
                negativeOperLogModel.setOperLevel(operate.getScope());
                negativeOperLogModel.setAfterOper(null == operate.getSubInfo() ? AdsMatchTypeEnum.of(operate.getMatchType()).getDesc() + "-" + operate.getValue() : AdsMatchTypeEnum.of(operate.getMatchType()).getDesc() + "-" + operate.getValue() + "+" + AdsMatchTypeEnum.of(operate.getSubInfo().getMatchType()).getDesc() + "-" + operate.getSubInfo().getValue());
                negativeOperLogModel.setStatus(AdsNegLogStatusEnum.COMPLETE.getCode());
                negativeOperLogModel.setUpdateBy(userId);
                negativeOperLogModel.setUpdateTime(date);
                negativeOperLogMapper.updateById(negativeOperLogModel);

                //调用ads接口进行否词
                String accountId = negativeOperLogModel.getAccountId();
                Long profileId = negativeOperLogModel.getProfileId();
                String campaignId = negativeOperLogModel.getCampaignId();
                String adGroupId = negativeOperLogModel.getAdGroupId();

                String adsAccessToken = redisUtils.getAccountAccessTokenAndRefreshToken(accountId, CommonConstants.ADS_PREFIX_KEY);
                if (operate.getMatchType() == AdsMatchTypeEnum.ADV_ASIN.getCode()) {
                    adsNegativeApi.negativeTargeting(adsAccessToken, profileId.toString(), campaignId, adGroupId, operate.getValue(), operate.getMatchType());
                } else {
                    adsNegativeApi.negativeKeywords(adsAccessToken, profileId.toString(), campaignId, adGroupId, operate.getValue(), operate.getMatchType());
                    if (null != operate.getSubInfo()) {
                        adsNegativeApi.negativeKeywords(adsAccessToken, profileId.toString(), campaignId, adGroupId, operate.getSubInfo().getValue(), operate.getSubInfo().getMatchType());

                    }
                }
            }
        }

        if (2 == param.getOperSource()) {
            List<AdsNegProcessingVO.OperateInfo> operateLst = param.getOperateLst();

            String campaignName = param.getCampaignName();
            String adGroupName = param.getAdGroupName();
            Integer negSource = param.getNegSource();
            String asin = param.getAsin();
            Integer operType = param.getOperType();

            for (AdsNegProcessingVO.OperateInfo operate : operateLst) {
                String[] parts = operate.getUniqueSign().split("-", 6);
                if (parts.length < 5 || parts.length > 6) {
                    return;
                }
                String accountId = parts[0];
                String profileIdStr = parts[1];
                String campaignId = parts[2];
                String adGroupId = parts[3];
                AdsDailyAdvertiserReportModel advModel = advertiserReportMapper.searchByAccByProByCamByGroupByAsin(accountId, Long.valueOf(profileIdStr), campaignId, adGroupId, asin);

                //日志处理：更新日志信息
                AdsNegativeOperLogModel negativeOperLogModel = new AdsNegativeOperLogModel();
                //portfolio_id
                negativeOperLogModel.setPortfolioId(null);
                //account_id
                negativeOperLogModel.setAccountId(accountId);
                //profile_id
                negativeOperLogModel.setProfileId(Long.valueOf(profileIdStr));
                //campaign_id
                negativeOperLogModel.setCampaignId(campaignId);
                //campaign_name
                negativeOperLogModel.setCampaignName(campaignName);
                //ad_group_id
                negativeOperLogModel.setAdGroupId(adGroupId);
                //ad_group_name
                negativeOperLogModel.setAdGroupName(adGroupName);
                //negative_value_sign
                negativeOperLogModel.setNegativeValueSign(operate.getUniqueSign());
                //negative_value
                negativeOperLogModel.setNegativeValue(operate.getValue());
                //negative_type
                negativeOperLogModel.setNegativeType(null == operate.getSubInfo() ? operate.getMatchType() : AdsMatchTypeEnum.EXACT_AND_PHRASE.getCode());
                //source
                negativeOperLogModel.setSource(negSource);
                //parent_asin
                negativeOperLogModel.setParentAsin(null == advModel ? "" : advModel.getAdvertisedParentAsin());
                //asin
                negativeOperLogModel.setAsin(asin);
                //sku
                negativeOperLogModel.setSku(null == advModel ? "" : advModel.getAdvertisedSku());
                //picture_url
                negativeOperLogModel.setPictureUrl(null == advModel ? "" : advModel.getPictureUrl());
                //asin_Item_name
                negativeOperLogModel.setAsinItemName(null == advModel ? "" : advModel.getAdvertisedItemName());
                //oper_level
                negativeOperLogModel.setOperLevel(operate.getScope());
                //oper_type
                negativeOperLogModel.setOperType(operType);
                //before_oper
                negativeOperLogModel.setBeforeOper(null);
                //after_oper
                negativeOperLogModel.setAfterOper(null == operate.getSubInfo() ? AdsMatchTypeEnum.of(operate.getMatchType()).getDesc() + "-" + operate.getValue() : AdsMatchTypeEnum.of(operate.getMatchType()).getDesc() + "-" + operate.getValue() + "+" + AdsMatchTypeEnum.of(operate.getSubInfo().getMatchType()).getDesc() + "-" + operate.getSubInfo().getValue());
                //judge_type
                negativeOperLogModel.setJudgeType(AdsNegJudgeTypeEnum.ARTIFICIAL.getCode());
                //judge_type
                negativeOperLogModel.setStatus(AdsNegLogStatusEnum.COMPLETE.getCode());
                //create_by
                negativeOperLogModel.setCreateBy(userId);
                //update_by
                negativeOperLogModel.setUpdateBy(userId);
                //create_time
                negativeOperLogModel.setCreateTime(date);
                //update_time
                negativeOperLogModel.setUpdateTime(date);
                negativeOperLogMapper.insert(negativeOperLogModel);
                //调用ads接口进行否词
                String adsAccessToken = redisUtils.getAccountAccessTokenAndRefreshToken(accountId, CommonConstants.ADS_PREFIX_KEY);
                if (operate.getMatchType() == AdsMatchTypeEnum.ADV_ASIN.getCode()) {
                    adsNegativeApi.negativeTargeting(adsAccessToken, profileIdStr, campaignId, adGroupId, operate.getValue(), operate.getMatchType());
                } else {
                    adsNegativeApi.negativeKeywords(adsAccessToken, profileIdStr, campaignId, adGroupId, operate.getValue(), operate.getMatchType());
                    if (null != operate.getSubInfo()) {
                        adsNegativeApi.negativeKeywords(adsAccessToken, profileIdStr, campaignId, adGroupId, operate.getSubInfo().getValue(), operate.getSubInfo().getMatchType());

                    }
                }
            }
        }
    }

}
