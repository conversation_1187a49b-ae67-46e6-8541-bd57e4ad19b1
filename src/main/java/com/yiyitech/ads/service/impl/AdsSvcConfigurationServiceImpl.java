package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yiyitech.ads.enums.*;
import com.yiyitech.ads.exception.SvcConfgExceptionCode;
import com.yiyitech.ads.exception.UserExceptionCode;
import com.yiyitech.ads.mapper.*;
import com.yiyitech.ads.model.*;
import com.yiyitech.ads.model.assembler.AdsNegativeOperLogAssembler;
import com.yiyitech.ads.model.dto.*;
import com.yiyitech.ads.service.AdsSvcConfigurationService;
import com.yiyitech.ads.service.CommonService;
import com.yiyitech.ads.vo.AdsConditionGroupsVO;
import com.yiyitech.ads.vo.AdsConditionsVO;
import com.yiyitech.ads.vo.AdsMonitorRulesVO;
import com.yiyitech.ads.vo.AdsSvcConfigurationVO;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSvcConfigurationServiceImpl.java
 * @Description
 * @createTime 2025年06月27日 14:23:00
 */
@Slf4j
@Service
public class AdsSvcConfigurationServiceImpl extends ServiceImpl<AdsSvcConfigurationMapper, AdsSvcConfigurationModel> implements AdsSvcConfigurationService {
    @Autowired
    private CommonService commonService;
    @Autowired
    private AdsSvcConfigurationMapper svcConfigMapper;
    @Autowired
    private AdsMonitorRulesMapper monitorRulesMapper;
    @Autowired
    private AdsConditionGroupMapper conditionGroupMapper;
    @Autowired
    private AdsConditionMapper conditionMapper;
    @Autowired
    private AdsUserAccountMapper adsUserAccountMapper;
    @Autowired
    private AdsAccountProfileMapper adsAccountProfileMapper;
    @Autowired
    private AdsNegativeOperLogMapper negOperLogMapper;
    @Autowired
    private TransactionTemplate tx;

    @Override
    public Map<String, Object> getCurrentSvcConfig(Long userId) {
        Map<String, Object> map = new HashMap<>();
        List<AdsSvcConfigurationVO> voLst = new ArrayList<>();
        //获取当前登录用户拥有的店铺
        List<AdsAccountProfileModel> profileLst = (List<AdsAccountProfileModel>) commonService.searchFilterOrCurrentProfileInfo(userId, null).get("profiles");

//        List<AdsUserAccountModel> userAccLst = adsUserAccountMapper.searchByUserIdByStatus(userId, true);
//        if (null == userAccLst || userAccLst.size() <= 0) {
//            map.put("data", vo);
//            map.put("status", HttpStatus.HTTP_OK);
//            return map;
//        }
//        //获取店铺的accountId以及profileId
//        AdsAccountProfileModel accProMode = adsAccountProfileMapper.selectById(userAccLst.get(0).getAccountProId());
        for(AdsAccountProfileModel accProMode : profileLst){
            AdsSvcConfigurationVO vo = new AdsSvcConfigurationVO();

            String accountId = accProMode.getAccountId();
            Long profileId = accProMode.getProfileId();
            //获取此店铺SvcConfiguration信息（已有默认配置的情况）
            List<AdsSvcConfigurationModel> svcModelLst = svcConfigMapper.selectByAccountIdByproId(accountId, profileId);
            AdsSvcConfigurationModel model;
            if (null != svcModelLst && svcModelLst.size() > 0) {
                model = svcModelLst.get(0);
            }else {
                //店铺还没有创建配置（此店铺还没有配置的情况）,临时默认保存方法，后续拆出单独保存方法
                try {
                    model = saveDefaultData(accountId, profileId);
                } catch (BusinessException e) {
                    log.error("默认配置创建失败", e);
                    throw new BusinessException(SvcConfgExceptionCode.SAVE_DEFAULT_CONFG_EXIST_CODE, SvcConfgExceptionCode.SAVE_DEFAULT_CONFG_EXIST_MSG);
                }
            }
            //组装返回vo
            vo.setId(model.getId().toString())
                    .setName(model.getName())
                    .setDescription(model.getDescription())
                    .setNotificationMethod(SvcConfgNotificationEnum.of(model.getNotificationMethod()).getDesc())
                    .setMonitorObj(model.getMonitorObj())
                    .setIsEnable(model.getStatus());
            voLst.add(vo);
        }

        map.put("data", voLst.get(0));
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    public AdsSvcConfigurationModel saveDefaultData(String accountId, Long profileId){
        return tx.execute(status -> {
            try {
                //配置数据
                AdsSvcConfigurationModel confgModel = new AdsSvcConfigurationModel()
                        .setAccountId(accountId)
                        .setProfileId(profileId)
                        .setName("否词运营监控配置")
                        .setDescription("否词运营监控配置")
                        .setMonitorObj("用户搜索词")
                        .setNotificationMethod(2)
                        .setStatus(false)
                        .setCreateBy("system")
                        .setUpdateBy("system");
                svcConfigMapper.insert(confgModel);
                Long svcConfgId = confgModel.getId();

                //规则数据
                AdsMonitorRulesModel rulesModel1 = new AdsMonitorRulesModel();
                AdsMonitorRulesModel rulesModel2 = new AdsMonitorRulesModel();
                rulesModel1.setSvcConfigId(svcConfgId).setRuleName("规则1").setActionType(2).setStatus(true).setCreateBy("system").setUpdateBy("system");
                rulesModel2.setSvcConfigId(svcConfgId).setRuleName("规则2").setActionType(3).setStatus(true).setCreateBy("system").setUpdateBy("system");
                monitorRulesMapper.insert(rulesModel1);
                monitorRulesMapper.insert(rulesModel2);
                Long ruleId1 = rulesModel1.getId();
                Long ruleId2 = rulesModel2.getId();

                //条件组数据
                AdsConditionGroupModel conditionGroup1 = new AdsConditionGroupModel();
                AdsConditionGroupModel conditionGroup2 = new AdsConditionGroupModel();
                conditionGroup1.setRuleId(ruleId1).setLogicalRelation("AND").setStatus(true).setCreateBy("system").setUpdateBy("system");
                conditionGroup2.setRuleId(ruleId2).setLogicalRelation("AND").setStatus(true).setCreateBy("system").setUpdateBy("system");
                conditionGroupMapper.insert(conditionGroup1);
                conditionGroupMapper.insert(conditionGroup2);
                Long groupId1 = conditionGroup1.getId();
                Long groupId2 = conditionGroup2.getId();

                //条件组1的条件数据
                List<AdsConditionModel> conditionLst = new ArrayList<>();
                AdsConditionModel condition1 = new AdsConditionModel();
                AdsConditionModel condition2 = new AdsConditionModel();
                AdsConditionModel condition3 = new AdsConditionModel();
                AdsConditionModel condition4 = new AdsConditionModel();
                condition1.setGroupId(groupId1).setFieldId(1l).setDays(30).setOperator(2).setFieldValue("20").setStatus(true).setCreateBy("system").setUpdateBy("system");
                condition2.setGroupId(groupId1).setFieldId(2l).setDays(30).setOperator(5).setFieldValue("0").setStatus(true).setCreateBy("system").setUpdateBy("system");
                condition3.setGroupId(groupId1).setFieldId(1l).setDays(7).setOperator(2).setFieldValue("20").setStatus(true).setCreateBy("system").setUpdateBy("system");
                condition4.setGroupId(groupId1).setFieldId(2l).setDays(7).setOperator(5).setFieldValue("0").setStatus(true).setCreateBy("system").setUpdateBy("system");
                conditionLst.add(condition1);
                conditionLst.add(condition2);
                conditionLst.add(condition3);
                conditionLst.add(condition4);

                //条件组2的条件数据
                AdsConditionModel condition5 = new AdsConditionModel();
                AdsConditionModel condition6 = new AdsConditionModel();
                AdsConditionModel condition7 = new AdsConditionModel();
                AdsConditionModel condition8 = new AdsConditionModel();
                condition5.setGroupId(groupId2).setFieldId(1l).setDays(30).setOperator(2).setFieldValue("20").setStatus(true).setCreateBy("system").setUpdateBy("system");
                condition6.setGroupId(groupId2).setFieldId(2l).setDays(30).setOperator(1).setFieldValue("0").setStatus(true).setCreateBy("system").setUpdateBy("system");
                condition7.setGroupId(groupId2).setFieldId(1l).setDays(7).setOperator(2).setFieldValue("20").setStatus(true).setCreateBy("system").setUpdateBy("system");
                condition8.setGroupId(groupId2).setFieldId(2l).setDays(7).setOperator(5).setFieldValue("0").setStatus(true).setCreateBy("system").setUpdateBy("system");
                conditionLst.add(condition5);
                conditionLst.add(condition6);
                conditionLst.add(condition7);
                conditionLst.add(condition8);
                conditionMapper.batchInsert(AdsConditionModel.class, conditionLst, 10);

                return confgModel;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new BusinessException("创建默认配置失败", e.getMessage());
            }
        });
    }

//    @Override
//    public void svcConfigSwitch(Long svcConfigId, Boolean status, String userId) {
//        AdsSvcConfigurationModel model = svcConfigMapper.selectById(svcConfigId);
//        Date date = new Date();
//        //组装日志信息
//        String beforeOperStatus = model.getStatus() ? "开启" : "关闭";
//        String afterOperStatus = status ? "开启" : "关闭";
//        AdsNegativeOperLogModel negativeOperLogModel = AdsNegativeOperLogAssembler.build(null, model.getAccountId(), model.getProfileId(),
//                null, null, null, null, null, null, null, null,null,
//                null, null, null, null, AdsNegOperLevelEnum.NEGATIVE_OPERATION.getCode(), AdsNegOperTypeEnum.NEGATIVE_OPERATION.getCode(),
//                beforeOperStatus, afterOperStatus, AdsNegJudgeTypeEnum.ARTIFICIAL.getCode(), AdsNegLogStatusEnum.COMPLETE.getCode());
//        negativeOperLogModel.setCreateBy(userId).setCreateTime(date).setUpdateBy(userId).setUpdateTime(date);
////        AdsNegativeOperLogModel negativeOperLogModel = new AdsNegativeOperLogModel();
////        negativeOperLogModel
////                .setPortfolioId(null)
////                .setAccountId(model.getAccountId())
////                .setProfileId(model.getProfileId())
////                .setCampaignId(null)
////                .setCampaignName(null)
////                .setAdGroupId(null)
////                .setAdGroupName(null)
////                .setNegativeValueSign(null)
////                .setNegativeValue(null)
////                .setNegativeType(null)
////                .setSource(null)
////                .setParentAsin(null)
////                .setAsin(null)
////                .setSku(null)
////                .setPictureUrl(null)
////                .setAsinItemName(null)
////                .setOperLevel(AdsNegOperLevelEnum.NEGATIVE_OPERATION.getCode())
////                .setOperType(AdsNegOperTypeEnum.NEGATIVE_OPERATION.getCode())
////                .setBeforeOper(beforeOperStatus)
////                .setAfterOper(afterOperStatus)
////                .setJudgeType(AdsNegJudgeTypeEnum.ARTIFICIAL.getCode())
////                .setStatus(AdsNegLogStatusEnum.COMPLETE.getCode())
////                .setCreateBy(userId)
////                .setCreateTime(date)
////                .setUpdateBy(userId)
////                .setUpdateTime(date);
//        //更新配置状态
//        model.setStatus(status).setUpdateBy(userId).setUpdateTime(date);
//        //更新配置状态并保存操作日志
//        //TODO 事务配置
//        svcConfigMapper.updateById(model);
//        negOperLogMapper.insert(negativeOperLogModel);
//    }

    @Override
    public void svcConfigSwitch(Long svcConfigId, Boolean status, String userId) {
        //获取当前登录用户拥有的店铺
        List<AdsAccountProfileModel> profileLst = (List<AdsAccountProfileModel>) commonService.searchFilterOrCurrentProfileInfo(Long.valueOf(userId), null).get("profiles");
        for (AdsAccountProfileModel profile : profileLst) {
            AdsSvcConfigurationModel model = svcConfigMapper.getByAccountIdByproId(profile.getAccountId(), profile.getProfileId());
            if (null != model) {
                Date date = new Date();
                //组装日志信息
                String beforeOperStatus = model.getStatus() ? "开启" : "关闭";
                String afterOperStatus = status ? "开启" : "关闭";
                AdsNegativeOperLogModel negativeOperLogModel = AdsNegativeOperLogAssembler.build(null, model.getAccountId(), model.getProfileId(),
                        null, null, null, null, null, null, null, null, null,
                        null, null, null, null, AdsNegOperLevelEnum.NEGATIVE_OPERATION.getCode(), AdsNegOperTypeEnum.NEGATIVE_OPERATION.getCode(),
                        beforeOperStatus, afterOperStatus, AdsNegJudgeTypeEnum.ARTIFICIAL.getCode(), AdsNegLogStatusEnum.COMPLETE.getCode());
                negativeOperLogModel.setCreateBy(userId).setCreateTime(date).setUpdateBy(userId).setUpdateTime(date);
                //更新配置状态
                model.setStatus(status).setUpdateBy(userId).setUpdateTime(date);
                //更新配置状态并保存操作日志
                //TODO 事务配置
                svcConfigMapper.updateById(model);
                negOperLogMapper.insert(negativeOperLogModel);
            }
        }
    }

    @Override
    public Map<String, Object> searhSvcConfigRules(Long svcConfigId) {
        Map<String, Object> map = new HashMap<>();
        // 查询所有规则
        List<AdsMonitorRulesModel> rules = monitorRulesMapper.searchBySvcConfigId(svcConfigId);
        // 所有规则ID
        List<Long> ruleIds = rules.stream().map(AdsMonitorRulesModel::getId).collect(Collectors.toList());
        // 查询所有条件组（过滤状态为有效的）
        List<AdsConditionGroupModel> allGroups = conditionGroupMapper.selectByRuleIds(ruleIds);
        // 所有条件组ID
        List<Long> groupIds = allGroups.stream().map(AdsConditionGroupModel::getId).collect(Collectors.toList());
        // 查询所有条件
        List<AdsConditionModel> allConditions = conditionMapper.selectByGroupIds(groupIds);
        // 分组：groupId -> List<Condition>
        Map<Long, List<AdsConditionModel>> groupToConditionsMap = allConditions.stream()
                .collect(Collectors.groupingBy(AdsConditionModel::getGroupId));
        // 分组：ruleId -> List<Group>
        Map<Long, List<AdsConditionGroupModel>> ruleToGroupsMap = allGroups.stream()
                .collect(Collectors.groupingBy(AdsConditionGroupModel::getRuleId));
        // 构建 VO
        List<AdsMonitorRulesVO> ruleVOList = rules.stream().map(rule -> {
            //规则
            AdsMonitorRulesVO ruleVO = new AdsMonitorRulesVO();
            ruleVO.setRuleId(rule.getId());
            ruleVO.setRuleName(rule.getRuleName());
//            ruleVO.setActionType(RulesActionTypeEnum.of(rule.getActionType()).getDesc());
            ruleVO.setActionType(rule.getActionType());
            ruleVO.setSortOrder(rule.getSortOrder());
            //条件组
            List<AdsConditionGroupsVO> groupVOList = Optional.ofNullable(ruleToGroupsMap.get(rule.getId()))
                    .orElse(Collections.emptyList())
                    .stream().map(group -> {
                        AdsConditionGroupsVO groupVO = new AdsConditionGroupsVO();
                        groupVO.setGroupId(group.getId());
                        groupVO.setLogicalRelation(group.getLogicalRelation());
                        groupVO.setSortOrder(group.getSortOrder());
                        //条件
                        List<AdsConditionsVO> conditionVOList = Optional.ofNullable(groupToConditionsMap.get(group.getId()))
                                .orElse(Collections.emptyList())
                                .stream().map(condition -> {
                                    AdsConditionsVO conditionVO = new AdsConditionsVO();
                                    conditionVO.setConditionId(condition.getId());
                                    conditionVO.setDays(condition.getDays());
//                                    conditionVO.setOperator(ConditionOperatorEnum.of(condition.getOperator()).getDesc());
                                    conditionVO.setOperator(condition.getOperator());
                                    conditionVO.setFieldId(condition.getFieldId());
                                    conditionVO.setFieldName(condition.getFieldId() == 1l ? "点击次数" : "订单数");
                                    conditionVO.setFieldValue(condition.getFieldValue());
                                    return conditionVO;
                                }).collect(Collectors.toList());
                        groupVO.setConditions(conditionVOList);
                        return groupVO;
                    }).collect(Collectors.toList());
            ruleVO.setConditionGroups(groupVOList);
            return ruleVO;
        }).collect(Collectors.toList());

        map.put("data", ruleVOList);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    @Transactional
    public void updateCondition(String userId, UpdateConfigRequest request) {
        List<MonitorRulesDTO> rules = request.getRules();
        for (MonitorRulesDTO ruleDTO : rules) {
            if(null != ruleDTO.getRuleId()){
                AdsMonitorRulesModel rule = monitorRulesMapper.selectById(ruleDTO.getRuleId());
                rule.setActionType(ruleDTO.getActionType());
                monitorRulesMapper.updateById(rule);
            }
            for (ConditionGroupDTO groupDTO : ruleDTO.getConditionGroups()) {
                for (ConditionDTO condDTO : groupDTO.getConditions()) {
                    AdsConditionModel cond = new AdsConditionModel()
                            .setId(condDTO.getConditionId())
                            .setDays(condDTO.getDays())
                            .setOperator(condDTO.getOperator())
                            .setFieldValue(condDTO.getFieldValue())
                            .setUpdateBy(userId)
                            .setUpdateTime(new Date());
                    conditionMapper.updateById(cond);
                }
            }
        }
    }




}
