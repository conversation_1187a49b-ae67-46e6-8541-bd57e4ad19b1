package com.yiyitech.ads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.yiyitech.ads.mapper.AdsAccountProfileMapper;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.service.AdsAccountProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAccountProfileServiceImpl.java
 * @Description
 * @createTime 2023年12月25日 11:29:00
 */
@Slf4j
@Service
public class AdsAccountProfileServiceImpl extends ServiceImpl<AdsAccountProfileMapper, AdsAccountProfileModel> implements AdsAccountProfileService {

    @Override
    public boolean batchSaveOrUpdateByUniqueKey(List<AdsAccountProfileModel> list) {
        return SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, list, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            LambdaQueryWrapper<AdsAccountProfileModel> queryWrapper = Wrappers.<AdsAccountProfileModel>lambdaQuery()
                    .eq(AdsAccountProfileModel::getAccountId, entity.getAccountId())
                    .eq(AdsAccountProfileModel::getProfileId, entity.getProfileId());
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            LambdaUpdateWrapper<AdsAccountProfileModel> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(AdsAccountProfileModel::getAccountId, entity.getAccountId())
                    .eq(AdsAccountProfileModel::getProfileId, entity.getProfileId());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });
    }
}
