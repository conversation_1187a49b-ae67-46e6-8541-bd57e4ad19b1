package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.yiyitech.ads.mapper.AdsAdGroupMapper;
import com.yiyitech.ads.model.AdsAdGroupModel;
import com.yiyitech.ads.vo.AdsSpAdGroupCreateVO;
import com.yiyitech.ads.model.request.AdsSpAdGroupCreateRequest;
import com.yiyitech.ads.model.response.AdsSpAdGroupCreateResponse;
import com.yiyitech.ads.service.AdsAdGroupService;
import com.yiyitech.ads.service.api.AdsSpGroupsAPi;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.support.exception.BusinessException;
import com.yiyitech.support.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAdGroupServiceImpl.java
 * @Description
 * @createTime 2023年12月22日 13:27:00
 */
@Slf4j
@Service
public class AdsAdGroupServiceImpl extends ServiceImpl<AdsAdGroupMapper, AdsAdGroupModel> implements AdsAdGroupService {
    @Autowired
    private AdsAdGroupMapper adsAdGroupMapper;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsSpGroupsAPi adsSpGroupsAPi;


    @Override
    public boolean batchSaveOrUpdateByUniqueKey(List<AdsAdGroupModel> list) {
        return SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, list, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            LambdaQueryWrapper<AdsAdGroupModel> queryWrapper = Wrappers.<AdsAdGroupModel>lambdaQuery()
                    .eq(AdsAdGroupModel::getAccoutnId, entity.getAccoutnId())
                    .eq(AdsAdGroupModel::getProfileId, entity.getProfileId())
                    .eq(AdsAdGroupModel::getCampaignId, entity.getCampaignId())
                    .eq(AdsAdGroupModel::getAdGroupId, entity.getAdGroupId());
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            LambdaUpdateWrapper<AdsAdGroupModel> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(AdsAdGroupModel::getAccoutnId, entity.getAccoutnId())
                    .eq(AdsAdGroupModel::getProfileId, entity.getProfileId())
                    .eq(AdsAdGroupModel::getCampaignId, entity.getCampaignId())
                    .eq(AdsAdGroupModel::getAdGroupId, entity.getAdGroupId());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });
    }

    @Override
    public List<AdsAdGroupModel> searchBySearchTermReportParam(List<String> accountId, List<String> profileId, String adGroupName) {
        return adsAdGroupMapper.searchByAccIdByProIdByAdName(accountId, profileId, adGroupName);
    }

    @Override
    public Map<String, Object> createAdGroup(AdsSpAdGroupCreateVO param) throws BusinessException {
        Map<String, Object> map = new HashMap<>();
        //根据accountId获取店铺的accessToken
        String accessToken = redisUtils.getAccountAccessToken(param.getAccountId());
        //组装请求参数
        List<AdsSpAdGroupCreateRequest.AdGroupsData> adGroups = param.getAdGroups().stream().map(vo -> {
                    AdsSpAdGroupCreateRequest.AdGroupsData adGroupsData = new AdsSpAdGroupCreateRequest.AdGroupsData();
                    BeanCopyUtil.copyBean(vo, adGroupsData);
                    return adGroupsData;
                }).collect(Collectors.toList());
        AdsSpAdGroupCreateRequest request = new AdsSpAdGroupCreateRequest().setAdGroups(adGroups);
        //调用api创建广告组
        AdsSpAdGroupCreateResponse response = adsSpGroupsAPi.adGroupCreate(param.getAccountId(), param.getProfileId(), request);
        map.put("data", null);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }
}
