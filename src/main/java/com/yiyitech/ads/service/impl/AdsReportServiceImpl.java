package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.yiyitech.ads.mapper.AdsDailySearchTermReportMapper;
import com.yiyitech.ads.mapper.AdsDailyTargetingReportMapper;
import com.yiyitech.ads.mapper.AdsExchangeRateMapper;
import com.yiyitech.ads.model.AdsAdGroupModel;
import com.yiyitech.ads.model.AdsDailySearchTermReportModel;
import com.yiyitech.ads.model.AdsDailyTargetingReportModel;
import com.yiyitech.ads.model.AdsExchangeRateModel;
import com.yiyitech.ads.service.AdsAdGroupService;
import com.yiyitech.ads.service.AdsReportService;
import com.yiyitech.ads.vo.AdsReportVO;
import com.yiyitech.support.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsReportServiceImpl.java
 * @Description
 * @createTime 2023年12月19日 11:25:00
 */
@Slf4j
@Service
public class AdsReportServiceImpl implements AdsReportService {
    @Autowired
    private AdsDailySearchTermReportMapper dailySearchTermReportMapper;
    @Autowired
    private AdsDailyTargetingReportMapper dailyTargetingReportMapper;
    @Autowired
    private AdsExchangeRateMapper adsExchangeRateMapper;
    @Autowired
    private AdsAdGroupService adsAdGroupService;

//    @Override
//    @DS("master")
//    public Map<String, Object> searchDailySearchTermReportList(AdsReportVO adsReportVO) {
//        Map<String, Object> map = new HashMap<>();
//        List<AdsReportVO> voLst = new ArrayList<>();
//        List<AdsDailySearchTermReportModel> searchTermReportModelLst = dailySearchTermReportMapper.searchDailySearchTermReportModel(adsReportVO.getAccountId(), adsReportVO.getProfileId(), adsReportVO.getStartDate(), adsReportVO.getEndDate(), adsReportVO.getCampaignName(), adsReportVO.getAdGroupName(), adsReportVO.getSearchTerm(), adsReportVO.getKeyword());
//        if (CollectionUtils.isNotEmpty(searchTermReportModelLst)) {
//            voLst = searchTermReportModelLst.stream().map(model -> {
//                AdsReportVO vo = new AdsReportVO();
//                BeanCopyUtil.copyBean(model, vo);
//                return vo;
//            }).collect(Collectors.toList());
//        }
//        map.put("data", voLst);
//        map.put("status", HttpStatus.HTTP_OK);
//        return map;
//    }

    @Override
    @DS("master")
    public Map<String, Object> searchDailySearchTermReportList(AdsReportVO adsReportVO) {
        Map<String, Object> map = new HashMap<>();
        List<AdsReportVO> voLst = new ArrayList<>();
        List<String> accountIdLst = "".equals(adsReportVO.getAccountId()) ? null : Arrays.asList(adsReportVO.getAccountId().split(","));
        List<String> profileIdLst = "".equals(adsReportVO.getProfileId()) ? null : Arrays.asList(adsReportVO.getProfileId().split(","));
        String startDate = adsReportVO.getStartDate();
        String endDate = adsReportVO.getEndDate();
        //查询每日搜索词报告数据
        List<AdsDailySearchTermReportModel> searchTermReportModelLst = dailySearchTermReportMapper.searchDailySearchTermReportModel(accountIdLst, profileIdLst, startDate, endDate, adsReportVO.getCampaignName(), adsReportVO.getAdGroupName(), adsReportVO.getSearchTerm(), adsReportVO.getKeyword());
        //查询广告组列表
        List<AdsAdGroupModel> adGroupLst = adsAdGroupService.searchBySearchTermReportParam(accountIdLst, profileIdLst, adsReportVO.getAdGroupName());
        if (CollectionUtils.isNotEmpty(searchTermReportModelLst)) {
            //查询汇率表
            List<AdsExchangeRateModel> exchangeRateModelLst = adsExchangeRateMapper.searchByStatusByDate(startDate.substring(0, startDate.length() - 2) + "01", endDate.substring(0, endDate.length() - 2) + "01");
            //按原始币种分组
            Map<String, Map<String, AdsExchangeRateModel>> exchangeRateMap = exchangeRateModelLst.stream()
                    .collect(Collectors.groupingBy(AdsExchangeRateModel::getOriginalCurrencyCode,
                            Collectors.toMap(AdsExchangeRateModel::getEffectDate, exchangeRate -> exchangeRate)));
            //按同一搜索词分组报告数据
            Map<String, List<AdsDailySearchTermReportModel>> searchTermReportModelMap = searchTermReportModelLst.stream()
                    .collect(Collectors.toMap(model -> model.getAccountId() + "-" + model.getProfileId() + "-" + model.getCampaignId() + "-" + model.getAdGroupId() + "-" + model.getKeywordId() + "-" + model.getSearchTerm(),
                            Collections::singletonList,
                            (list1, list2) -> {
                                List<AdsDailySearchTermReportModel> combinedList = new ArrayList<>(list1);
                                combinedList.addAll(list2);
                                return combinedList;
                            }));
            //合并相同搜索词的跨天数据并按照页面选择换算汇率
            searchTermReportModelMap.entrySet().stream().forEach(entry -> {
                AdsReportVO vo = new AdsReportVO();
                BeanCopyUtil.copyBean(entry.getValue().get(0), vo);
                //给vo匹配词所在的活动组的状态
                adGroupLst.stream().filter(adGroup ->
                        Objects.equals(adGroup.getAccoutnId(), vo.getAccountId()) &&
                                Objects.equals(adGroup.getProfileId(), vo.getProfileId()) &&
                                Objects.equals(adGroup.getCampaignId(), vo.getCampaignId()) &&
                                Objects.equals(adGroup.getAdGroupId(), vo.getAdGroupId())).findFirst()// 只找第一个匹配的
                        .ifPresent(matchedAdGroup -> vo.setAdGroupStatus(matchedAdGroup.getState()));
                //报告原始货币
                String originalCurrencyExchangeRate = exchangeRateMap.get(vo.getCampaignBudgetCurrencyCode()).get(vo.getDate().substring(0, vo.getDate().length() - 2) + "01").getCExchangeRate();
                //要转换的目标货币
                String targetCurrencyExchangeRate = exchangeRateMap.get(adsReportVO.getCampaignBudgetCurrencyCode()).get(vo.getDate().substring(0, vo.getDate().length() - 2) + "01").getCExchangeRate();
                MathContext mc = new MathContext(4);
                //大于1则表示有多天数据，进行字段合并
                if (entry.getValue().size() > 1) {
                    AdsDailySearchTermReportModel calModel = entry.getValue().stream()
                            .collect(Collectors.reducing(new AdsDailySearchTermReportModel(0, 0, 0d, 0d, 0),
                                    (cal1, cal2) -> new AdsDailySearchTermReportModel(
                                            cal1.getImpressions()+cal2.getImpressions(),
                                            cal1.getClicks()+ cal2.getClicks(),
                                            cal1.getCost() + cal2.getCost(),
                                            cal1.getSales1d() + cal2.getSales1d(),
                                            cal1.getPurchases1d() + cal2.getPurchases1d())));
                    vo.setImpressions(calModel.getImpressions());
                    vo.setClicks(calModel.getClicks());
                    vo.setCost(calModel.getCost());
                    vo.setSales1d(calModel.getSales1d());
                    vo.setPurchases1d(calModel.getPurchases1d());
                }
                //计算百分率（CTR、CPC、ACOS、CVR）
                vo.setClickThroughRate(vo.getImpressions() > 0 ? new BigDecimal(vo.getClicks()).divide(new BigDecimal(vo.getImpressions()), 4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                vo.setCostPerClick(vo.getClicks() > 0 ? new BigDecimal(vo.getCost()).divide(new BigDecimal(vo.getClicks()), 2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                vo.setAcosClicks(vo.getSales1d() > 0 ? new BigDecimal(vo.getCost()).divide(new BigDecimal(vo.getSales1d()), 2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                vo.setCvr(vo.getClicks() > 0 ? new BigDecimal(vo.getPurchases1d()).divide(new BigDecimal(vo.getClicks()), 4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                //当前报告的货币和选择的货币不一样时通过汇率转换货币
                if (!vo.getCampaignBudgetCurrencyCode().equals(adsReportVO.getCampaignBudgetCurrencyCode())) {
                    if (null != vo.getKeywordBid()) {
                        vo.setKeywordBid(((new BigDecimal(vo.getKeywordBid()).multiply(new BigDecimal(originalCurrencyExchangeRate), mc)).divide(new BigDecimal(targetCurrencyExchangeRate), 2, BigDecimal.ROUND_HALF_UP)).doubleValue());
                    }
                    vo.setCost(((new BigDecimal(vo.getCost()).multiply(new BigDecimal(originalCurrencyExchangeRate), mc)).divide(new BigDecimal(targetCurrencyExchangeRate), 2, BigDecimal.ROUND_HALF_UP)).doubleValue());
                    vo.setSales1d(((new BigDecimal(vo.getSales1d()).multiply(new BigDecimal(originalCurrencyExchangeRate), mc)).divide(new BigDecimal(targetCurrencyExchangeRate), 2, BigDecimal.ROUND_HALF_UP)).doubleValue());
                    vo.setCostPerClick(((new BigDecimal(vo.getCostPerClick()).multiply(new BigDecimal(originalCurrencyExchangeRate), mc)).divide(new BigDecimal(targetCurrencyExchangeRate), 4, BigDecimal.ROUND_HALF_UP)).doubleValue());
                }
                voLst.add(vo);
            });
        }
        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> searchDailyTargetingReportList(AdsReportVO adsReportVO) {
        Map<String, Object> map = new HashMap<>();
        List<AdsReportVO> voLst = new ArrayList<>();
        List<String> accountIdLst = "".equals(adsReportVO.getAccountId()) ? null : Arrays.asList(adsReportVO.getAccountId().split(","));
        List<String> profileIdLst = "".equals(adsReportVO.getProfileId()) ? null : Arrays.asList(adsReportVO.getProfileId().split(","));
        String startDate = adsReportVO.getStartDate();
        String endDate = adsReportVO.getEndDate();
        //查询每日关键字报告数据
        List<AdsDailyTargetingReportModel> dailyTargetingReportModelLst = dailyTargetingReportMapper.searchDailyTargetingReportModel(accountIdLst, profileIdLst, startDate, endDate, adsReportVO.getCampaignName(), adsReportVO.getAdGroupName(), adsReportVO.getKeyword());
        //查询广告组列表
        List<AdsAdGroupModel> adGroupLst = adsAdGroupService.searchBySearchTermReportParam(accountIdLst, profileIdLst, adsReportVO.getAdGroupName());
        if (CollectionUtils.isNotEmpty(dailyTargetingReportModelLst)) {
            //查询汇率表
            List<AdsExchangeRateModel> exchangeRateModelLst = adsExchangeRateMapper.searchByStatusByDate(startDate.substring(0, startDate.length() - 2) + "01", endDate.substring(0, endDate.length() - 2) + "01");
            //按原始币种分组
            Map<String, Map<String, AdsExchangeRateModel>> exchangeRateMap = exchangeRateModelLst.stream()
                    .collect(Collectors.groupingBy(AdsExchangeRateModel::getOriginalCurrencyCode,
                            Collectors.toMap(AdsExchangeRateModel::getEffectDate, exchangeRate -> exchangeRate)));
            //按关键字分组报告数据
            Map<String, List<AdsDailyTargetingReportModel>> dailyTargetingReportModelMap = dailyTargetingReportModelLst.stream()
                    .collect(Collectors.groupingBy(AdsDailyTargetingReportModel::getKeywordId));
            //合并相同关键字的跨天数据并按照页面选择换算汇率
            dailyTargetingReportModelMap.entrySet().stream().forEach(entry -> {
                AdsReportVO vo = new AdsReportVO();
                BeanCopyUtil.copyBean(entry.getValue().get(0), vo);
                //给vo匹配词所在的活动组的状态
                adGroupLst.stream().filter(adGroup ->
                        Objects.equals(adGroup.getAccoutnId(), vo.getAccountId()) &&
                                Objects.equals(adGroup.getProfileId(), vo.getProfileId()) &&
                                Objects.equals(adGroup.getCampaignId(), vo.getCampaignId()) &&
                                Objects.equals(adGroup.getAdGroupId(), vo.getAdGroupId())).findFirst()// 只找第一个匹配的
                        .ifPresent(matchedAdGroup -> vo.setAdGroupStatus(matchedAdGroup.getState()));
                //报告原始货币
                String originalCurrencyExchangeRate = exchangeRateMap.get(vo.getCampaignBudgetCurrencyCode()).get(vo.getDate().substring(0, vo.getDate().length() - 2) + "01").getCExchangeRate();
                //要转换的目标货币
                String targetCurrencyExchangeRate = exchangeRateMap.get(adsReportVO.getCampaignBudgetCurrencyCode()).get(vo.getDate().substring(0, vo.getDate().length() - 2) + "01").getCExchangeRate();
                MathContext mc = new MathContext(4);
                //大于1则表示有多天数据，进行字段合并
                if (entry.getValue().size() > 1) {
                    AdsDailyTargetingReportModel calModel = entry.getValue().stream()
                            .collect(Collectors.reducing(new AdsDailyTargetingReportModel(0, 0, 0d, 0d, 0),
                                    (cal1, cal2) -> new AdsDailyTargetingReportModel(
                                            cal1.getImpressions()+cal2.getImpressions(),
                                            cal1.getClicks()+ cal2.getClicks(),
                                            cal1.getCost() + cal2.getCost(),
                                            cal1.getSales1d() + cal2.getSales1d(),
                                            cal1.getPurchases1d() + cal2.getPurchases1d())));
                    vo.setImpressions(calModel.getImpressions());
                    vo.setClicks(calModel.getClicks());
                    vo.setCost(calModel.getCost());
                    vo.setSales1d(calModel.getSales1d());
                    vo.setPurchases1d(calModel.getPurchases1d());
                }
                //计算百分率（CTR、CPC、ACOS、CVR）
                vo.setClickThroughRate(vo.getImpressions() > 0 ? new BigDecimal(vo.getClicks()).divide(new BigDecimal(vo.getImpressions()), 4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                vo.setCostPerClick(vo.getClicks() > 0 ? new BigDecimal(vo.getCost()).divide(new BigDecimal(vo.getClicks()), 2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                vo.setAcosClicks(vo.getSales1d() > 0 ? new BigDecimal(vo.getCost()).divide(new BigDecimal(vo.getSales1d()), 2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                vo.setCvr(vo.getClicks() > 0 ? new BigDecimal(vo.getPurchases1d()).divide(new BigDecimal(vo.getClicks()), 4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                //当前报告的货币和选择的货币不一样时通过汇率转换货币
                if (!vo.getCampaignBudgetCurrencyCode().equals(adsReportVO.getCampaignBudgetCurrencyCode())) {
                    if (null != vo.getKeywordBid()) {
                        vo.setKeywordBid(((new BigDecimal(vo.getKeywordBid()).multiply(new BigDecimal(originalCurrencyExchangeRate), mc)).divide(new BigDecimal(targetCurrencyExchangeRate), 2, BigDecimal.ROUND_HALF_UP)).doubleValue());
                    }
                    vo.setCost(((new BigDecimal(vo.getCost()).multiply(new BigDecimal(originalCurrencyExchangeRate), mc)).divide(new BigDecimal(targetCurrencyExchangeRate), 2, BigDecimal.ROUND_HALF_UP)).doubleValue());
                    vo.setSales1d(((new BigDecimal(vo.getSales1d()).multiply(new BigDecimal(originalCurrencyExchangeRate), mc)).divide(new BigDecimal(targetCurrencyExchangeRate), 2, BigDecimal.ROUND_HALF_UP)).doubleValue());
                    vo.setCostPerClick(((new BigDecimal(vo.getCostPerClick()).multiply(new BigDecimal(originalCurrencyExchangeRate), mc)).divide(new BigDecimal(targetCurrencyExchangeRate), 4, BigDecimal.ROUND_HALF_UP)).doubleValue());
                }
                voLst.add(vo);
            });
        }
        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

//    @Override
//    public Map<String, Object> searchDailyTargetingReportList(AdsReportVO adsReportVO) {
//        Map<String, Object> map = new HashMap<>();
//        List<AdsReportVO> voLst = new ArrayList<>();
//        List<AdsDailyTargetingReportModel> dailyTargetingReportModel = dailyTargetingReportMapper.searchDailyTargetingReportModel(adsReportVO.getAccountId(), adsReportVO.getProfileId(), adsReportVO.getStartDate(), adsReportVO.getEndDate(), adsReportVO.getCampaignName(), adsReportVO.getAdGroupName(), adsReportVO.getKeyword());
//        if (CollectionUtils.isNotEmpty(dailyTargetingReportModel)) {
//            Map<String, List<AdsDailyTargetingReportModel>> dailyTargetingReportModelMap = dailyTargetingReportModel.stream()
//                    .collect(Collectors.groupingBy(AdsDailyTargetingReportModel::getKeywordId));
//
//
//
//
//            voLst = dailyTargetingReportModel.stream().map(model -> {
//                AdsReportVO vo = new AdsReportVO();
//                BeanCopyUtil.copyBean(model, vo);
//                return vo;
//            }).collect(Collectors.toList());
//        }
//        map.put("data", voLst);
//        map.put("status", HttpStatus.HTTP_OK);
//        return map;
//    }

}
