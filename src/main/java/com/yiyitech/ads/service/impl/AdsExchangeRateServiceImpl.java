package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yiyitech.ads.mapper.AdsExchangeRateMapper;
import com.yiyitech.ads.model.AdsExchangeRateModel;
import com.yiyitech.ads.service.AdsExchangeRateService;
import com.yiyitech.ads.vo.AdsExchangeRateVO;
import com.yiyitech.support.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsExchangeRateServiceImpl.java
 * @Description
 * @createTime 2024年01月29日 15:31:00
 */
@Slf4j
@Service
public class AdsExchangeRateServiceImpl extends ServiceImpl<AdsExchangeRateMapper, AdsExchangeRateModel> implements AdsExchangeRateService {
    @Autowired
    private AdsExchangeRateService adsExchangeRateService;
    @Autowired
    private AdsExchangeRateMapper adsExchangeRateMapper;

    @Override
    public Map<String, Object> searchExchangeRateLst(String original, String target, String startDate, String endDate) {
        Map<String, Object> map = new HashMap<>();
        List<AdsExchangeRateVO> voLst = new ArrayList<>();
        List<AdsExchangeRateModel> exchangeRateModelLst = adsExchangeRateMapper.searchExchangeRateModelLst(original, target, startDate, endDate);
        if (CollectionUtils.isNotEmpty(exchangeRateModelLst)) {
            voLst = exchangeRateModelLst.stream().map(model -> {
                AdsExchangeRateVO vo = new AdsExchangeRateVO();
                BeanCopyUtil.copyBean(model, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> saveOrUpdateExchangeRate(AdsExchangeRateVO adsExchangeRateVO) {
        Map<String, Object> map = new HashMap<>();
        AdsExchangeRateModel model = new AdsExchangeRateModel();
        BeanCopyUtil.copyBean(adsExchangeRateVO, model);
        this.saveOrUpdate(model);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }
}
