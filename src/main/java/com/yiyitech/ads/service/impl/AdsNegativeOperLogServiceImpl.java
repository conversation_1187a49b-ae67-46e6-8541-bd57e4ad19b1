package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.enums.AdsMatchTypeEnum;
import com.yiyitech.ads.enums.AdsNegLogStatusEnum;
import com.yiyitech.ads.mapper.*;
import com.yiyitech.ads.model.*;
import com.yiyitech.ads.service.AdsNegativeOperLogService;
import com.yiyitech.ads.service.CommonService;
import com.yiyitech.ads.service.api.AdsNegativeApi;
import com.yiyitech.ads.util.AdsDataComparisonAggregatorUtil;
import com.yiyitech.ads.util.AdsDateUtil;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.ads.vo.*;
import com.yiyitech.ads.vo.search.AdsNegOperLogSearchVO;
import com.yiyitech.support.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperLogServiceImpl.java
 * @Description
 * @createTime 2025年07月03日 22:29:00
 */
@Slf4j
@Service
public class AdsNegativeOperLogServiceImpl implements AdsNegativeOperLogService {
    @Autowired
    private CommonService commonService;
    @Autowired
    private AdsNegativeOperLogMapper negOperLogMapper;
    @Autowired
    private AdsLogoutLogMapper logoutLogMapper;
    @Autowired
    private AdsUserAccountMapper adsUserAccountMapper;
    @Autowired
    private AdsDailySearchTermReportMapper dailySearchTermReportMapper;
    @Autowired
    private AdsDailyTargetingReportMapper dailyTargetingReportMapper;
    @Autowired
    private AdsNegativeApi adsNegativeApi;
    @Autowired
    private RedisUtils redisUtils;

    @Override
    public Map<String, Object> searhPushNotification(Long userId) {
        Map<String, Object> map = new HashMap<>();
        AdsPushNotificationLogVO result = new AdsPushNotificationLogVO();
        //当前用户拥有的店铺
        List<AdsAccountProfileModel> profileLst = (List<AdsAccountProfileModel>) commonService.searchFilterOrCurrentProfileInfo(userId, null).get("profiles");
        //当前用户拥有的店铺下的否词日志
        List<AdsNegativeOperLogModel> negOperLogModelLst = negOperLogMapper.searchByProfileLstByStatus(profileLst, Arrays.asList(AdsNegLogStatusEnum.NOTVIEWED.getCode(), AdsNegLogStatusEnum.UNTREATED.getCode()));
        //待查看记录
        List<AdsNegativeOperLogModel> notViewedList = negOperLogModelLst.stream()
                .filter(e -> e.getStatus() != null && e.getStatus() == AdsNegLogStatusEnum.NOTVIEWED.getCode())
                .collect(Collectors.toList());
        //待处理记录
        List<AdsNegativeOperLogModel> untreatedList = negOperLogModelLst.stream()
                .filter(e -> e.getStatus() != null && e.getStatus() == AdsNegLogStatusEnum.UNTREATED.getCode())
                .collect(Collectors.toList());
        // 统一按asin分组并转VO
        List<AdsPushNotificationLogVO.NegativeAsinVO> notification = groupToVOList(notViewedList, 1, null);
        notification.addAll(groupToVOList(untreatedList, 2, null));
        // 时间升序
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        notification.sort(Comparator.comparing(vo -> {
            try {
                return sdf.parse(vo.getTime());
            } catch (ParseException e) {
                return new Date(0);
            }
        }));

        result.setNotification(notification);
        result.setNum(result.getNotification().size());
        map.put("data", result);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> searhPush(Long userId) {
        Map<String, Object> map = new HashMap<>();
        AdsPushNotificationLogVO result = new AdsPushNotificationLogVO();
        //当前用户拥有的店铺
        List<AdsAccountProfileModel> profileLst = (List<AdsAccountProfileModel>) commonService.searchFilterOrCurrentProfileInfo(userId, null).get("profiles");
        List<Long> accProIds = profileLst.stream().map(AdsAccountProfileModel::getId).collect(Collectors.toList());
        //当前用户拥有店铺的登出记录
        List<AdsLogoutLogModel> logoutLst = logoutLogMapper.searchByAccProIds(accProIds);
        //登出记录按店铺分组
        Map<Long, Date> logoutMap = logoutLst.stream()
                .collect(Collectors.toMap(
                        AdsLogoutLogModel::getId,
                        AdsLogoutLogModel::getUpdateTime,
                        //保留最后一条updateTime记录（后续如果登出日志要全部保存时使用）
                        (existing, replacement) -> existing.after(replacement) ? existing : replacement
                ));
        //当前用户拥有的店铺下的否词日志
        List<AdsNegativeOperLogModel> negOperLogModelLst = negOperLogMapper.searchByProfileLstByStatus(profileLst, Arrays.asList(AdsNegLogStatusEnum.NOTVIEWED.getCode(), AdsNegLogStatusEnum.UNTREATED.getCode()));
        //所有记录按当前用户拥有的店铺分组
        Map<String, List<AdsNegativeOperLogModel>> groupedByAccPro = negOperLogModelLst.stream()
                .collect(Collectors.groupingBy(e -> e.getAccountId() + "_" + e.getProfileId()));

        Date thisLoginTime = new Date();
        List<AdsNegativeOperLogModel> allProNeg = new ArrayList<>();
        for (AdsAccountProfileModel profile : profileLst) {
            String accountId = profile.getAccountId();
            Long profileId = profile.getProfileId();
            //本店铺上次退出登陆的时间
            Date lastLogOutTime = logoutMap.get(profile.getId());
            if (lastLogOutTime == null) {
                continue;//没有lastLogOutTime跳过该profile
            }
            //当前店铺下所有记录
            String key = accountId + "_" + profileId;
            List<AdsNegativeOperLogModel> subList = groupedByAccPro.getOrDefault(key, Collections.emptyList());
            //待查看记录（当前店铺在上次登出——本次登陆期间的记录）
            List<AdsNegativeOperLogModel> offlineNotViewedList = subList.stream()
                    .filter(e -> e.getStatus() != null && e.getStatus() == AdsNegLogStatusEnum.NOTVIEWED.getCode() && inTimeRange(e.getUpdateTime(), lastLogOutTime, thisLoginTime))
                    .collect(Collectors.toList());
            allProNeg.addAll(offlineNotViewedList);
        }
        //统一按asin分组并转VO
        result.setPush(groupToVOList(allProNeg, 1, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(thisLoginTime)));
        result.setNum(result.getPush().size());
        map.put("data", result);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> checkAsinLog(Long userId, boolean isManager, String asin, Integer flag, String loginTime) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //当前用户拥有的店铺
        List<AdsAccountProfileModel> profileLst = (List<AdsAccountProfileModel>) commonService.searchFilterOrCurrentProfileInfo(userId, null).get("profiles");
        //当前用户拥有的店铺下「待查看」的否词日志
        List<AdsNegativeOperLogModel> negOperLogModelLst = negOperLogMapper.searchByProfileLstByStatusBYAsin(profileLst, asin);

        List<String> searchTermLst = new ArrayList<>();
        List<AdsNegativeOperLogModel> timeFileterModelLst = new ArrayList<>();
        if (2 == flag) {
            List<Long> accProIds = profileLst.stream().map(AdsAccountProfileModel::getId).collect(Collectors.toList());
            //当前用户拥有店铺的登出记录
            List<AdsLogoutLogModel> logoutLst = logoutLogMapper.searchByAccProIds(accProIds);
            //登出记录按店铺分组
            Map<Long, Date> logoutMap = logoutLst.stream()
                    .collect(Collectors.toMap(
                            AdsLogoutLogModel::getId,
                            AdsLogoutLogModel::getUpdateTime,
                            //保留最后一条updateTime记录（后续如果登出日志要全部保存时使用）
                            (existing, replacement) -> existing.after(replacement) ? existing : replacement
                    ));
            //所有记录按当前用户拥有的店铺分组
            Map<String, List<AdsNegativeOperLogModel>> groupedByAccPro = negOperLogModelLst.stream()
                    .collect(Collectors.groupingBy(e -> e.getAccountId() + "_" + e.getProfileId()));

            Date thisLoginTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(loginTime);
            for (AdsAccountProfileModel profile : profileLst) {
                String accountId = profile.getAccountId();
                Long profileId = profile.getProfileId();
                Date lastLogOutTime = logoutMap.get(profile.getId());
                if (lastLogOutTime == null) {
                    continue;//没有lastLogOutTime跳过该profile
                }
                //当前店铺下所有记录
                String key = accountId + "_" + profileId;
                List<AdsNegativeOperLogModel> subList = groupedByAccPro.getOrDefault(key, Collections.emptyList());
                //待查看记录（当前店铺在上次登出——本次登陆期间）
                List<AdsNegativeOperLogModel> offlineNotViewedList = subList.stream()
                        .filter(e -> e.getStatus() != null && e.getStatus() == AdsNegLogStatusEnum.NOTVIEWED.getCode() && inTimeRange(e.getUpdateTime(), lastLogOutTime, thisLoginTime))
                        .collect(Collectors.toList());
                timeFileterModelLst.addAll(offlineNotViewedList);
                searchTermLst = timeFileterModelLst.stream()
                        .peek(e -> {
                            e.setStatus(1);
                            e.setUpdateBy(userId.toString());
                            e.setUpdateTime(new Date());
                        })
                        .map(model -> model.getNegativeValueSign())
//                    .map(model -> model.getAccountId() + "-" +
//                            model.getProfileId() + "-" +
//                            model.getCampaignId() + "-" +
//                            model.getAdGroupId() + "-" +
//                            model.getNegativeValueSign())
                        .collect(Collectors.toList());
            }
        } else {
            searchTermLst = negOperLogModelLst.stream()
                    .peek(e -> {
                        e.setStatus(1);
                        e.setUpdateBy(userId.toString());
                        e.setUpdateTime(new Date());
                    })
                    .map(model -> model.getNegativeValueSign())
//                    .map(model -> model.getAccountId() + "-" +
//                            model.getProfileId() + "-" +
//                            model.getCampaignId() + "-" +
//                            model.getAdGroupId() + "-" +
//                            model.getNegativeValueSign())
                    .collect(Collectors.toList());

        }

        //查询每日搜索词报告数据
        List<AdsReportVO> voLst = new ArrayList<>();
        Map<String, String> lastDayStr = AdsDateUtil.getLastDays(30);
        List<AdsDailySearchTermReportModel> searchTermReportModelLst = dailySearchTermReportMapper.searchByDateBySearchTermLst(lastDayStr.get("startDateStr"), lastDayStr.get("endDateStr"), searchTermLst);

        if (CollectionUtils.isNotEmpty(searchTermReportModelLst)) {
            //按同一搜索词分组报告数据
            Map<String, List<AdsDailySearchTermReportModel>> searchTermReportModelMap = searchTermReportModelLst.stream()
                    .collect(Collectors.toMap(model -> model.getAccountId() + "-" + model.getProfileId() + "-" + model.getCampaignId() + "-" + model.getAdGroupId() + "-" + model.getKeywordId() + "-" + model.getSearchTerm(),
                            Collections::singletonList,
                            (list1, list2) -> {
                                List<AdsDailySearchTermReportModel> combinedList = new ArrayList<>(list1);
                                combinedList.addAll(list2);
                                return combinedList;
                            }));
            //合并相同搜索词的跨天数据
            searchTermReportModelMap.entrySet().stream().forEach(entry -> {
                AdsReportVO vo = new AdsReportVO();
                BeanCopyUtil.copyBean(entry.getValue().get(0), vo);
                MathContext mc = new MathContext(4);
                //大于1则表示有多天数据，进行字段合并
                if (entry.getValue().size() > 1) {
                    AdsDailySearchTermReportModel calModel = entry.getValue().stream()
                            .collect(Collectors.reducing(new AdsDailySearchTermReportModel(0, 0, 0d, 0d, 0),
                                    (cal1, cal2) -> new AdsDailySearchTermReportModel(
                                            cal1.getImpressions() + cal2.getImpressions(),
                                            cal1.getClicks() + cal2.getClicks(),
                                            cal1.getCost() + cal2.getCost(),
                                            cal1.getSales1d() + cal2.getSales1d(),
                                            cal1.getPurchases1d() + cal2.getPurchases1d())));
                    vo.setImpressions(calModel.getImpressions());
                    vo.setClicks(calModel.getClicks());
                    vo.setCost(calModel.getCost());
                    vo.setSales1d(calModel.getSales1d());
                    vo.setPurchases1d(calModel.getPurchases1d());
                }
                //计算百分率（CTR、CPC、ACOS、CVR）
                vo.setClickThroughRate(vo.getImpressions() > 0 ? new BigDecimal(vo.getClicks()).divide(new BigDecimal(vo.getImpressions()), 4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                vo.setCostPerClick(vo.getClicks() > 0 ? new BigDecimal(vo.getCost()).divide(new BigDecimal(vo.getClicks()), 2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                vo.setAcosClicks(vo.getSales1d() > 0 ? new BigDecimal(vo.getCost()).divide(new BigDecimal(vo.getSales1d()), 2, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                vo.setCvr(vo.getClicks() > 0 ? new BigDecimal(vo.getPurchases1d()).divide(new BigDecimal(vo.getClicks()), 4, BigDecimal.ROUND_HALF_UP).doubleValue() : 0d);
                vo.setUniqueSign(entry.getKey());
                voLst.add(vo);
            });
        }

        negOperLogMapper.updateBatchById(AdsNegativeOperLogModel.class, 1 == flag ? negOperLogModelLst : timeFileterModelLst, 100);
        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> dataComparison(String uniqueSign, Integer comparisonType) {
        Map<String, Object> map = new HashMap<>();
        AdsAsinDataComparisonVO vo = new AdsAsinDataComparisonVO();
        //关键词统计
        if (1 == comparisonType) {
            List<AdsDailyTargetingReportModel> targetingReportModelLst = dailyTargetingReportMapper.searchLstByUniqueSign(Arrays.asList(uniqueSign));
            // 调用通用聚合方法进行计算
            vo = AdsDataComparisonAggregatorUtil.aggregate(
                    targetingReportModelLst,
                    AdsDailyTargetingReportModel::getDate,
                    AdsDailyTargetingReportModel::getClicks,
                    AdsDailyTargetingReportModel::getPurchases1d
            );
        }
        //搜索词统计
        if (2 == comparisonType) {
            List<AdsDailySearchTermReportModel> searchTermReportModelLst = dailySearchTermReportMapper.searchLstByUniqueSign(Arrays.asList(uniqueSign));
            // 调用通用聚合方法进行计算
            vo = AdsDataComparisonAggregatorUtil.aggregate(
                    searchTermReportModelLst,
                    AdsDailySearchTermReportModel::getDate,
                    AdsDailySearchTermReportModel::getClicks,
                    AdsDailySearchTermReportModel::getPurchases1d
            );
        }

        map.put("data", vo);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> searhNegOperLogLst(LoginUser loginUser, AdsNegOperLogSearchVO param) {
        Map<String, Object> map = new HashMap<>();
        //当前用户拥有的店铺
        List<AdsAccountProfileModel> profileLst = (List<AdsAccountProfileModel>) commonService.searchFilterOrCurrentProfileInfo(Long.valueOf(loginUser.getId()), param.getAccProId()).get("profiles");
        //当前用户拥有的店铺下的否词日志
        Map<String, String> timeRange = AdsDateUtil.buildFullDayTimeRange(param.getStartDate(), param.getEndDate());
        List<AdsNegativeOperLogModel> negOperLogModelLst = negOperLogMapper.searchByProfileLstLstByDateByStatus(profileLst, timeRange.get("startTime"), timeRange.get("endTime"), param.getStatus());

        List<AdsNegativeLogLstVO> resultLst = negOperLogModelLst.stream().map(model -> {
            AdsNegativeLogLstVO vo = new AdsNegativeLogLstVO();
            BeanUtils.copyProperties(model, vo, "operLevel", "updateTime", "afterOper");
            //自定义转换
            if (AdsNegLogStatusEnum.UNTREATED.getCode() != model.getStatus()) {
                vo.setOperLevel(model.getOperLevel());
                vo.setAfterOper(model.getAfterOper());
            }
            if (model.getUpdateTime() != null) {
                vo.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(model.getUpdateTime()));
            }
            return vo;
        }).collect(Collectors.toList());
        map.put("data", resultLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public void logProcessing(LoginUser loginUser, AdsNegativeOperateVO param) {
        //TODO 事务配置
        List<AdsNegativeOperateVO.OperateInfo> operateLst = param.getOperateLst();
        for (AdsNegativeOperateVO.OperateInfo operate : operateLst) {
            //日志处理：更新日志信息
            AdsNegativeOperLogModel negativeOperLogModel = negOperLogMapper.selectById(operate.getRecordId());

            negativeOperLogModel.setNegativeType(null == operate.getSubInfo() ? operate.getMatchType() : AdsMatchTypeEnum.EXACT_AND_PHRASE.getCode());
            negativeOperLogModel.setOperLevel(operate.getScope());
            negativeOperLogModel.setAfterOper(null == operate.getSubInfo() ? AdsMatchTypeEnum.of(operate.getMatchType()).getDesc() + "-" + operate.getValue() : AdsMatchTypeEnum.of(operate.getMatchType()).getDesc() + "-" + operate.getValue() + "+" + AdsMatchTypeEnum.of(operate.getSubInfo().getMatchType()).getDesc() + "-" + operate.getSubInfo().getValue());
            negativeOperLogModel.setStatus(AdsNegLogStatusEnum.COMPLETE.getCode());
            negativeOperLogModel.setUpdateBy(loginUser.getId());
            negativeOperLogModel.setUpdateTime(new Date());
            negOperLogMapper.updateById(negativeOperLogModel);

            //调用sp接口进行否词
            String accountId = negativeOperLogModel.getAccountId();
            Long profileId = negativeOperLogModel.getProfileId();
            String campaignId = negativeOperLogModel.getCampaignId();
            String adGroupId = negativeOperLogModel.getAdGroupId();

            String spAccessToken = redisUtils.getAccountAccessTokenAndRefreshToken(accountId, CommonConstants.SP_PREFIX_KEY);
            if (operate.getMatchType() == AdsMatchTypeEnum.ADV_ASIN.getCode()) {
                adsNegativeApi.negativeTargeting(spAccessToken, profileId.toString(), campaignId, adGroupId, operate.getValue(), operate.getMatchType());
            } else {
                adsNegativeApi.negativeKeywords(spAccessToken, profileId.toString(), campaignId, adGroupId, operate.getValue(), operate.getMatchType());
                if (null != operate.getSubInfo()) {
                    adsNegativeApi.negativeKeywords(spAccessToken, profileId.toString(), campaignId, adGroupId, operate.getSubInfo().getValue(), operate.getSubInfo().getMatchType());

                }
            }
        }
    }

    private boolean inTimeRange(Date t, Date start, Date end) {
        return t != null && !t.before(start) && !t.after(end);
    }

    private List<AdsPushNotificationLogVO.NegativeAsinVO> groupToVOList(List<AdsNegativeOperLogModel> list, Integer flag, String loginTime) {
        return list.stream()
                .filter(e -> StringUtils.isNotBlank(e.getAsin())) // 过滤无效asin
                .collect(Collectors.groupingBy(AdsNegativeOperLogModel::getAsin))
                .entrySet()
                .stream()
                .map(entry -> {
                    List<AdsNegativeOperLogModel> asinGroup = entry.getValue();
                    AdsNegativeOperLogModel first = asinGroup.stream()
                            .min(Comparator.comparing(AdsNegativeOperLogModel::getCreateTime))
                            .orElse(null);
                    if (first == null) return null;

                    AdsPushNotificationLogVO.NegativeAsinVO vo = new AdsPushNotificationLogVO.NegativeAsinVO();
                    vo.setAsin(entry.getKey());
                    vo.setPictureUrl(first.getPictureUrl());
                    vo.setIteamName(first.getAsinItemName());
                    vo.setNum(asinGroup.size());
                    vo.setTime(first.getCreateTime() != null
                            ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(first.getCreateTime())
                            : null);
                    vo.setFlag(flag);
                    vo.setLoginTime(loginTime);
                    return vo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
