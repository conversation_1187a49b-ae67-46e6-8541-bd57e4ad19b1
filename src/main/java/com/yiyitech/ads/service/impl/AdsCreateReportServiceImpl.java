package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.yiyitech.ads.mapper.AdsCreateReportMapper;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.AdsCreateReportModel;
import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.service.AdsCreateReportService;
import com.yiyitech.ads.service.CommonService;
import com.yiyitech.ads.util.AdsDateUtil;
import com.yiyitech.ads.vo.AdsDataRefreshCreateVO;
import com.yiyitech.ads.vo.search.AdsNegOperSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCreateReportServiceImpl.java
 * @Description
 * @createTime 2025年07月10日 14:15:00
 */
@Slf4j
@Service
public class AdsCreateReportServiceImpl implements AdsCreateReportService {
    @Autowired
    private AdsCreateReportMapper createReportMapper;
    @Autowired
    private CommonService commonService;

    @Override
    public Map<String, Object> dataRefresh(LoginUser loginUser, AdsDataRefreshCreateVO param) {
        Map<String, Object> map = new HashMap<>();
        String lastRefreashTime = "";
        //当前用户拥有的店铺
        List<AdsAccountProfileModel> profileLst = (List<AdsAccountProfileModel>) commonService.searchFilterOrCurrentProfileInfo(Long.valueOf(loginUser.getId()), param.getAccProId()).get("profiles");
        //刷新数据并返回最近更新时间
        if (param.getRefresh()) {



            lastRefreashTime = AdsDateUtil.formatDate(new Date());
        }

        //返回最近更新时间
        if (!param.getRefresh()) {
            List<AdsCreateReportModel> createReportModelLst = createReportMapper.searchByProfileLst(profileLst);
            if(!CollectionUtils.isEmpty(createReportModelLst)){
                lastRefreashTime = AdsDateUtil.formatTimestamp(createReportModelLst.get(0).getCreatedAt());
            }
        }
        map.put("data", lastRefreashTime);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

}
