package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.mapper.AdsSpCategoryMapper;
import com.yiyitech.ads.model.AdsSpCategoryModel;
import com.yiyitech.ads.model.response.AdsSpCategoryResponse;
import com.yiyitech.ads.service.AdsSpCategoryService;
import com.yiyitech.ads.service.api.AdsSpCategoryApi;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.ads.vo.AdsSpCategoryVO;
import com.yiyitech.ads.vo.search.AdsSpCategorySearchVO;
import com.yiyitech.support.exception.BusinessException;
import com.yiyitech.support.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpCategoryServiceImpl.java
 * @Description
 * @createTime 2025年02月18日 13:46:00
 */
@Slf4j
@Service
public class AdsSpCategoryServiceImpl extends ServiceImpl<AdsSpCategoryMapper, AdsSpCategoryModel> implements AdsSpCategoryService {
    @Autowired
    private AdsSpCategoryMapper adsSpCategoryMapper;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsSpCategoryApi adsSpCategoryApi;

    @Override
    public Map<String, Object> insertSpCategorys(String accountId, AdsSpCategorySearchVO param) {
        Map<String, Object> map = new HashMap<>();
        List<AdsSpCategoryVO> voLst = new ArrayList<>();
        if (null == accountId || null == param.getProfileId()) {
            map.put("data", voLst);
            map.put("status", HttpStatus.HTTP_BAD_REQUEST);
        }
        //根据accountId获取店铺的accessToken
        String accessToken = redisUtils.getAccountAccessToken(accountId);
        //调用获取站点所有品类api
        AdsSpCategoryResponse spCategoryResponse = adsSpCategoryApi.getSpCategory(accessToken, param.getProfileId());
        //解析api返回结果为AdsSpCategoryVO
        ObjectMapper objectMapper = new ObjectMapper();
        List<AdsSpCategoryVO> categories = new ArrayList<>();
        try {
            String categoryTreeJson = spCategoryResponse.getCategoryTree();
            if (categoryTreeJson != null && !categoryTreeJson.isEmpty()) {
                categories = objectMapper.readValue(categoryTreeJson, new TypeReference<List<AdsSpCategoryVO>>() {
                });
            }
        } catch (JsonProcessingException e) {
            log.error("站点品类信息获取或解析异常" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
        //遍历解析结果，展开子类到vo集合
        flattenCategoryList(categories, null, voLst);
        //组装model对象集合
        List<AdsSpCategoryModel> batchSpCategoryLst = new ArrayList<>();
        voLst.forEach(vo -> {
            AdsSpCategoryModel model = new AdsSpCategoryModel().setAccoutnId(accountId).setProfileId(Long.valueOf(param.getProfileId()));
            BeanUtils.copyProperties(vo, model, "id");
            batchSpCategoryLst.add(model);
        });
        //品类信息入库
        adsSpCategoryMapper.batchInsert(AdsSpCategoryModel.class, batchSpCategoryLst, 100);
        map.put("data", null);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    private static void flattenCategoryList(List<AdsSpCategoryVO> categories, Long parentId, List<AdsSpCategoryVO> voLst) {
        for (AdsSpCategoryVO vo : categories) {
            vo.setParentId(parentId);
            vo.setCategoryId(vo.getId());
            vo.setCategoryName(vo.getNa());
            voLst.add(vo);
            if (vo.getCh() != null && !vo.getCh().isEmpty()) {
                flattenCategoryList(vo.getCh(), vo.getId(), voLst);
            }
            vo.setCh(null);
        }
    }

    @Override
    public Map<String, Object> searchSpCategorys(AdsSpCategorySearchVO param) throws BusinessException {
        Map<String, Object> map = new HashMap<>();
        List<AdsSpCategoryVO> voLst = new ArrayList<>();
        List<String> accountIdLst = null == param.getAccountId() ? null : Arrays.asList(param.getAccountId().split(","));
        List<String> profileIdLst = null == param.getProfileId() ? null : Arrays.asList(param.getProfileId().split(","));
        List<AdsSpCategoryModel> modelLSt = adsSpCategoryMapper.searchByAccIdsByProIdsByCatId(accountIdLst, profileIdLst, param.getCategoryId());
        modelLSt.forEach(model -> {
            AdsSpCategoryVO vo = new AdsSpCategoryVO();
            BeanCopyUtil.copyBean(model, vo);
            voLst.add(vo);
        });
        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }
}
