package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.yiyitech.ads.constants.CommonConstants;
import com.yiyitech.ads.mapper.AdsAccountInfoMapper;
import com.yiyitech.ads.mapper.AdsAccountProfileMapper;
import com.yiyitech.ads.mapper.AdsUserAccountMapper;
import com.yiyitech.ads.model.AdsAccountInfoModel;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.AdsUserAccountModel;
import com.yiyitech.ads.model.AuthState;
import com.yiyitech.ads.model.response.AdsProfileResponse;
import com.yiyitech.ads.model.response.AdsTokenResponse;
import com.yiyitech.ads.model.response.SPTokenResponse;
import com.yiyitech.ads.model.response.SpMarketplacePartResponse;
import com.yiyitech.ads.service.AdsAccountProfileService;
import com.yiyitech.ads.service.AdsAccountService;
import com.yiyitech.ads.service.api.AdsProfilesApi;
import com.yiyitech.ads.service.api.AdsTokenApi;
import com.yiyitech.ads.service.apisp.SpTokenApi;
import com.yiyitech.ads.util.AdsCountryUtils;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.ads.vo.AdsAccountVO;
import com.yiyitech.ads.vo.search.AdsAccountSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAccountServiceImpl.java
 * @Description
 * @createTime 2023年12月13日 16:34:00
 */
@Slf4j
@Service
public class AdsAccountServiceImpl extends ServiceImpl<AdsAccountInfoMapper, AdsAccountInfoModel> implements AdsAccountService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsCountryUtils adsCountryUtils;
    @Autowired
    private AdsTokenApi adsTokenApi;
    @Autowired
    private SpTokenApi spTokenApi;
    @Autowired
    private AdsProfilesApi adsProfilesApi;
    @Autowired
    private AdsAccountProfileService adsAccountProfileService;
    @Autowired
    private AdsAccountInfoMapper adsAccountInfoMapper;
    @Autowired
    private AdsAccountProfileMapper adsAccountProfileMapper;
    @Autowired
    private AdsUserAccountMapper adsUserAccountMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void accessSpToken(String code, AuthState state, String sellerId) {
        Date date = new Date();
        //获取token
        SPTokenResponse spTokenResponse = spTokenApi.accessToken(code);
        //获取店铺基本信息
        SpMarketplacePartResponse sellerInfo = spTokenApi.getMarketplaceParticipations(spTokenResponse.getAccessToken());
        //账户信息组装
        AdsAccountInfoModel accountInfoModel = new AdsAccountInfoModel()
                .setUserId(Long.valueOf(state.getUserId()))
                .setAccountId(sellerId)
                .setName(sellerInfo.getPayload().get(0).getStoreName())
                .setRefreshTokenSp(spTokenResponse.getRefreshToken())
                .setStatus(CommonConstants.FORM_STATUS_EFFECTIVE)
                .setCreateBy("system")
                .setUpdateBy("system")
                .setCreateTime(date)
                .setUpdateTime(date);
        //事务持久账户信息
        adsAccountInfoMapper.insert(accountInfoModel);
        //主键已被自动注入
        Long generatedAccountId = accountInfoModel.getId();
        //站点信息组装
        String siteName = state.getSiteName();
        List<String> siteLst = state.getSiteLst();
        List<Long> generatedAccountProId = new ArrayList<>();
//        List<AdsAccountProfileModel> profileLst = new ArrayList<>();
        sellerInfo.getPayload().forEach(payload -> {
            if (siteLst.contains(payload.getMarketplace().getCountryCode())) {
                AdsAccountProfileModel saveUpdateModel = new AdsAccountProfileModel()
                        .setAccountId(accountInfoModel.getAccountId())
                        .setName(StringUtils.isBlank(siteName) ? payload.getMarketplace().getCountryCode() : siteName)
                        .setCountryCode(payload.getMarketplace().getCountryCode())
                        .setCurrencyCode(adsCountryUtils.getCurrencyCode(payload.getMarketplace().getCountryCode()))
                        .setCreateBy("system")
                        .setUpdateBy("system")
                        .setCreateTime(date)
                        .setUpdateTime(date);
                adsAccountProfileMapper.insert(saveUpdateModel);
                generatedAccountProId.add(saveUpdateModel.getId());
//                profileLst.add(saveUpdateModel);
            }
        });

        for(Long accProId : generatedAccountProId){
            AdsUserAccountModel userAccountModel = new AdsUserAccountModel()
                    .setUserId(Long.valueOf(state.getUserId()))
                    .setAccountId(generatedAccountId)
                    .setAccountProId(accProId)
                    .setType(1)
                    .setStatus(true)
                    .setCreateBy("system")
                    .setUpdateBy("system")
                    .setCreateTime(date)
                    .setUpdateTime(date);
            adsUserAccountMapper.insert(userAccountModel);
        }
        //事务持久(账户信息，账户站点新)
//        adsAccountInfoMapper.insert(accountInfoModel);
//        adsAccountProfileService.batchSaveOrUpdateByUniqueKey(profileLst);
        //缓存ads token
        try {
            redisUtils.setAccountAccessTokenAndRefreshToken(accountInfoModel.getAccountId(), spTokenResponse.getRefreshToken(), spTokenResponse.getAccessToken(), CommonConstants.SP_PREFIX_KEY);
        } catch (Exception e) {
            log.error("state = "+state+"||店铺授权缓存Token失败", e);
        }
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void accessToken(String code, AuthState state) {
        //调用ads-api获取账号和站点信息
        AdsTokenResponse tokenResponse = adsTokenApi.accessToken(code);
        List<AdsProfileResponse> profileResponseLst = adsProfilesApi.AdsProfilesApi(tokenResponse.getAccessToken());
        AdsProfileResponse firstProfile = profileResponseLst.stream() .findFirst().orElseThrow(() -> new RuntimeException("无广告账户Profile"));
        //回调的自定义参数
        String accountId = state.getAccountId();
        String site = state.getSiteLst().stream().findFirst().orElseThrow(() -> new RuntimeException("没有选择站点！"));;
        //查询要授权的账户以及账户站点信息
        AdsAccountInfoModel accountInfo = adsAccountInfoMapper.searchByAccountId(accountId)
                .stream().findFirst()
                .orElseThrow(() -> new RuntimeException("账户不存在"));
        AdsAccountProfileModel accountProfile = adsAccountProfileMapper.searchByAccountIdByCountry(accountId, site)
                .stream().findFirst()
                .orElseThrow(() -> new RuntimeException("账户配置不存在"));
        //更新数据
        accountInfo
                .setRefreshToken(tokenResponse.getRefreshToken())
                .setType(firstProfile.getAccountInfo().getType())
                .setValidPayment(firstProfile.getAccountInfo().getValidPaymentMethod());

//                profileResponseLst.stream()
//                .filter(profile -> site.equals(profile.getCountryCode()))
//                .forEach(profile -> accountProfile.setProfileId(profile.getProfileId()));
        profileResponseLst
                .stream()
                .filter(profile -> site.equals(profile.getCountryCode()))
                .findFirst()
                .ifPresent(profile -> accountProfile.setProfileId(profile.getProfileId()));

        //事务持久
        adsAccountInfoMapper.updateById(accountInfo);
        adsAccountProfileMapper.updateById(accountProfile);
        //缓存ads token
        try {
            redisUtils.setAccountAccessTokenAndRefreshToken(accountId, tokenResponse.getRefreshToken(), tokenResponse.getAccessToken(), CommonConstants.ADS_PREFIX_KEY);
        } catch (Exception e) {
            log.error("state = "+state+"||广告授权缓存Token失败", e);
        }
    }

    @Override
    public Map<String, Object> getAccList(Long userId, AdsAccountSearchVO param) {
        Map<String, Object> map = new HashMap<>();

        List<Long> paramAccProIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getAccProId())) {
            paramAccProIdList = Arrays.stream(param.getAccProId().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
        //查询当前用户有哪些店铺和站点
        List<AdsUserAccountModel> userAccLst = adsUserAccountMapper.searchByUserIdByStatus(userId, true);
        //查询当前用户的站点信息
        List<AdsAccountProfileModel> accProModelLst = adsAccountProfileMapper.selectBatchIds(paramAccProIdList.size() <= 0 ? userAccLst.stream().map(AdsUserAccountModel::getAccountProId).collect(Collectors.toList()) : paramAccProIdList);
        //把站点信息按AccountId分组
        Map<String, List<AdsAccountProfileModel>> profileMap = accProModelLst.stream()
                .collect(Collectors.groupingBy(AdsAccountProfileModel::getAccountId));
        //获取当前用户拥有的账户id的集合
        List<String> accountIdList = accProModelLst.stream()
                .map(AdsAccountProfileModel::getAccountId)
                .filter(Objects::nonNull)
                .distinct() // 去重
                .collect(Collectors.toList());
        //按AccountId集合查询这些店铺的基本信息
        List<AdsAccountInfoModel> accInfoModelLst = adsAccountInfoMapper.searchByAccountIdsByStatusByIsAuth(accountIdList, param.getSpAuth(), param.getAdsAuth(), true);
        //遍历店铺信息列表组装VO
        List<AdsAccountVO> voLst = accInfoModelLst.stream()
                .map(info -> {
                    AdsAccountVO vo = new AdsAccountVO()
                            .setAccountInfoId(info.getId().toString())
                            .setAccountId(info.getAccountId())
                            .setAccountName(info.getName())
                            .setRefreshTokenSp(info.getRefreshTokenSp())
                            .setRefreshToken(info.getRefreshToken() != null ? info.getRefreshToken() : null)
                            .setStatus(info.getStatus());
                    List<AdsAccountVO.Profile> profiles = profileMap.getOrDefault(info.getAccountId(), Collections.emptyList())
                            .stream()
                            .map(profile -> new AdsAccountVO.Profile()
                                    .setAccProId(profile.getId().toString())
                                    .setProfileId(profile.getProfileId() != null ? profile.getProfileId().toString() : null)
                                    .setProName(profile.getName())
                                    .setProCountry(adsCountryUtils.getCountryNameByCode(profile.getCountryCode()))
                                    .setProCurrency(adsCountryUtils.getCurrencyNameByCode(profile.getCurrencyCode())))
                            .collect(Collectors.toList());
                    vo.setAccountProfiles(profiles);
                    return vo;
                }).collect(Collectors.toList());
        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public boolean batchSaveOrUpdateByUniqueKey(List<AdsAccountInfoModel> list) {
        return SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, list, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            LambdaQueryWrapper<AdsAccountInfoModel> queryWrapper = Wrappers.<AdsAccountInfoModel>lambdaQuery()
                    .eq(AdsAccountInfoModel::getAccountId, entity.getAccountId());
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            LambdaUpdateWrapper<AdsAccountInfoModel> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(AdsAccountInfoModel::getAccountId, entity.getAccountId());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });
    }

//        @Override
//    public boolean saveOrUpdateBatchByAgentIdAndPeriodAndType(List<AdsAccountInfoModel> list) {
//        return SqlHelper.executeBatch(entityClass, log, list, 100, (sqlSession, entity) -> {
//            HashMap param = new HashMap();
//            LambdaQueryWrapper<AdsAccountInfoModel> eq = Wrappers.<AdsAccountInfoModel>lambdaQuery()
//                    .eq(AdsAccountInfoModel::getAccoutnId, entity.getAccoutnId());
//            param.put("ew", eq);
//            AdsAccountInfoModel model = sqlSession.selectOne(getSqlStatement(SqlMethod.SELECT_ONE), param);
//            if (model == null) {
//                sqlSession.insert(getSqlStatement(SqlMethod.INSERT_ONE), entity);
//            } else {
//                entity.setId(model.getId());
//                param.put("et",entity);
//                sqlSession.update(getSqlStatement(SqlMethod.UPDATE_BY_ID), param);
//            }
//        });
//    }


}
