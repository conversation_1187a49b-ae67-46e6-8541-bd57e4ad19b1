package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.yiyitech.ads.model.request.AdsPortfolioSearchRequest;
import com.yiyitech.ads.model.response.AdsPortfolioSearchResponse;
import com.yiyitech.ads.service.AdsPortfolioService;
import com.yiyitech.ads.service.api.AdsPortfolioApi;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.ads.vo.AdsPortfolioVO;
import com.yiyitech.ads.vo.search.AdsPortfolioSearchVO;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsPortfolioServiceImpl.java
 * @Description
 * @createTime 2025年02月18日 20:35:00
 */
@Slf4j
@Service
public class AdsPortfolioServiceImpl implements AdsPortfolioService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsPortfolioApi adsPortfolioApi;

    @Override
    public Map<String, Object> searchPortfolioList(String accountId, AdsPortfolioSearchVO param) throws BusinessException {
        Map<String, Object> map = new HashMap<>();
        List<AdsPortfolioVO> voLst = new ArrayList<>();
        if (null == accountId || null == param.getProfileId()) {
            map.put("data", voLst);
            map.put("status", HttpStatus.HTTP_BAD_REQUEST);
        }
        if(null == param.getState() || param.getState().size() <= 0){
            param.setState(Arrays.asList("ENABLED"));
        }
        //根据accountId获取店铺的accessToken
        String accessToken = redisUtils.getAccountAccessToken(accountId);
        //api请求参数
        AdsPortfolioSearchRequest request = new AdsPortfolioSearchRequest()
                .setStateFilter(new AdsPortfolioSearchRequest.StateFilterData().setInclude(param.getState()))
                .setIncludeExtendedDataFields(true);
        //调用api获取文件夹信息
        List<AdsPortfolioSearchResponse.PortfoliosData> apiResponse = adsPortfolioApi.portfolioSearch(accessToken, param.getProfileId(), request).getPortfolios();
        //解析api结果生成voLst
        apiResponse.stream().forEach(response -> {
            AdsPortfolioVO vo = new AdsPortfolioVO()
                    .setProfileId(param.getProfileId())
                    .setInBudget(response.isInBudget())
                    .setName(response.getName())
                    .setPortfolioId(response.getPortfolioId())
                    .setState(response.getState());
            voLst.add(vo);
        });
        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }
}
