package com.yiyitech.ads.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.yiyitech.ads.mapper.AdsKeywordMapper;
import com.yiyitech.ads.model.AdsKeywordModel;
import com.yiyitech.ads.model.request.AdsSpKeywordsCreateRequest;
import com.yiyitech.ads.model.request.AdsSpKeywordsRecRequest;
import com.yiyitech.ads.model.response.AdsSpKeywordsCreateResponse;
import com.yiyitech.ads.model.response.AdsSpKeywordsRecResponse;
import com.yiyitech.ads.service.AdsKeywordService;
import com.yiyitech.ads.service.api.AdsSpKeywordsAPi;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.ads.vo.search.AdsCreateKeywordsVO;
import com.yiyitech.ads.vo.AdsRecKeywordVO;
import com.yiyitech.ads.vo.search.AdsKeywordSearchVO;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsKeywordServiceImpl.java
 * @Description
 * @createTime 2023年12月22日 13:27:00
 */
@Slf4j
@Service
public class AdsKeywordServiceImpl extends ServiceImpl<AdsKeywordMapper, AdsKeywordModel> implements AdsKeywordService {
    @Autowired
    private AdsKeywordMapper adsKeywordMapper;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsSpKeywordsAPi adsSpKeywordsAPi;

    @Override
    public boolean batchSaveOrUpdateByUniqueKey(List<AdsKeywordModel> list) {
        return SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, list, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            LambdaQueryWrapper<AdsKeywordModel> queryWrapper = Wrappers.<AdsKeywordModel>lambdaQuery()
                    .eq(AdsKeywordModel::getAccoutnId, entity.getAccoutnId())
                    .eq(AdsKeywordModel::getProfileId, entity.getProfileId())
                    .eq(AdsKeywordModel::getCampaignId, entity.getCampaignId())
                    .eq(AdsKeywordModel::getAdGroupId, entity.getAdGroupId())
                    .eq(AdsKeywordModel::getKeywordId, entity.getKeywordId());
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            LambdaUpdateWrapper<AdsKeywordModel> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(AdsKeywordModel::getAccoutnId, entity.getAccoutnId())
                    .eq(AdsKeywordModel::getProfileId, entity.getProfileId())
                    .eq(AdsKeywordModel::getCampaignId, entity.getCampaignId())
                    .eq(AdsKeywordModel::getAdGroupId, entity.getAdGroupId())
                    .eq(AdsKeywordModel::getKeywordId, entity.getKeywordId());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });
    }

    @Override
    public Map<String, Object> searchRecKeywordList(String accountId, AdsKeywordSearchVO param) throws BusinessException {
        Map<String, Object> map = new HashMap<>();
        List<AdsRecKeywordVO> voLst = new ArrayList<>();
        if(null==accountId || null==param.getProfileId() || null == param.getCampaignId() || null == param.getAdGroupId()){
            map.put("data", voLst);
            map.put("status", HttpStatus.HTTP_BAD_REQUEST);
        }
        //根据accountId获取店铺的accessToken
        String accessToken = redisUtils.getAccountAccessToken(accountId);
        //为每个关键词添加三种状态属性(关键字为null时表示自动推荐关键词上限200个，否则为手动搜索词推荐)
        List<AdsSpKeywordsRecRequest.TargetsData> targetsDataLst = null == param.getKeyword() ? new ArrayList<>() : Arrays.stream(param.getKeyword())
                .flatMap(keyword -> {
                    return Arrays.stream(new String[]{"BROAD", "PHRASE", "EXACT"})
                            .map(matchType -> new AdsSpKeywordsRecRequest.TargetsData().setMatchType(matchType).setKeyword(keyword).setUserSelectedKeyword(true));
                }).collect(Collectors.toList());
        //组装请求参数
        AdsSpKeywordsRecRequest adsSpKeywordsRecRequest = new AdsSpKeywordsRecRequest()
                .setCampaignId(param.getCampaignId())
                .setRecommendationType("KEYWORDS_FOR_ADGROUP")
                .setAdGroupId(param.getAdGroupId())
                .setTargets(targetsDataLst)
                .setMaxRecommendations(null == param.getKeyword() ? "200" : "0")
                .setSortDimension("CLICKS")
                .setLocale("ar_EG");
        //调用api获取推荐词列表
        List<AdsSpKeywordsRecResponse.KeywordTargetData> keywordTargetList = adsSpKeywordsAPi.keywordsRec(accessToken, param.getProfileId(), adsSpKeywordsRecRequest).getKeywordTargetList();
        keywordTargetList.stream().forEach(model -> {
            AdsRecKeywordVO vo = new AdsRecKeywordVO();
            BeanUtil.copyProperties(model, vo);
            voLst.add(vo);
        });
        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> createKeywords(String accountId, AdsCreateKeywordsVO createKeywordParam) {
        Map<String, Object> map = new HashMap<>();
        //根据accountId获取店铺的accessToken
        String accessToken = redisUtils.getAccountAccessToken(accountId);
        //组装请求参数
        List<AdsSpKeywordsCreateRequest.KeywordData> keywords = createKeywordParam.getCreateKeywordParam().stream()
                .map(param -> new AdsSpKeywordsCreateRequest.KeywordData()
                        .setCampaignId(param.getCampaignId())
                        .setMatchType(param.getMatchType())
                        .setState(param.getState())
                        .setBid(param.getBid())
                        .setAdGroupId(param.getAdGroupId())
                        .setKeywordText(param.getKeywordText())).collect(Collectors.toList());
        AdsSpKeywordsCreateRequest adsSpKeywordsCreateRequest = new AdsSpKeywordsCreateRequest().setKeywords(keywords);
        //调用api投放关键词
        AdsSpKeywordsCreateResponse response = adsSpKeywordsAPi.createKeywords(accessToken, createKeywordParam.getProfileId(), adsSpKeywordsCreateRequest);
        map.put("data", null);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }
}
