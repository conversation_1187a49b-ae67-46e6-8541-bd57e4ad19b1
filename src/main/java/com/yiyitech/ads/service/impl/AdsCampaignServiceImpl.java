package com.yiyitech.ads.service.impl;

import cn.hutool.http.HttpStatus;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.yiyitech.ads.exception.CampaignExceptionCode;
import com.yiyitech.ads.mapper.AdsAdGroupMapper;
import com.yiyitech.ads.mapper.AdsCampaignMapper;
import com.yiyitech.ads.model.AdsAdGroupModel;
import com.yiyitech.ads.model.AdsCampaignModel;
import com.yiyitech.ads.model.request.AdsSpCampaignCreateRequest;
import com.yiyitech.ads.model.response.AdsSpCampaignCreateResponse;
import com.yiyitech.ads.service.AdsCampaignService;
import com.yiyitech.ads.service.api.AdsSpCampaignsAPi;
import com.yiyitech.ads.util.RedisUtils;
import com.yiyitech.ads.vo.AdsCampaignCreateVO;
import com.yiyitech.ads.vo.AdsCampaignVO;
import com.yiyitech.ads.vo.search.AdsCampaignSearchVO;
import com.yiyitech.support.exception.BusinessException;
import com.yiyitech.support.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCampaignServiceImpl.java
 * @Description
 * @createTime 2023年12月06日 17:26:00
 */
@Slf4j
@Service
public class AdsCampaignServiceImpl extends ServiceImpl<AdsCampaignMapper, AdsCampaignModel> implements AdsCampaignService {
    @Autowired
    private AdsCampaignMapper adsCampaignMapper;
    @Autowired
    private AdsAdGroupMapper adsAdGroupMapper;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsSpCampaignsAPi adsSpCampaignsAPi;


    @Override
    @DS("master")
    public AdsCampaignModel getCampaign(long campaignId) throws BusinessException {
        AdsCampaignModel adsCampaignModel = adsCampaignMapper.selectById(campaignId);
        if (null == adsCampaignModel) {
            throw new BusinessException(CampaignExceptionCode.CAMPAIGNS_NOT_EXIST_CODE, CampaignExceptionCode.CAMPAIGNS_NOT_EXIST_MSG);
        }
        return adsCampaignModel;
    }

    @Override
    @DS("wangmeng")
    public AdsCampaignModel saveCampaign() throws BusinessException {
        AdsCampaignModel adsCampaignModel = new AdsCampaignModel();
        adsCampaignModel.setCampaignId("4").setName("测试4");
        adsCampaignMapper.insert(adsCampaignModel);
        return adsCampaignModel;
    }

    @Override
    public AdsCampaignModel saveCampaignn() throws BusinessException {
        AdsCampaignModel adsCampaignModel = new AdsCampaignModel();
        adsCampaignModel.setCampaignId("5").setName("测试5");
        adsCampaignMapper.insert(adsCampaignModel);
        return adsCampaignModel;
    }

    @Override
    public AdsCampaignModel getCampaignn(long campaignId) throws BusinessException {
        AdsCampaignModel adsCampaignModel = adsCampaignMapper.selectById(campaignId);
        if (null == adsCampaignModel) {
            throw new BusinessException(CampaignExceptionCode.CAMPAIGNS_NOT_EXIST_CODE, CampaignExceptionCode.CAMPAIGNS_NOT_EXIST_MSG);
        }
        return adsCampaignModel;
    }

    @Override
    @DS("wangmeng")
    public AdsCampaignModel getCampaignnn(long campaignId) throws BusinessException {
        AdsCampaignModel adsCampaignModel = adsCampaignMapper.selectById(campaignId);
        if (null == adsCampaignModel) {
            throw new BusinessException(CampaignExceptionCode.CAMPAIGNS_NOT_EXIST_CODE, CampaignExceptionCode.CAMPAIGNS_NOT_EXIST_MSG);
        }
        return adsCampaignModel;
    }

    @Override
    public Map<String, Object> searchCampaignList(AdsCampaignSearchVO param) throws BusinessException {
        Map<String, Object> map = new HashMap<>();
        List<AdsCampaignVO> voLst = new ArrayList<>();
        if(null == param.getProfileId()){
            map.put("data", voLst);
            map.put("status", HttpStatus.HTTP_BAD_REQUEST);
        }
        List<AdsCampaignModel> campaignLst = adsCampaignMapper.searchByProfileId(param.getProfileId());
        if (null != campaignLst && campaignLst.size() > 0) {
            List<String> campaignIdLst = campaignLst.stream().map(AdsCampaignModel::getCampaignId).collect(Collectors.toList());
            List<AdsAdGroupModel> adGroupLst = adsAdGroupMapper.searchByCampaignIds(campaignIdLst);
            voLst = campaignLst.stream().map(campaign -> {
                List<AdsCampaignVO.AdGroupData> adGroups = adGroupLst.stream()
                        .filter(adGroup -> adGroup.getCampaignId().equals(campaign.getCampaignId()))
                        .map(adGroup -> new AdsCampaignVO.AdGroupData().setAdGroupId(adGroup.getAdGroupId()).setName(adGroup.getName()))
                        .collect(Collectors.toList());
                return new AdsCampaignVO()
                        .setCampaignId(campaign.getCampaignId())
                        .setName(campaign.getName())
                        .setAdGroup(adGroups);
            }).collect(Collectors.toList());
        }
        map.put("data", voLst);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public Map<String, Object> createCampaign(AdsCampaignCreateVO campaignCreateParam) throws BusinessException {
        Map<String, Object> map = new HashMap<>();
        //根据accountId获取店铺的accessToken
        String accessToken = redisUtils.getAccountAccessToken(campaignCreateParam.getAccountId());
        //组装请求参数
        List<AdsSpCampaignCreateRequest.CampaignData> campaigns = campaignCreateParam.getCampaigns().stream().map(campaignVO -> {
            AdsSpCampaignCreateRequest.CampaignData campaignData = new AdsSpCampaignCreateRequest.CampaignData();
            BeanCopyUtil.copyBean(campaignVO, campaignData);
            //赋值 BudgetData
            if (campaignVO.getBudget() != null) {
                AdsSpCampaignCreateRequest.BudgetData budgetData = new AdsSpCampaignCreateRequest.BudgetData();
                BeanCopyUtil.copyBean(campaignVO.getBudget(), budgetData);
                budgetData.setBudgetType("DAILY");
                campaignData.setBudget(budgetData);
            }
            //赋值 DynamicBiddingData
            if (campaignVO.getDynamicBidding() != null) {
                AdsSpCampaignCreateRequest.DynamicBiddingData dynamicBiddingData = new AdsSpCampaignCreateRequest.DynamicBiddingData();
                BeanCopyUtil.copyBean(campaignVO.getDynamicBidding(), dynamicBiddingData);
                //赋值 PlacementBiddingData
                if (campaignVO.getDynamicBidding().getPlacementBidding() != null) {
                    List<AdsSpCampaignCreateRequest.PlacementBiddingData> placementBiddingList =
                            campaignVO.getDynamicBidding().getPlacementBidding().stream().map(pb -> {
                                AdsSpCampaignCreateRequest.PlacementBiddingData placementBiddingData = new AdsSpCampaignCreateRequest.PlacementBiddingData();
                                BeanCopyUtil.copyBean(pb, placementBiddingData);
                                return placementBiddingData;
                            }).collect(Collectors.toList());
                    dynamicBiddingData.setPlacementBidding(placementBiddingList);
                }
                campaignData.setDynamicBidding(dynamicBiddingData);
            }
            return campaignData;
        }).collect(Collectors.toList());
        AdsSpCampaignCreateRequest request = new AdsSpCampaignCreateRequest().setCampaigns(campaigns);
        //调用api创建广告活动
        AdsSpCampaignCreateResponse response = adsSpCampaignsAPi.campaignCreate(campaignCreateParam.getAccountId(), campaignCreateParam.getProfileId(), request);
        map.put("data", null);
        map.put("status", HttpStatus.HTTP_OK);
        return map;
    }

    @Override
    public boolean batchSaveOrUpdateByUniqueKey(List<AdsCampaignModel> list) {
        return SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, list, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            LambdaQueryWrapper<AdsCampaignModel> queryWrapper = Wrappers.<AdsCampaignModel>lambdaQuery()
                    .eq(AdsCampaignModel::getAccountId, entity.getAccountId())
                    .eq(AdsCampaignModel::getProfileId, entity.getProfileId())
                    .eq(AdsCampaignModel::getCampaignId, entity.getCampaignId());
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            LambdaUpdateWrapper<AdsCampaignModel> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(AdsCampaignModel::getAccountId, entity.getAccountId())
                    .eq(AdsCampaignModel::getProfileId, entity.getProfileId())
                    .eq(AdsCampaignModel::getCampaignId, entity.getCampaignId());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });
    }
}
