package com.yiyitech.ads.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yiyitech.ads.model.AdsSvcConfigurationModel;
import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.model.dto.UpdateConfigRequest;
import com.yiyitech.ads.model.dto.UpdateSvcConfRulesRequest;
import com.yiyitech.ads.vo.AdsSvcConfigurationVO;
import com.yiyitech.ads.vo.search.AdsSvcConfigSearchVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSvcConfigurationService.java
 * @Description
 * @createTime 2025年06月27日 14:23:00
 */
public interface AdsSvcConfigurationService extends IService<AdsSvcConfigurationModel> {

    Map<String, Object> getCurrentSvcConfig(Long userId);

    void svcConfigSwitch(Long svcConfigId, Boolean status, String userId);

    Map<String, Object> searhSvcConfigRules(Long svcConfigId);

    void updateCondition(String userId, UpdateConfigRequest request);

}
