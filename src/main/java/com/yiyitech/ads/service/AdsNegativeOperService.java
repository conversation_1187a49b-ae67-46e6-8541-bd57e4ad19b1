package com.yiyitech.ads.service;

import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.vo.AdsNegProcessingVO;
import com.yiyitech.ads.vo.search.AdsNegOperSearchVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperService.java
 * @Description
 * @createTime 2025年07月10日 13:16:00
 */
public interface AdsNegativeOperService {

    Map<String, Object> searhControlChartData(LoginUser loginUser, AdsNegOperSearchVO param);

    Map<String, Object> keywordToSechTerm(LoginUser loginUser, AdsNegOperSearchVO param);

    Map<String, Object> sechTermToKeyword(LoginUser loginUser, AdsNegOperSearchVO param);

    void negativeProcessing(LoginUser loginUser, AdsNegProcessingVO param);
}
