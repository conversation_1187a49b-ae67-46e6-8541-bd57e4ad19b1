package com.yiyitech.ads.service.apisp;

import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.response.SpCatalogItemResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SpCatalogItemApi.java
 * @Description
 * @createTime 2025年07月06日 18:44:00
 */
@Slf4j
@Component
public class SpServicesApi {
    private final RestTemplate defaultRestTemplate;
    private final RestTemplate awsSignedRestTemplate;

    public SpServicesApi(
            @Qualifier("defaultRestTemplate") RestTemplate defaultRestTemplate,
            @Qualifier("awsSignedRestTemplate") RestTemplate awsSignedRestTemplate) {
        this.defaultRestTemplate = defaultRestTemplate;
        this.awsSignedRestTemplate = awsSignedRestTemplate;
    }

    @Value("${url.token_url}")
    private String tokenUrl;
    @Value("${amazon.spapi.clientId}")
    private String clientId;
    @Value("${amazon.spapi.clientSecret}")
    private String clientSecret;
    @Value("${amazon.spapi.redirectUri}")
    private String redirectUri;


    public SpCatalogItemResponse getSpCatalogItem(String asin, String marketplaceId, String accessToken) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl("https://sellingpartnerapi-na.amazon.com/catalog/2020-12-01/items/" + asin)
                .queryParam("marketplaceIds", marketplaceId)
                .queryParam("includedData", "images,variations,summaries");
        URI url = uriBuilder.build().encode().toUri();

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.set("x-amz-access-token", accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        try {
            ResponseEntity<SpCatalogItemResponse> response = awsSignedRestTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    SpCatalogItemResponse.class
            );
            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("sp 获取asin对应的商品信息异常：" + response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            return response.getBody();
        } catch (Exception e) {
            log.error("sp 获取asin对应的商品信息异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }


}
