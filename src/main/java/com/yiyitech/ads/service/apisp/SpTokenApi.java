package com.yiyitech.ads.service.apisp;

import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.response.SPTokenResponse;
import com.yiyitech.ads.model.response.SpMarketplacePartResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SPTokenApi.java
 * @Description
 * @createTime 2025年06月18日 15:41:00
 */
@Slf4j
@Component
public class SpTokenApi {
    private final RestTemplate defaultRestTemplate;
    private final RestTemplate awsSignedRestTemplate;

    public SpTokenApi(
            @Qualifier("defaultRestTemplate") RestTemplate defaultRestTemplate,
            @Qualifier("awsSignedRestTemplate") RestTemplate awsSignedRestTemplate) {
        this.defaultRestTemplate = defaultRestTemplate;
        this.awsSignedRestTemplate = awsSignedRestTemplate;
    }

    @Value("${url.token_url}")
    private String tokenUrl;
    @Value("${amazon.spapi.clientId}")
    private String clientId;
    @Value("${amazon.spapi.clientSecret}")
    private String clientSecret;
    @Value("${amazon.spapi.redirectUri}")
    private String redirectUri;


    /**
     * 获取令牌
     * @param code
     * @return
     */
    public SPTokenResponse accessToken(String code) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        Map<String, String> params = new LinkedHashMap<>();
        params.put("grant_type", "authorization_code");
        params.put("code", code);
        params.put("client_id", clientId);
        params.put("client_secret", clientSecret);
        params.put("redirect_uri", redirectUri);
        StringBuilder bodyBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (bodyBuilder.length() > 0) bodyBuilder.append("&");
            bodyBuilder.append(entry.getKey()).append("=").append(entry.getValue());
        }
        HttpEntity<String> entity = new HttpEntity<>(bodyBuilder.toString(), headers);
        try {
            ResponseEntity<SPTokenResponse> response = defaultRestTemplate.exchange(
                    tokenUrl,
                    HttpMethod.POST,
                    entity,
                    SPTokenResponse.class
            );
            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("sp令牌获取异常：" + response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            return response.getBody();
        } catch (Exception e) {
            log.error("sp令牌获取异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

    /**
     * 刷新令牌
     * @param code
     * @return
     */
    public SPTokenResponse refreshToken(String refreshToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        Map<String, String> params = new LinkedHashMap<>();
        params.put("grant_type", "refresh_token");
        params.put("refresh_token", refreshToken);
        params.put("client_id", clientId);
        params.put("client_secret", clientSecret);
        StringBuilder bodyBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (bodyBuilder.length() > 0) bodyBuilder.append("&");
            bodyBuilder.append(entry.getKey()).append("=").append(entry.getValue());
        }

        HttpEntity<String> entity = new HttpEntity<>(bodyBuilder.toString(), headers);

        try {
            ResponseEntity<SPTokenResponse> response = defaultRestTemplate.exchange(
                    tokenUrl,
                    HttpMethod.POST,
                    entity,
                    SPTokenResponse.class
            );
            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("sp令牌刷新异常：" + response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            return response.getBody();
        } catch (Exception e) {
            log.error("sp令牌刷新异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

    public SpMarketplacePartResponse getMarketplaceParticipations(String accessToken) {
        String url = "https://sellingpartnerapi-na.amazon.com/sellers/v1/marketplaceParticipations";
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.set("x-amz-access-token", accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        try {
            ResponseEntity<SpMarketplacePartResponse> response = awsSignedRestTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    SpMarketplacePartResponse.class
            );
            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("sp 获取店铺基本信息异常：" + response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            return response.getBody();
        } catch (Exception e) {
            log.error("sp 获取店铺基本信息异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }


}
