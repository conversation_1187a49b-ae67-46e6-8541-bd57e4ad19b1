package com.yiyitech.ads.service.api;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.request.AdsSpAdGroupCreateRequest;
import com.yiyitech.ads.model.response.AdsSpAdGroupCreateResponse;
import com.yiyitech.ads.model.response.AdsSpGroupsListResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpGroupsAPi.java
 * @Description
 * @createTime 2023年12月12日 13:25:00
 */
@Slf4j
@Component
public class AdsSpGroupsAPi {
    @Value("${client.id}")
    private String clientId;
    @Value("${url.api_url}")
    private String apiUrl;

    /**
     * 查询组列表
     * @param accessToken
     * @param profileId
     * @param campaignIds
     * @return
     */
    public AdsSpGroupsListResponse groupsList(String accessToken, String profileId, List<String> campaignIds) {
        HttpPost request = new HttpPost(apiUrl + "/sp/adGroups/list");
//        JSONObject reqObj = new JSONObject();
//        reqObj.put("campaignIdFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd(campaignId)));
//        reqObj.put("stateFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 10);
//        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        request.setHeader("Accept", "application/vnd.spAdGroup.v3+json");
        request.setHeader("Content-Type", "application/vnd.spAdGroup.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("组列表api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsSpGroupsListResponse groupsLstResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsSpGroupsListResponse.class);
            return groupsLstResponse;
        } catch (IOException e) {
            log.error("组列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("组列表查询异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

    /**
     * 创建活动组
     * @param accessToken
     * @param profileId
     * @param adsSpAdGroupCreateRequest
     * @return
     */
    public AdsSpAdGroupCreateResponse adGroupCreate(String accessToken, String profileId, AdsSpAdGroupCreateRequest adsSpAdGroupCreateRequest) {
        HttpPost request = new HttpPost(apiUrl + "/sp/campaigns");
//        JSONObject reqObj = new JSONObject();
//        reqObj.put("campaignIdFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd(campaignId)));
//        reqObj.put("stateFilter", new JSONObject().fluentPut("include",new JSONArray().fluentAdd("PAUSED")));
//        reqObj.put("maxResults", 10);
//        reqObj.put("includeExtendedDataFields", true);
//        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setEntity(new StringEntity(new Gson().toJson(adsSpAdGroupCreateRequest), StandardCharsets.UTF_8));
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Accept", "application/vnd.spAdGroup.v3+json");
        request.setHeader("Content-Type", "application/vnd.spAdGroup.v3+json");
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("广告组api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsSpAdGroupCreateResponse spAdGroupCreateResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsSpAdGroupCreateResponse.class);
            return spAdGroupCreateResponse;
        } catch (IOException e) {
            log.error("创建广告组异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("创建广告组异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }
}
