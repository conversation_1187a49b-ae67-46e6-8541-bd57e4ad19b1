package com.yiyitech.ads.service.api;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONArray;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.response.AdsProfileResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsProfilesApi.java
 * @Description
 * @createTime 2023年12月11日 16:34:00
 */
@Slf4j
@Component
public class AdsProfilesApi {
    @Value("${client.id}")
    private String clientId;
    @Value("${client.secret}")
    private String clientSecret;
    @Value("${url.api_url}")
    private String apiUrl;

    /**
     * 获取账号站点文件
     * @param accessToken
     * @return
     */
    public List<AdsProfileResponse> AdsProfilesApi(String accessToken) {
        List<AdsProfileResponse> apiResponse = new ArrayList<>();
        HttpGet request = new HttpGet(apiUrl + "/v2/profiles");
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = null;
            response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("站点文件获取异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            apiResponse = JSONArray.parseArray(EntityUtils.toString(response.getEntity(), "UTF-8"), AdsProfileResponse.class);
            return apiResponse;
        } catch (IOException e) {
            log.error("站点文件获取异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }
}
