package com.yiyitech.ads.service.api;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yiyitech.ads.enums.AdsTargetingColumnsEnum;
import com.yiyitech.ads.exception.BasicExceptionCode;
import com.yiyitech.ads.model.response.AdsReportsResponse;
import com.yiyitech.support.exception.BusinessException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsReportsAPi.java
 * @Description
 * @createTime 2023年12月12日 15:10:00
 */
@Slf4j
@Component
public class AdsReportsAPi {
    @Value("${client.id}")
    private String clientId;
    @Value("${url.api_url}")
    private String apiUrl;

    /**
     * 生成报告
     * @param accessToken
     * @param profileId
     * @param reportName
     * @param reporeDate
     * @param groupBy
     * @param reportTypeId
     * @param columns
     * @return
     */
    public AdsReportsResponse createReport(String accessToken, String profileId, String reportName, String reporeDate, String groupBy, String reportTypeId, List<String> columns) {
        HttpPost request = new HttpPost(apiUrl + "/reporting/reports");
        JSONObject reqObj = new JSONObject();
        reqObj.put("name", reportName);
        reqObj.put("startDate", reporeDate);
        reqObj.put("endDate", reporeDate);
        reqObj.put("configuration", new JSONObject()
                .fluentPut("adProduct", "SPONSORED_PRODUCTS")
                .fluentPut("groupBy", new JSONArray().fluentAdd(groupBy))
                .fluentPut("columns", JSONArray.parseArray(JSONArray.toJSONString(columns)))
                .fluentPut("reportTypeId", reportTypeId)
                .fluentPut("timeUnit", "DAILY")
                .fluentPut("format", "GZIP_JSON"));
        request.setEntity(new StringEntity(reqObj.toJSONString(), StandardCharsets.UTF_8));
        request.setHeader("Content-Type", "application/vnd.createasyncreportrequest.v3+json");
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("生成报告api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsReportsResponse reportsResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsReportsResponse.class);
            return reportsResponse;
        } catch (IOException e) {
            log.error("生成报告异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("生成报告异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

    /**
     * 获取报告
     * @param accessToken
     * @param profileId
     * @param reportId
     * @return
     */
    public AdsReportsResponse getReport(String accessToken, String profileId, String reportId) {
//        HttpGet request = new HttpGet(new URIBuilder(apiUrl + "/reporting/reports/" + reportId + "").build());
        HttpGet request = new HttpGet(apiUrl + "/reporting/reports/" + reportId + "");
        request.setHeader("Content-Type", "application/vnd.createasyncreportrequest.v3+json");
        request.setHeader("Amazon-Advertising-API-ClientId", clientId);
        request.setHeader("Amazon-Advertising-API-Scope", profileId);
        request.setHeader("Authorization", "Bearer " + accessToken);
        try {
            CloseableHttpClient httpClient = HttpClients.createMinimal();
            CloseableHttpResponse response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
                log.error("获取报告api请求异常：" + response.getStatusLine().getReasonPhrase());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
            }
            AdsReportsResponse reportsResponse = JSONObject.parseObject((EntityUtils.toString(response.getEntity(), "UTF-8")), AdsReportsResponse.class);
            return reportsResponse;
        } catch (IOException e) {
            log.error("获取报告异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        } catch (ParseException e) {
            log.error("获取报告异常：" + e.getMessage());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }
    }

//    public static void main(String[] args) {
//        AdsReportsAPi aa = new AdsReportsAPi();
//        String b = "Atza|IwEBIMDS8rwXOtXRmZnDXrmkVvXjDE3zE7LXZf29-t_xBJXlHoeUOdeVV6Nn1NTHIlrb-F9V1cf138Z9eV_YRR5HQU3ml7LJxJJcRP_kf73SbO-KvuxOlW-47lJUzVFgfb_9xl1H0sNSj-gLxaen7pfY8EuRpD2KOSUR5UyqotesUlyvRoI1ZZ8YumcTb3Zs_qNFuZnJoeiXoeu29y9Eoo85-TYTT39bkN38SE3XEGLyMa1L0M1kT53gBHrh6JajqyjbJNEDL94kJgKi50ShCJ_rY86jN4kECWMtX1gThP0bziMojlvbIYcSrEaCUOD9BxyeFWiRavWN5mpLrIClw_jBOtDvfsBl67yuyy9QKkCCBNejdnemCg_rxYlTt8UhjjBKRZV8cVkEgu7xScB4AOXYjRlEZQKFIWJ1Lhu0G1tH6DPsgUe_oPcnp_sOupFFmpxFE-WwUMvKLiBBDHbnc9op78XUVzCtXsrVM7FsWh_gVYy1Aw";
////        aa.createReport(b, "1762485261961949", "SP spTargeting report test14", "2023-12-12","targeting","spTargeting", AdsTargetingColumnsEnum.getColumnsLst());
//        aa.getReport(b,"1762485261961949","b7c1ee36-fbbe-4581-b11b-dad30f6f1a82");
//    }
}
