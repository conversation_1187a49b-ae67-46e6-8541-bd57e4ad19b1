package com.yiyitech.ads.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yiyitech.ads.model.AdsCampaignModel;
import com.yiyitech.ads.vo.AdsCampaignCreateVO;
import com.yiyitech.ads.vo.search.AdsCampaignSearchVO;
import com.yiyitech.support.exception.BusinessException;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCampaignService.java
 * @Description
 * @createTime 2023年12月06日 17:26:00
 */
public interface AdsCampaignService extends IService<AdsCampaignModel> {

    AdsCampaignModel getCampaign(long campaignId) throws BusinessException;

    AdsCampaignModel saveCampaign() throws BusinessException;

    AdsCampaignModel saveCampaignn() throws BusinessException;

    AdsCampaignModel getCampaignn(long campaignId) throws BusinessException;

    AdsCampaignModel getCampaignnn(long campaignId) throws BusinessException;

    Map<String, Object> searchCampaignList(AdsCampaignSearchVO param) throws BusinessException;

    Map<String, Object> createCampaign(AdsCampaignCreateVO param) throws BusinessException;

    boolean batchSaveOrUpdateByUniqueKey(List<AdsCampaignModel> list);
}
