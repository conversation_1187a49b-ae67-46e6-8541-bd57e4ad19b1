package com.yiyitech.ads.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yiyitech.ads.model.AdsKeywordModel;
import com.yiyitech.ads.model.response.AdsSpKeywordsCreateResponse;
import com.yiyitech.ads.vo.search.AdsCreateKeywordsVO;
import com.yiyitech.ads.vo.search.AdsKeywordSearchVO;
import com.yiyitech.support.exception.BusinessException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsKeywordService.java
 * @Description
 * @createTime 2023年12月22日 13:26:00
 */
public interface AdsKeywordService extends IService<AdsKeywordModel> {

    boolean batchSaveOrUpdateByUniqueKey(List<AdsKeywordModel> list);

    Map<String, Object> searchRecKeywordList(String accountId, AdsKeywordSearchVO param) throws BusinessException;

    Map<String, Object> createKeywords(String accountId, AdsCreateKeywordsVO createKeywordParam);
}
