package com.yiyitech.ads.service;

import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.vo.AdsNegativeOperateVO;
import com.yiyitech.ads.vo.search.AdsNegOperLogSearchVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperLogService.java
 * @Description
 * @createTime 2025年07月03日 22:29:00
 */
public interface AdsNegativeOperLogService {

    Map<String, Object> searhPushNotification(Long userId);

    Map<String, Object> searhPush(Long userId);

    Map<String, Object> checkAsinLog(Long userId, boolean isManager, String asin, Integer flag, String loginTime) throws Exception;

    Map<String, Object> dataComparison(String uniqueSign, Integer comparisonType);

    Map<String, Object> searhNegOperLogLst(LoginUser loginUser, AdsNegOperLogSearchVO param);

    void logProcessing(LoginUser loginUser, AdsNegativeOperateVO param);
}
