package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegLogStatusEnum.java
 * @Description
 * @createTime 2025年07月10日 11:59:00
 */
@Getter
public enum AdsNegLogStatusEnum {
    COMPLETE(1, "已完成"),
    NOTVIEWED(2, "待查看"),
    UNTREATED(3, "待处理"),
    AUTO_FAILED(4, "系统自动否词失败");

    private final int code;
    private final String desc;

    AdsNegLogStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdsNegLogStatusEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的NegLogStatus"));
    }

    //除去AUTO_FAILED的code集合
    public static List<Integer> getNonAutoFailedCodes() {
        return Arrays.stream(values())
                .filter(e -> e != AUTO_FAILED)
                .map(AdsNegLogStatusEnum::getCode)
                .collect(Collectors.toList());
    }

}
