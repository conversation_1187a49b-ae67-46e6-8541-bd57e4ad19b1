package com.yiyitech.ads.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAccountNameEnum.java
 * @Description
 * @createTime 2024年01月23日 14:51:00
 */
@Getter
public enum AdsAccountNameEnum {
    WMDD("萌瓷"),
    WMXJ("大麦"),
    WMSR("上饶"),
    WMLL("易蚁"),
    WMOK("易挑");
    private final String columns;

    AdsAccountNameEnum(String columns) {
        this.columns = columns;
    }

    public static String getColumn(String name) {
        for (AdsAccountNameEnum e : AdsAccountNameEnum.values()) {
            if (e.toString().equals(name)) {
                return e.getColumns();
            }
        }
        return null;
    }
}
