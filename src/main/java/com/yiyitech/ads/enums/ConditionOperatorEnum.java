package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ConditionOperatorEnum.java
 * @Description
 * @createTime 2025年07月03日 10:12:00
 */
@Getter
public enum ConditionOperatorEnum {
    GREATER_THAN(1, "大于"),
    GREATER_THAN_OR_EQUAL(2, "大于等于"),
    LESS_THAN(3, "小于"),
    LESS_THAN_OR_EQUAL(4, "小于等于"),
    EQUAL_TO(5, "等于");

    private final int code;
    private final String desc;

    ConditionOperatorEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ConditionOperatorEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的operator"));
    }
}
