package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegSourceEnum.java
 * @Description
 * @createTime 2025年07月10日 11:58:00
 */
@Getter
public enum AdsNegSourceEnum {
    SEARCHTERM(1, "搜索词报告"),
    KEYWORD(2, "关键词报告");

    private final int code;
    private final String desc;

    AdsNegSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdsNegSourceEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的NegSource"));
    }
}
