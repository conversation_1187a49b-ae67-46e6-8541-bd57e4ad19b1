package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCommonStatusEnum.java
 * @Description
 * @createTime 2025年07月10日 19:15:00
 */
@Getter
public enum AdsCommonStatusEnum {
    EXCEPT_ARCHIVED(1, "归档除外"),
    ENABLED(2, "ENABLED"),
    PAUSED(3, "PAUSED"),
    ARCHIVED(4, "ARCHIVED");

    private final int code;
    private final String desc;

    AdsCommonStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdsCommonStatusEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的CommonStatus"));
    }

    /**
     * 返回 ENABLED、PAUSED、ARCHIVED 的 desc 列表
     */
    public static List<String> getExceptArchivedDescList() {
        return Arrays.stream(values())
                .filter(e -> e != EXCEPT_ARCHIVED)
                .map(AdsCommonStatusEnum::getDesc)
                .collect(Collectors.toList());
    }
}
