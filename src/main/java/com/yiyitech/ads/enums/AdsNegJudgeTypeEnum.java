package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegJudgeTypeEnum.java
 * @Description
 * @createTime 2025年07月10日 11:59:00
 */
@Getter
public enum AdsNegJudgeTypeEnum {
    ARTIFICIAL(1, "人工主动"),
    SYSTEM(2, "系统主动"),
    SYSTEM_AND_ARTIFICIAL(3, "系统判定+人工操作");

    private final int code;
    private final String desc;

    AdsNegJudgeTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdsNegJudgeTypeEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的NegJudgeType"));
    }
}
