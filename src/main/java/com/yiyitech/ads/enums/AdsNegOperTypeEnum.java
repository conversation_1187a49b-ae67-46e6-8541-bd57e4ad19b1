package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegOperTypeEnum.java
 * @Description
 * @createTime 2025年07月10日 11:58:00
 */
@Getter
public enum AdsNegOperTypeEnum {
    KEYWORD_NEG(1, "关键词否定"),
    ASIN_NEG(2, "商品ASIN否定"),
    AUTO_NEG(3, "自动匹配否定"),
    NEGATIVE_OPERATION(4, "否词运营监控");

    private final int code;
    private final String desc;

    AdsNegOperTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdsNegOperTypeEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的NegOperType"));
    }
}
