package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SvcConfgNotificationEnum.java
 * @Description
 * @createTime 2025年07月03日 11:40:00
 */
@Getter
public enum SvcConfgNotificationEnum {
    GREATER_THAN(1, "无通知"),
    GREATER_THAN_OR_EQUAL(2, "系统消息");

    private final int code;
    private final String desc;

    SvcConfgNotificationEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SvcConfgNotificationEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的NotificationMethod"));
    }
}
