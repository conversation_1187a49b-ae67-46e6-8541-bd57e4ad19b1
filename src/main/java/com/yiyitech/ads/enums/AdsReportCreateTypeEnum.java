package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsReportCreateTypeEnum.java
 * @Description
 * @createTime 2025年07月10日 14:39:00
 */
@Getter
public enum AdsReportCreateTypeEnum {
    SEARCHTERM(2, "spSearchTerm"),
    TARGETING(1, "spTargeting"),
    PRODUCT(3, "spAdvertisedProduct");

    private final int code;
    private final String desc;

    AdsReportCreateTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdsReportCreateTypeEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的ReportCreateType"));
    }
}
