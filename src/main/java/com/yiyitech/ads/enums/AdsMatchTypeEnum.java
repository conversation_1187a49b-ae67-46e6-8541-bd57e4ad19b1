package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsMatchTypeEnum.java
 * @Description
 * @createTime 2025年07月09日 20:07:00
 */
@Getter
public enum AdsMatchTypeEnum {
    EXACT(1, "精准否定"),
    PHRASE(2, "词组否定"),
    ADV_ASIN(3, "商品asin否定"),
    EXACT_AND_PHRASE(4, "精准否定+商品asin否定");

    private final int code;
    private final String desc;

    AdsMatchTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdsMatchTypeEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的MatchType"));
    }
}
