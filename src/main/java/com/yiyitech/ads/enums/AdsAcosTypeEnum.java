package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAcosTypeEnum.java
 * @Description
 * @createTime 2025年07月10日 19:17:00
 */
@Getter
public enum AdsAcosTypeEnum {
    ACOS0(1, "0-10"),
    <PERSON><PERSON><PERSON>(2, "11-20"),
    <PERSON><PERSON><PERSON>(3, "21-50"),
    <PERSON><PERSON><PERSON>(4, "51-70"),
    <PERSON><PERSON>71(5, "71-100"),
    <PERSON><PERSON>101(6, "101以上"),
    NO_SALES(7, "有花费无销售额"),
    NO_CLICKS(8, "无点击"),
    ;

    private final int code;
    private final String desc;

    AdsAcosTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdsAcosTypeEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的AcosType"));
    }
}
