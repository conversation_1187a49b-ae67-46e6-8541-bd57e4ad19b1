package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RulesActionTypeEnum.java
 * @Description
 * @createTime 2025年07月03日 09:55:00
 */
@Getter
public enum RulesActionTypeEnum {
    NO_ACTION(1, "无行为"),
    AUTO_EXECUTE(2, "系统自动否定关键词，发送日志"),
    MANUAL_OPERATION(3, "仅预警，人工审核确认");

    private final int code;
    private final String desc;

    RulesActionTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RulesActionTypeEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的actionType"));
    }
}