package com.yiyitech.ads.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegOperLevelEnum.java
 * @Description
 * @createTime 2025年07月09日 19:13:00
 */
@Getter
public enum AdsNegOperLevelEnum {
    PORTFOLIO(1, "广告组合"),
    CAMPAIGN(2, "广告活动"),
    NEGATIVE_OPERATION(3, "否词运营");

    private final int code;
    private final String desc;

    AdsNegOperLevelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdsNegOperLevelEnum of(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的NegOperLevel"));
    }
}
