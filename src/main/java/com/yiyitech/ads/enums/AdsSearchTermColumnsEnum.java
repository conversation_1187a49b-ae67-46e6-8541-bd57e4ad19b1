package com.yiyitech.ads.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSearchTermColumnsEnum.java
 * @Description
 * @createTime 2023年12月12日 16:50:00
 */
public enum AdsSearchTermColumnsEnum {
    CAMPAIGN_ID("campaignId"),
    ADGROUP_ID("adGroupId"),
    KEYWORD_ID("keywordId"),
    PORTFOLIO_ID("portfolioId"),
    DATE("date"),
    CAMPAIGN_NAME("campaignName"),
    ADGROUP_NAME("adGroupName"),
    KEY<PERSON>ORD_TYPE("keywordType"),
    MATCH_TYPE("matchType"),
    KEYWORD("keyword"),
    TARGETING("targeting"),
    SEARCH_TERM("searchTerm"),
    CAMPAIGN_STATUS("campaignStatus"),
    AD_KEYWORD_STATUS("adKeywordStatus"),
    CAMPAIGN_BUDGET_TYPE("campaignBudgetType"),
    CAMPAIGN_BUDGET_AMOUNT("campaignBudgetAmount"),
    CAMPAIGN_BUDGET_CURRENCY_CODE("campaignBudgetCurrencyCode"),
    IMPRESSIONS("impressions"),
    CLICKS("clicks"),
    CLICK_THROUGH_RATE("clickThroughRate"),
    COST("cost"),
    COST_PER_CLICK("costPerClick"),
    KEYWORD_BID("keywordBid"),
    PURCHASES_1D("purchases1d"),
    UNITS_SOLD_CLICKS_1D("unitsSoldClicks1d"),
    PURCHASES_SAME_SKU_1D("purchasesSameSku1d"),
    UNITS_SOLD_SAME_SKU_1D("unitsSoldSameSku1d"),
    SALES_1D("sales1d"),
    ATTRIBUTED_SALES_SAME_SKU_1D("attributedSalesSameSku1d")
    ;
    private final String columns;

    AdsSearchTermColumnsEnum(String columns) {
        this.columns = columns;
    }

    public static List<String> getColumnsLst() {
        List<String> columnsLst = new ArrayList<>();
        Arrays.stream(AdsSearchTermColumnsEnum.values()).forEach(value -> {
            columnsLst.add(value.columns);
        });
        return columnsLst;
    }

}
