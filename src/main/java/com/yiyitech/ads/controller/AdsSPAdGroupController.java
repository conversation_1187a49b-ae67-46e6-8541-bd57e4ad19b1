package com.yiyitech.ads.controller;

import com.yiyitech.ads.vo.AdsSpAdGroupCreateVO;
import com.yiyitech.ads.service.AdsAdGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSPAdGroupController.java
 * @Description
 * @createTime 2025年02月10日 21:31:00
 */
@RestController
@RequestMapping("/v1/adGroup")
public class AdsSPAdGroupController {
    @Autowired
    private AdsAdGroupService adsAdGroupService;


    @RequestMapping(path = {"/create"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*",allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> createAdGroup(@RequestBody AdsSpAdGroupCreateVO param) {
        return new ResponseEntity<>(adsAdGroupService.createAdGroup(param), HttpStatus.OK);
    }

}
