package com.yiyitech.ads.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyitech.ads.vo.AdsSpCategoryVO;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName Testc.java
 * @Description
 * @createTime 2025年02月17日 16:45:00
 */
public class Testc {
    public static void main(String[] args) throws Exception {
        String json = "{ \"CategoryTree\": \"[{\\\"id\\\":9230166011,\\\"na\\\":\\\"Gift Cards\\\",\\\"ch\\\":[{\\\"id\\\":9843102011,\\\"na\\\":\\\"Gift Cards\\\",\\\"ch\\\":[],\\\"ta\\\":true}],\\\"ta\\\":true},{\\\"id\\\":917972,\\\"na\\\":\\\"Movies & TV Shows\\\",\\\"ch\\\":[{\\\"id\\\":17202639011,\\\"na\\\":\\\"Movies\\\",\\\"ch\\\":[],\\\"ta\\\":true}]}]\" }";

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(json);

        // 获取 "CategoryTree" 字符串并解析为 List<CategoryModel>
        String categoryTreeJson = rootNode.get("CategoryTree").asText();
        List<AdsSpCategoryVO> categories = objectMapper.readValue(categoryTreeJson, new TypeReference<List<AdsSpCategoryVO>>() {});

        // AdsSpCategoryVO
        List<AdsSpCategoryVO> flatList = new ArrayList<>();
        flattenCategoryList(categories, null, flatList);

        // 输出解析结果
        flatList.forEach(category ->
                System.out.println("ID: " + category.getCategoryId() + ", Name: " + category.getCategoryName() + ", ParentID: " + category.getParentId()));
    }

    // 递归展开分类列表
    private static void flattenCategoryList(List<AdsSpCategoryVO> categories, Long parentId, List<AdsSpCategoryVO> flatList) {
        for (AdsSpCategoryVO category : categories) {
            category.setParentId(parentId); // 设置父 ID
            flatList.add(category);
            if (category.getCh() != null && !category.getCh().isEmpty()) {
                flattenCategoryList(category.getCh(), category.getId(), flatList);
            }
            category.setCh(null); // 清空子分类，防止冗余
        }
    }
}

