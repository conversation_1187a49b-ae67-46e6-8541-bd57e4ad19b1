package com.yiyitech.ads.controller;

import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.service.AdsCreateReportService;
import com.yiyitech.ads.service.AdsNegativeOperService;
import com.yiyitech.ads.vo.AdsDataRefreshCreateVO;
import com.yiyitech.ads.vo.AdsNegProcessingVO;
import com.yiyitech.ads.vo.search.AdsNegOperSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperController.java
 * @Description
 * @createTime 2025年07月10日 13:13:00
 */
@RestController
@RequestMapping("/v1/negOper")
public class AdsNegativeOperController {
    @Autowired
    private AdsCreateReportService createReportService;
    @Autowired
    private AdsNegativeOperService negativeOperService;

    @RequestMapping(path = {"/data_refresh"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> dataRefresh(@AuthenticationPrincipal LoginUser loginUser, @RequestBody AdsDataRefreshCreateVO param) {
        return new ResponseEntity<>(createReportService.dataRefresh(loginUser, param), HttpStatus.OK);
    }

    @RequestMapping(path = {"/control_chart"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searhControlChartData(@AuthenticationPrincipal LoginUser loginUser, @RequestBody AdsNegOperSearchVO param) {
        return new ResponseEntity<>(negativeOperService.searhControlChartData(loginUser, param), HttpStatus.OK);
    }

    @RequestMapping(path = {"/keyword_to_sechTerm"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> keywordToSechTerm(@AuthenticationPrincipal LoginUser loginUser, @RequestBody AdsNegOperSearchVO param) {
        return new ResponseEntity<>(negativeOperService.keywordToSechTerm(loginUser, param), HttpStatus.OK);
    }

    @RequestMapping(path = {"/sechTerm_to_keyword"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> sechTermToKeyword(@AuthenticationPrincipal LoginUser loginUser, @RequestBody AdsNegOperSearchVO param) {
        return new ResponseEntity<>(negativeOperService.sechTermToKeyword(loginUser, param), HttpStatus.OK);
    }

    @RequestMapping(path = {"/neg_processing"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> negativeProcessing(@AuthenticationPrincipal LoginUser loginUser, @RequestBody AdsNegProcessingVO param) {
        negativeOperService.negativeProcessing(loginUser, param);
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }
}
