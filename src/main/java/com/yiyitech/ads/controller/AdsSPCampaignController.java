package com.yiyitech.ads.controller;

import com.yiyitech.ads.service.AdsCampaignService;
import com.yiyitech.ads.vo.AdsCampaignCreateVO;
import com.yiyitech.ads.vo.search.AdsCampaignSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSPCampaignController.java
 * @Description
 * @createTime 2025年02月10日 21:31:00
 */
@RestController
@RequestMapping("/v1/campaign")
public class AdsSPCampaignController {
    @Autowired
    private AdsCampaignService adsCampaignService;

    @RequestMapping(path = {"/search_list"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*",allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searchCampaignList(@RequestBody AdsCampaignSearchVO param) {
        return new ResponseEntity<>(adsCampaignService.searchCampaignList(param), HttpStatus.OK);
    }

    @RequestMapping(path = {"/create"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*",allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> createCampaign(@RequestBody AdsCampaignCreateVO param) {
        return new ResponseEntity<>(adsCampaignService.createCampaign(param), HttpStatus.OK);
    }

}
