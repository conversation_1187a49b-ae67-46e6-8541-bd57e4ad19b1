package com.yiyitech.ads.controller;

import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CommonController.java
 * @Description
 * @createTime 2025年06月23日 13:33:00
 */
@RestController
@RequestMapping("/v1/common")
public class CommonController {
    @Autowired
    private CommonService commonService;

    @RequestMapping(path = {"/def_filter"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searchDefaultFilterList() {
        return new ResponseEntity<>(commonService.searchDefaultFilterList(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/cur_filter"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searchCurrentFilterList(@AuthenticationPrincipal LoginUser loginUser,
                                                                       @RequestHeader(value = "Accept-Language", defaultValue = "zh-CN") String acceptLanguage) {
        return new ResponseEntity<>(commonService.searchCurrentFilterList(Long.valueOf(loginUser.getId()), acceptLanguage), HttpStatus.OK);
    }

}
