package com.yiyitech.ads.controller;

import com.yiyitech.ads.job.*;
import com.yiyitech.ads.vo.AdsHandleOperVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsHandleController.java
 * @Description
 * @createTime 2025年07月12日 19:52:00
 */
@RestController
@RequestMapping("/v1/handle/oper")
public class AdsHandleController {
    @Autowired
    private AdsProfileTask profileTask;
    @Autowired
    private AdsCreateReportTask createReportTask;
    @Autowired
    private AdsGetReportTask getReportTask;
    @Autowired
    private AdsCatalogUpdateTask catalogUpdateTask;
    @Autowired
    private AdsAutoNegativeTask autoNegativeTask;

    @RequestMapping(path = {"/profile"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> refreshAdsAccountProfile() {
        profileTask.refreshAdsAccountProfile();
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/create"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> createReport(@RequestBody AdsHandleOperVo param) {
        createReportTask.createReport(param.getDate(), param.getCountingBack());
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/get"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> getReport(@RequestBody AdsHandleOperVo param) {
        getReportTask.getReport(param.getDate(), param.getCountingBack());
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/catalog_update"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> updateAsinCatalogInfo(@RequestBody AdsHandleOperVo param) {
        catalogUpdateTask.updateAsinCatalogInfo(null == param.getMarketplaceId() ? "ATVPDKIKX0DER" : param.getMarketplaceId(), param.getDate(), param.getCountingBack());
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/auto_neg"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> autoNegativeSearchTerm(@RequestBody AdsHandleOperVo param) {
        autoNegativeTask.autoNegative(param.getDate());
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

}
