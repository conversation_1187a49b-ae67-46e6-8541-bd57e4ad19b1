package com.yiyitech.ads.controller;

import com.yiyitech.ads.service.AdsPortfolioService;
import com.yiyitech.ads.vo.search.AdsPortfolioSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsPortfolioController.java
 * @Description
 * @createTime 2025年02月18日 20:33:00
 */
@RestController
@RequestMapping("/v1/portfolio")
public class AdsPortfolioController {
    @Autowired
    private AdsPortfolioService adsPortfolioService;

    @RequestMapping(path = {"/list"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searchPortfolioList(@RequestHeader("Authorize") String accountId, @RequestBody AdsPortfolioSearchVO param) {
        return new ResponseEntity<>(adsPortfolioService.searchPortfolioList(accountId, param), HttpStatus.OK);
    }
}
