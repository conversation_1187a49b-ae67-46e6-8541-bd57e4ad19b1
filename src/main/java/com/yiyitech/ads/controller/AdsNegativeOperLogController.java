package com.yiyitech.ads.controller;

import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.service.AdsNegativeOperLogService;
import com.yiyitech.ads.vo.AdsNegativeOperateVO;
import com.yiyitech.ads.vo.search.AdsNegOperLogSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperLogController.java
 * @Description
 * @createTime 2025年07月03日 20:13:00
 */
@RestController
@RequestMapping("/v1/negLog")
public class AdsNegativeOperLogController {
    @Autowired
    private AdsNegativeOperLogService negOperLogService;


    @RequestMapping(path = {"/notification"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searhNotification(@AuthenticationPrincipal LoginUser loginUser) {
        return new ResponseEntity<>(negOperLogService.searhPushNotification(Long.valueOf(loginUser.getId())), HttpStatus.OK);
    }

    @RequestMapping(path = {"/push"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searhPush(@AuthenticationPrincipal LoginUser loginUser) {
        return new ResponseEntity<>(negOperLogService.searhPush(Long.valueOf(loginUser.getId())), HttpStatus.OK);
    }

    @RequestMapping(path = {"/check"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> checkAsinLog(@AuthenticationPrincipal LoginUser loginUser, @RequestBody AdsNegOperLogSearchVO param) {
        Map<String, Object> result = null;
        try {
            result = negOperLogService.checkAsinLog(Long.valueOf(loginUser.getId()), loginUser.isAdmin(), param.getAsin(), param.getFlag(), param.getEndDate());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @RequestMapping(path = {"/data_comparison"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> dataComparison( @RequestBody AdsNegOperLogSearchVO param) {
        return new ResponseEntity<>(negOperLogService.dataComparison(param.getUniqueSign(), param.getComparisonType()), HttpStatus.OK);
    }

    @RequestMapping(path = {"/list"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searhNegOperLogLst(@AuthenticationPrincipal LoginUser loginUser, @RequestBody AdsNegOperLogSearchVO param) {
        return new ResponseEntity<>(negOperLogService.searhNegOperLogLst(loginUser, param), HttpStatus.OK);
    }

//    @RequestMapping(path = {"/processing"}, method = {RequestMethod.POST})
//    @CrossOrigin(origins = "*", allowCredentials = "true")
//    public ResponseEntity<Map<String, Object>> logProcessing(@AuthenticationPrincipal LoginUser loginUser, @RequestBody AdsNegativeOperateVO param) {
//        negOperLogService.logProcessing(loginUser, param);
//        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
//    }


}
