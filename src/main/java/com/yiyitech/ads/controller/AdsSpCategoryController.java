package com.yiyitech.ads.controller;

import com.yiyitech.ads.service.AdsSpCategoryService;
import com.yiyitech.ads.vo.search.AdsSpCategorySearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpCategoryController.java
 * @Description
 * @createTime 2025年02月18日 14:15:00
 */
@RestController
@RequestMapping("/v1/sp/category")
public class AdsSpCategoryController {
    @Autowired
    private AdsSpCategoryService adsSpCategoryService;

    @RequestMapping(path = {"/insert/handle"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> insertSpCategoryList(@RequestHeader("Authorize") String accountId, @RequestBody AdsSpCategorySearchVO param) {
        return new ResponseEntity<>(adsSpCategoryService.insertSpCategorys(accountId, param), HttpStatus.OK);
    }

    @RequestMapping(path = {"/search"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searchSpCategoryList(@RequestBody AdsSpCategorySearchVO param) {
        return new ResponseEntity<>(adsSpCategoryService.searchSpCategorys(param), HttpStatus.OK);
    }
}
