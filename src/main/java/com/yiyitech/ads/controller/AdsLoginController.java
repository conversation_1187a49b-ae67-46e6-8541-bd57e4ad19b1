package com.yiyitech.ads.controller;

import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.service.AdsLoginService;
import com.yiyitech.ads.vo.search.AdsCampaignSearchVO;
import com.yiyitech.ads.vo.search.AdsUserSearchVO;
import com.yiyitech.support.dy.LoginResponse;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.core.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsLoginController.java
 * @Description
 * @createTime 2025年06月11日 13:58:00
 */
@RestController
@RequestMapping("/v1/enter")
public class AdsLoginController {
    @Autowired
    private AdsLoginService adsLoginService;

    @RequestMapping(path = {"/register"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*",allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> register(@RequestBody AdsUserSearchVO param) {
        return new ResponseEntity<>(adsLoginService.register(param.getPhone(), param.getPassword(), param.getCode()), HttpStatus.OK);
    }


    @RequestMapping(path = {"/login"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*",allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> login(@RequestBody AdsUserSearchVO param) {
        return new ResponseEntity<>(adsLoginService.login(param.getPhone(), param.getPassword()), HttpStatus.OK);
    }

    @RequestMapping(path = {"/login_out"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> loginOut(@AuthenticationPrincipal LoginUser loginUser) {
        adsLoginService.loginOut(Long.valueOf(loginUser.getId()), loginUser.isAdmin());
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }
}

