package com.yiyitech.ads.controller;

import com.yiyitech.ads.service.AdsReportService;
import com.yiyitech.ads.vo.AdsReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsReportController.java
 * @Description
 * @createTime 2023年12月19日 11:04:00
 */
@RestController
@RequestMapping("/v1/report")
public class AdsReportController {
    @Autowired
    private AdsReportService AdsReportService;

    @RequestMapping(path = {"/searchTerm/_search"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searchDailySearchTermReportList(@RequestBody() AdsReportVO adsReportVO) {
        return new ResponseEntity<>(AdsReportService.searchDailySearchTermReportList(adsReportVO), HttpStatus.OK);
    }

    @RequestMapping(path = {"/targeting/_search"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searchDailyTargetingReportList(@RequestBody() AdsReportVO adsReportVO) {
        return new ResponseEntity<>(AdsReportService.searchDailyTargetingReportList(adsReportVO), HttpStatus.OK);
    }

}
