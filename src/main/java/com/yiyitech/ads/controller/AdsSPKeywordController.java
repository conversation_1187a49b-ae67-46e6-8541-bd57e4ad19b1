package com.yiyitech.ads.controller;

import com.yiyitech.ads.service.AdsKeywordService;
import com.yiyitech.ads.vo.search.AdsCreateKeywordsVO;
import com.yiyitech.ads.vo.search.AdsKeywordSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSPKeywordController.java
 * @Description
 * @createTime 2025年02月10日 21:19:00
 */
@RestController
@RequestMapping("/v1/keyword")
public class AdsSPKeywordController {
    @Autowired
    private AdsKeywordService adsKeywordService;

    @RequestMapping(path = {"/recommendations"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searchRecKeywordList(@RequestHeader("Authorize") String accountId, @RequestBody AdsKeywordSearchVO param) {
        return new ResponseEntity<>(adsKeywordService.searchRecKeywordList(accountId, param), HttpStatus.OK);
    }

    @RequestMapping(path = {"/create"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> createKeywords(@RequestHeader("Authorize") String accountId, @RequestBody AdsCreateKeywordsVO createKeywordParam) {
        return new ResponseEntity<>(adsKeywordService.createKeywords(accountId, createKeywordParam), HttpStatus.OK);
    }
}
