package com.yiyitech.ads.controller;

import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.model.dto.UpdateSvcConfRulesRequest;
import com.yiyitech.ads.service.AdsSvcConfigurationService;
import com.yiyitech.ads.vo.search.AdsSvcConfigSearchVO;
import com.yiyitech.ads.model.dto.UpdateConfigRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SvcConfigurationController.java
 * @Description
 * @createTime 2025年06月27日 14:21:00
 */
@RestController
@RequestMapping("/v1/svcConf")
public class AdsSvcConfigurationController {
    //TODO 整个业务配置的列表及修改逻辑后续改为：展示，添加，更新 当前用户下多店铺操作
    @Autowired
    private AdsSvcConfigurationService svcConfigService;

    @RequestMapping(path = {"/get_config"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> getCurrentSvcConfig(@AuthenticationPrincipal LoginUser loginUser) {
        return new ResponseEntity<>(svcConfigService.getCurrentSvcConfig(Long.valueOf(loginUser.getId())), HttpStatus.OK);
    }

    @RequestMapping(path = {"/config_switch"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> svcConfigSwitch(@AuthenticationPrincipal LoginUser loginUser, @RequestBody AdsSvcConfigSearchVO param) {
        svcConfigService.svcConfigSwitch(Long.valueOf(param.getSvcConfigId()), param.getStatus(), loginUser.getId());
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/rules_list"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searhSvcConfigRules(@RequestBody AdsSvcConfigSearchVO param) {
        return new ResponseEntity<>(svcConfigService.searhSvcConfigRules(Long.valueOf(param.getSvcConfigId())), HttpStatus.OK);
    }

    @RequestMapping(path = {"/condition_update"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> updateCondition(@AuthenticationPrincipal LoginUser loginUser, @RequestBody UpdateConfigRequest request) {
        svcConfigService.updateCondition(loginUser.getId(), request);
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }



}
