package com.yiyitech.ads.controller;

import com.yiyitech.ads.job.AdsCampaignGroupKeywordTask;
import com.yiyitech.ads.job.AdsCreateReportTask;
import com.yiyitech.ads.job.AdsGetReportTask;
import com.yiyitech.ads.job.AdsProfileTask;
import com.yiyitech.ads.model.AdsCampaignModel;
import com.yiyitech.ads.service.AdsCampaignService;
import com.yiyitech.ads.service.AmazoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AmazonController.java
 * @Description
 * @createTime 2023年11月15日 15:20:00
 */
@RestController
public class AmazonController {
    @Autowired
    private AmazoService amazoService;
    @Autowired
    private AdsCampaignService adsCampaignService;
    @Autowired
    private AdsProfileTask adsProfileTask;
    @Autowired
    private AdsCampaignGroupKeywordTask adsCampaignGroupKeywordTask;
    @Autowired
    private AdsCreateReportTask adsCreateReportTask;
    @Autowired
    private AdsGetReportTask adsGetReportTask;

    @RequestMapping(path = {"/v1/ads/auth"}, method = {RequestMethod.GET})
    public ResponseEntity<Map<String, Object>> accessToken(@RequestParam("code") String code, HttpServletResponse response) {
        amazoService.accessToken(code);
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/token/refresh"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        amazoService.refreshToken(refreshToken);
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/profiles"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> queryProfiles(@RequestParam("token") String token) {
        amazoService.profileList(token);
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/campaign/list"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> queryCampaignList() {
        return new ResponseEntity<>(amazoService.campaignList(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/group/list"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> queryAdGroupList() {
        return new ResponseEntity<>(amazoService.adGroupList(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/group/top_keywords"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "http://localhost:8091", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> queryTopKeywordsList(@RequestBody String param) {
        return new ResponseEntity<>(amazoService.queryTopKeywordsList(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/campaign/insert1"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> insertCampaign() {
        adsCampaignService.saveCampaign();
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/campaign/get1"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> getCampaign(@RequestParam("id") long id) {
        AdsCampaignModel aa = adsCampaignService.getCampaign(id);
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }
    @RequestMapping(path = {"/v1/campaign/insert2"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> insertCampaign2() {
        adsCampaignService.saveCampaignn();
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/campaign/get2"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> getCampaign2(@RequestParam("id") long id) {
        AdsCampaignModel aa = adsCampaignService.getCampaignn(id);
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/campaign/get3"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> getCampaign3(@RequestParam("id") long id) {
        AdsCampaignModel aa = adsCampaignService.getCampaignnn(id);
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }


    @RequestMapping(path = {"/v1/handle/accpro"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> get1() {
        adsProfileTask.refreshAdsAccountProfile();
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/handle/cagpkd"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> get2() {
        adsCampaignGroupKeywordTask.refreshAdsCampaignGroupKeyword();
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/handle/create"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> get3(@RequestParam("date") String date, @RequestParam("date") Integer countingBack) {
        adsCreateReportTask.createReport(date, countingBack);
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/handle/get"}, method = {RequestMethod.POST})
    public ResponseEntity<Map<String, Object>> get4(@RequestParam("date") String date, @RequestParam("date") Integer countingBack) {
        adsGetReportTask.getReport(date, countingBack);
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }

    @RequestMapping(path = {"/v1/handle/setRedis"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> setRedis() {
        amazoService.setRedis();
        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
    }
}
