package com.yiyitech.ads.controller;

import com.yiyitech.ads.service.AdsExchangeRateService;
import com.yiyitech.ads.vo.AdsExchangeRateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsExchangeRateController.java
 * @Description
 * @createTime 2024年01月29日 15:29:00
 */
@RestController
@RequestMapping("/v1/exrate")
public class AdsExchangeRateController {
    @Autowired
    private AdsExchangeRateService adsExchangeRateService;

    @RequestMapping(path = {"/_search"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searchExchangeRateLst(@RequestBody AdsExchangeRateVO adsExchangeRateVO) {
        return new ResponseEntity<>(adsExchangeRateService.searchExchangeRateLst(adsExchangeRateVO.getOriginalCurrencyCode(), adsExchangeRateVO.getTargetCurrencyCode(), adsExchangeRateVO.getStartDate(), adsExchangeRateVO.getEndDate()), HttpStatus.OK);
    }

    @RequestMapping(path = {"/_saveOrUpdate"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> saveOrUpdateExchangeRate(@RequestBody AdsExchangeRateVO adsExchangeRateVO) {
        return new ResponseEntity<>(adsExchangeRateService.saveOrUpdateExchangeRate(adsExchangeRateVO), HttpStatus.OK);
    }

}
