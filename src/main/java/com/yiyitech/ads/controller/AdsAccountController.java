package com.yiyitech.ads.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyitech.ads.model.AuthState;
import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.service.AdsAccountService;
import com.yiyitech.ads.service.apisp.SpTokenApi;
import com.yiyitech.ads.vo.search.AdsAccountSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAccountController.java
 * @Description
 * @createTime 2023年12月13日 16:32:00
 */
@RestController
@RequestMapping("/v1/account")
public class AdsAccountController {
    @Value("${url.location}")
    private String location;
    @Autowired
    private AdsAccountService adsAccountService;
    @Autowired
    private SpTokenApi spTokenApi;

//    @RequestMapping(path = {"/auth"}, method = {RequestMethod.GET})
//    public ResponseEntity<MultiValueMap<String, String>> accessToken(@RequestParam("code") String code, HttpServletResponse response) throws Exception{
////        AdsTokenResponse tokenResponse = adsTokenApi.accessToken(code);
//        System.out.println(code);
//        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
//        String locationUrl = location;
//        Map<String, String> map = new HashMap<>();
////        map.put("userid", "A3QXCPZWLQ2IWO");
//        map.put("random", AdsUtils.generateRandomToken(32));
//        String userTOKEN = URLEncoder.encode(TokenUtil.getToken(map, "A3QXCPZWLQ2IWO"), "UTF-8");
//        locationUrl = locationUrl.concat("/?");
//        headers.put("location", Lists.newArrayList(locationUrl + "token=" + userTOKEN));
////        adsAccountService.accessToken(code);
//        return new ResponseEntity<>(headers, HttpStatus.PERMANENT_REDIRECT);
//    }

    @RequestMapping(path = {"/spauth"}, method = {RequestMethod.GET})
    public ResponseEntity<Void> spCallback(@RequestParam("spapi_oauth_code") String code, @RequestParam("selling_partner_id") String sellerId,
                                           @RequestParam(value = "state", required = false) String state, HttpServletResponse response) {
        try {
            AuthState siteInfo = new ObjectMapper().readValue(state, AuthState.class);
//            adsAccountService.accessSpToken(code, siteInfo, sellerId);
//            String redirectUrl = location.concat("/?1");
            String redirectUrl = "http://localhost:9527/system-settings/shop-authorize?action=ad-authorize&state="+state+"";
            return ResponseEntity.status(HttpStatus.PERMANENT_REDIRECT).header(HttpHeaders.LOCATION, redirectUrl).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(path = {"/auth"}, method = {RequestMethod.GET})
    public ResponseEntity<MultiValueMap<String, String>> adsCallback(@RequestParam("code") String code, @RequestParam("state") String state, HttpServletResponse response) {
        try {
            AuthState siteInfo = new ObjectMapper().readValue(state, AuthState.class);
            adsAccountService.accessToken(code, siteInfo);
            String redirectUrl = "http://localhost:9527/system-settings/shop-authorize?action=ad-authorize-complete";
            return ResponseEntity.status(HttpStatus.PERMANENT_REDIRECT).header(HttpHeaders.LOCATION, redirectUrl).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

//    @RequestMapping(path = {"/auth"}, method = {RequestMethod.GET})
//    public ResponseEntity<MultiValueMap<String, String>> adsCallback(@RequestParam("code") String code,  @RequestParam(value = "state", required = false) String state, HttpServletResponse response) {
//        try {
//            AuthState siteInfo = new ObjectMapper().readValue(state, AuthState.class);
//            adsAccountService.accessToken(code, siteInfo);
////            String redirectUrl = location.concat("/?2");
//            String redirectUrl = "http://localhost:9527/system-settings/shop-authorize?action=ad-authorize-complete";
//            return ResponseEntity.status(HttpStatus.PERMANENT_REDIRECT).header(HttpHeaders.LOCATION, redirectUrl).build();
//        } catch (Exception e) {
//            e.printStackTrace();
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//        }
//    }

    @RequestMapping(path = {"/list"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*",allowCredentials = "true")
//    @CrossOrigin(origins = "http://localhost:8091",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> getAccList(@AuthenticationPrincipal LoginUser loginUser, @RequestBody AdsAccountSearchVO param) {
        return new ResponseEntity<>(adsAccountService.getAccList(Long.valueOf(loginUser.getId()), param), HttpStatus.OK);
    }


//    @RequestMapping(path = {"/aa"}, method = {RequestMethod.POST})
//    @CrossOrigin(origins = "*",allowCredentials = "true")
//    public ResponseEntity<Map<String, Object>> aa() {
//        spTokenApi.getMarketplaceParticipations("Atza|IwEBIOgifrBtJMFCY0oevo6hIfd64bRPcjGzj_2r3rHxxC9QZ4bawq0hJHFUbGrSmPEoqmsGWp32qjbSatJe8SaTwW16vxTgSuT7s0UX2hBpGERhALYEK9Hf3Z2_XTe6LldRcuZSpAOp1ErIEmteOTkr5EKajklFGLZ_OSqdKcwnD30mS2G8dTj-y6JhRZbf9g0ToO0ZqwjljndReW8QBmuO8gFwxWL4Sr1Aceaw8b3JD3rC5tWiyRMyRHW63HXbIYTrk76yoO5sYpyy_reLeOGQSICGodyXdXp4PDPisK5OHcnwtqElE0KEPLXoxvcBrpWeEHN_3vylRNuWi87xuj7kzYfnw5d3_aBqOnauLVJEAMPotA");
//        return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
//    }

}
