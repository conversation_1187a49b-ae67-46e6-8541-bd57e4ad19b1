package com.yiyitech.ads.controller;

import com.yiyitech.ads.model.LoginUser;
import com.yiyitech.ads.service.AdsUserService;
import com.yiyitech.ads.vo.search.AdsUserSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsUserController.java
 * @Description
 * @createTime 2024年01月18日 16:46:00
 */
@RestController
@RequestMapping("/v1/user")
public class AdsUserController {
    @Autowired
    private AdsUserService adsUserService;

    @RequestMapping(path = {"/account"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*", allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> searchUserAccountList(@RequestParam("user") String empAcc) {
        return new ResponseEntity<>(adsUserService.searchUserAccountList(empAcc), HttpStatus.OK);
    }

    @RequestMapping(path = {"/info"}, method = {RequestMethod.POST})
    @CrossOrigin(origins = "*",allowCredentials = "true")
    public ResponseEntity<Map<String, Object>> getUserInfo(@AuthenticationPrincipal LoginUser loginUser) {
        return new ResponseEntity<>(adsUserService.getUserInfo(Long.valueOf(loginUser.getId()), loginUser.isAdmin()), HttpStatus.OK);
    }
}
