package com.yiyitech.ads.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName com.yiyitech.ads.constants.Constants
 * @Description 常量
 * @date 2021/6/28 16:54
 */
public class CommonConstants {
    //默认数据表状态值
    public static final boolean FORM_STATUS_EFFECTIVE = true;
    public static final boolean FORM_STATUS_INVALID = false;

    public static final int REDIS_DB = 1;
    //缓存前缀：sp / ads
    public static final String SP_PREFIX_KEY = "sp:";
    public static final String ADS_PREFIX_KEY = "ads:";
    //ads：账号令牌
    public static final String ACCOUNT_ACCESS_TOKEN = "account:accessToken:";
    public static final String ACCOUNT_REFRESH_TOKEN = "account:refreshToken:";

    //ads任务
    //解析报告任务：加锁
    public static final String GETREPORT_LOCK = "getReport:lock:";
    //自动否词任务：加锁
    public static final String ANTO_NEG_LOCK = "auto:neg:lock";

    public static final String ACCESS_TOKEN_REDIS_KEY = "access_token";

    //活动，活动组，关键词状态（可用、暂停、归档）
    public static final String ENABLED_STATE = "ENABLED";
    public static final String PAUSED_STATE = "PAUSED";
    public static final String ARCHIVED_STATE = "ARCHIVED";
}
