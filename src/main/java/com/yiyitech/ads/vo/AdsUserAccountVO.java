package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsUserShopVO.java
 * @Description
 * @createTime 2024年01月18日 16:56:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsUserAccountVO {
    private String countryCode;
    private String countryName;
    private List<AdsUserAccountVO.AccountProfile> accountProfiles = new ArrayList<>();

    @Getter
    @Setter
    public static class AccountProfile{
        private String accountId;
        private String accountName;
        private String profileId;
        private String proCountryCode;
//        private String proCountryName;
    }
}
