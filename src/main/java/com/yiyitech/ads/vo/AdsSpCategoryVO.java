package com.yiyitech.ads.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSpCategoryVO.java
 * @Description
 * @createTime 2025年02月18日 14:23:00
 */
@Getter
@Setter
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true) // 忽略不需要的字段
public class AdsSpCategoryVO {
    private Long id;
    private String na;
    private Long categoryId;
    private String categoryName;
    private Long parentId;
    private String parentName;
    //子类
    private List<AdsSpCategoryVO> ch;

//    public long getCategoryId() {
//        return this.id;
//    }
//    public String getCategoryName() {
//        return this.na;
//    }
}
