package com.yiyitech.ads.vo;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AmCampaignVO.java
 * @Description
 * @createTime 2023年11月23日 13:39:00
 */
@Setter
@Getter
@Accessors(chain = true)
public class AdsCampaignVO {
    private String accountId;
    private Long profileId;
    private String portfolioId;
    private String portfolioName;
    private String campaignId;
    private String name;
    private List<AdsCampaignVO.AdGroupData> adGroup;

    @Getter
    @Setter
    public static class AdGroupData{
        private String adGroupId;
        private String name;
    }


}
