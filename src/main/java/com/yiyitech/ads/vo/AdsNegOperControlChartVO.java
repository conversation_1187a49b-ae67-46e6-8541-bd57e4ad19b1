package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegOperControlChartVO.java
 * @Description
 * @createTime 2025年07月10日 17:10:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsNegOperControlChartVO {
    private Integer acos0;
    private Integer acos11;
    private Integer acos21;
    private Integer acos51;
    private Integer acos71;
    private Integer acos101;
    private Integer noSales;
    private Integer noClicks;
    private List<AdsNegOperControlChartVO.Data> dataLst;

    @Getter
    @Setter
    public static class Data {
        //活动名称
        private String campaignName;
        //组名称
        private String adGroupName;
        //产投比（ACOS = 花费/销售）、有花费无销售、无点击
        private Double acos;
        //销售额
        private Double sales1d;
        //花费
        private Double cost;
        //value
        private String value;
        //asin
        private String asin;
        //asin图片
        private String picture;
        //所属活动num
        private Integer belongsNum;
        //曝光量
        private Integer impressions;
        //点击次数
        private Integer clicks;
        //订单数
        private Integer purchases1d;
        //点击率（CTR = 点击/曝光）
        private Double clickThroughRate;
        //转化率（CVR = 订单数/点击次数）
        private Double conversionRate;
        //单次点击成本（CPC）
        private Double costPerClick;
        //当前词唯一标识
        private String uniqueSign;
        //关键字类型
        private String keywordType;
        //搜索词类型 1关键词否定，2商品ASIN否定
        private Integer searchTermType;
    }
}
