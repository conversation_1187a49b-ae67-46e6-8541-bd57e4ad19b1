package com.yiyitech.ads.vo;

import com.yiyitech.ads.model.AdsAccountProfileModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAccountVO.java
 * @Description
 * @createTime 2025年06月22日 23:59:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsAccountVO {
    private String accountInfoId;
    private String accountId;
    private String accountName;
    private String refreshTokenSp;
    private String refreshToken;
    private Boolean status;
    private List<AdsAccountVO.Profile> accountProfiles = new ArrayList<>();

    @Getter
    @Setter
    public static class Profile{
        private String accProId;
        private String profileId;
        private String proName;
        private String proCountry;
        private String proCurrency;
    }


}
