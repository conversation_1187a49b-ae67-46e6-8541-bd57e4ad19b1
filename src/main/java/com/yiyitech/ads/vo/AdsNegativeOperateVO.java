package com.yiyitech.ads.vo;

import com.yiyitech.ads.model.request.AdsSpCampaignCreateRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperateVO.java
 * @Description
 * @createTime 2025年07月09日 17:20:00
 */
@Getter
@Setter
public class AdsNegativeOperateVO {
    //操作来源：1日志处理，2主动否词
    private Integer source;
    private List<AdsNegativeOperateVO.OperateInfo> operateLst;

    @Getter
    @Setter
    public static class OperateInfo{
        //当前选中记录的主键id值
        private Long recordId;
        //否定内容
        private String value;
        //否定方式：1精准否，2词组否，3商品asin否
        private Integer matchType;
        //否定范围：1当前广告组合，2当前广告活动
        private Integer scope;
        //精准+词组的形式时，词组放在subInfo中
        private AdsNegativeOperateVO.OperateInfo subInfo;
    }

}
