package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsExchangeRateVO.java
 * @Description
 * @createTime 2024年01月30日 14:07:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsExchangeRateVO {
    //id
    private long id;
    //原始币种
    private String originalCurrencyCode;
    //目标币种
    private String targetCurrencyCode;
    //标准汇率
    private String sExchangeRate;
    //自定义汇率
    private String cExchangeRate;
    //生效日期
    private String effectDate;

//    //汇率状态（0无效、1有效）
//    private boolean status;

    private String startDate;
    private String endDate;
}
