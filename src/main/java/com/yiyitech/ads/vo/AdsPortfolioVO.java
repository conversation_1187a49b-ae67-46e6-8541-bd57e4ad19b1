package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsPortfolioVO.java
 * @Description
 * @createTime 2025年02月18日 20:39:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsPortfolioVO {
    private String budget;
    private String profileId;
    private boolean inBudget;
    private String name;
    private String portfolioId;
    private String state;
}
