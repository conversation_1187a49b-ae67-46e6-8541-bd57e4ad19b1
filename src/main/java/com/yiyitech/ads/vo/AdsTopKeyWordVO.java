package com.yiyitech.ads.vo;

import lombok.Data;


import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AmTopKeyWordVO.java
 * @Description
 * @createTime 2023年11月23日 13:37:00
 */
@Data
public class AdsTopKeyWordVO {
    private String campaignId;
    private String adGroupId;
    private String keywordId;
    //活动名称
    private String campaignName;
    //活动组名称
    private String groupName;
    //关键词内容
    private String keywordText;
    //关键词匹配范围(EXACT、BROAD、PHRASE)
    private String matchType;
    //关键词状态
    private String state;
    //关键词bid
    private String bid;
    //曝光
    private String impressions;
    //花费
    private String cost;
    //点击
    private String clicks;
    ///ctr
    private String clickThroughRate;
    //cpc
    private String costPerClick;
    //acos（产投比）
    private String acosClicks;
    //roas（回报率）
    private String roasClicks;
    //销售额
    private String sales;
    //订单数
    private String purchases;

    public void defaultData(AdsTopKeyWordVO vo) {
        Field[] field = vo.getClass().getDeclaredFields();
        try {
            for (int j = 0; j < field.length; j++) {
                field[j].setAccessible(true);
                Object value = field[j].get(vo);
                if (null == value) {
                    field[j].set(vo, "1");
                }
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }


}
