package com.yiyitech.ads.vo.search;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegOperLogSearchVO.java
 * @Description
 * @createTime 2025年07月03日 22:32:00
 */
@Getter
@Setter
public class AdsNegOperLogSearchVO {
    //查看商品的asin值
    private String asin;
    //1通知-查看、2主动推送日志-查看
    private Integer flag;
    //从查询关键词。搜索词、以及checkAsinLog接口返回的uniqueSign
    private String uniqueSign;
    //对比类型：1关键词，2搜索词
    private Integer comparisonType;
    //否定操作日志的筛选开始时间
    private String startDate;
    //否定操作日志的筛选开始时间 / check接口的本次登陆时间
    private String endDate;
    //日志状态（全部null值，1已完成，2待查看，3待处理）
    private Integer status;
    //
    private String accProId;
    //操作员
    private String operator;
}
