package com.yiyitech.ads.vo.search;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegOperSearchVO.java
 * @Description
 * @createTime 2025年07月10日 16:11:00
 */
@Getter
@Setter
public class AdsNegOperSearchVO {
    //筛选的店铺（站点）：null全部
    private String accProId;
    //操作员：暂时都传null
    private String operator;
    //广告活动：null全部
    private String campaign;
    //广告活动组：null全部
    private String adGroup;
    //状态：null全部，1归档除外，2启用，3暂停，4归档
    private Integer status;
    //asin值
    private String asin;
    //类型：1关键词，2搜索词
    private Integer type;
    //开始时间
    private String startDate;
    //结束时间
    private String endDate;
    //acos类型：1.0-10，2.11-20，3.21-50，4.51-70，5.71-100，6.101以上，7.有花费无销售额，8.无点击
    private Integer acosType;

    //用作关键词查搜索词 / 搜索词查关键词
    private String uniqueSign;

}
