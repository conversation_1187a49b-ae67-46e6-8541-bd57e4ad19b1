package com.yiyitech.ads.vo.search;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCreateKeywordsVO.java
 * @Description
 * @createTime 2025年02月11日 18:31:00
 */
@Setter
@Getter
@Accessors(chain = true)
public class AdsCreateKeywordsVO {
    private String profileId;
    private List<AdsCreateKeywordsVO.KeywordsData> createKeywordParam;

    @Getter
    @Setter
    public static class KeywordsData{
        private String campaignId;
        private String matchType;
        private String state;
        private double bid;
        private String adGroupId;
        private String keywordText;
    }

}
