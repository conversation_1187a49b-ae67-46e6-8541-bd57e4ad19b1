package com.yiyitech.ads.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsMonitorRuleVO.java
 * @Description
 * @createTime 2025年07月02日 11:54:00
 */
@Setter
@Getter
@Accessors(chain = true)
public class AdsSvcConfigurationVO {
    private String id;
    private String name;
    private String description;
    private String notificationMethod;
    private String monitorObj;
    private Boolean isEnable;

}
