package com.yiyitech.ads.vo;

import com.yiyitech.ads.model.request.AdsSpCampaignCreateRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCampaignCreateVO.java
 * @Description
 * @createTime 2025年02月19日 17:36:00
 */
@Setter
@Getter
@Accessors(chain = true)
public class AdsCampaignCreateVO {
    private String accountId;
    private String profileId;
    private List<AdsSpCampaignCreateRequest.CampaignData> campaigns;

    @Getter
    @Setter
    public static class CampaignData{
        private String name;
        private String portfolioId;
        private AdsSpCampaignCreateRequest.BudgetData budget;
        private String startDate;
        private String endDate;
        private String state;
        private String targetingType;
        private AdsSpCampaignCreateRequest.DynamicBiddingData dynamicBidding;
    }

    @Getter
    @Setter
    public static class BudgetData{
        private String budgetType;
        private double budget;
    }

    @Getter
    @Setter
    public static class DynamicBiddingData{
        private String strategy;
        private List<AdsSpCampaignCreateRequest.PlacementBiddingData> placementBidding;
    }

    @Getter
    @Setter
    public static class PlacementBiddingData{
        private int percentage;
        private String placement;
    }
}

