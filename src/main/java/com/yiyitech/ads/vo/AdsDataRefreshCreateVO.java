package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsDataRefresh.java
 * @Description
 * @createTime 2025年07月10日 14:19:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsDataRefreshCreateVO {
    private String accProId;
    private String startDate;
    private String endDate;
    //true刷新。false不刷新只查看时间
    private Boolean refresh;
}
