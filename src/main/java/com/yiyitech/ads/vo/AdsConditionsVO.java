package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName adsConditionsVO.java
 * @Description
 * @createTime 2025年07月02日 12:04:00
 */
@Setter
@Getter
@Accessors(chain = true)
public class AdsConditionsVO {
    private Long conditionId;
    private Integer days;
    private Integer operator;
    private Long fieldId;
    private String fieldName;
    private String fieldValue;
}
