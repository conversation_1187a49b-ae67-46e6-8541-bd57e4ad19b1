package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsMonitorRulesVO.java
 * @Description
 * @createTime 2025年07月02日 16:23:00
 */
@Setter
@Getter
@Accessors(chain = true)
public class AdsMonitorRulesVO {
    private Long ruleId;
    private String ruleName;
    private Integer actionType;
    private Integer sortOrder;
    private List<AdsConditionGroupsVO> conditionGroups;
}
