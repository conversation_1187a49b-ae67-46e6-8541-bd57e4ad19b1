package com.yiyitech.ads.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeLogVO.java
 * @Description
 * @createTime 2025年07月08日 21:20:00
 */
@Setter
@Getter
@Accessors(chain = true)
public class AdsNegativeLogLstVO {
    private Long id;
    /** 修改级别（1广告组合，2广告活动，3否词运营） */
    private Integer operLevel;
    private String campaignName;
    private String adGroupName;
    private String asin;
    private String asinItemName;
    private String pictureUrl;
    /** 修改类型（1关键词否定，2商品ASIN否定，3自动匹配否定，4否词运营监控） */
    private Integer operType;
    private String beforeOper;
    private String afterOper;
    private String updateTime;
    private String updateBy;
    /** 日志状态（1已完成，2待查看，3待处理） */
    private Integer status;
    /** 否定类型（1精准，2词组，3asin） */
    private Integer negativeType;
    private String negativeValue;

}
