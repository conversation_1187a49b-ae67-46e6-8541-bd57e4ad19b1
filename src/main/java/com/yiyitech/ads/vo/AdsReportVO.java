package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsDailySearchTermReportVO.java
 * @Description
 * @createTime 2023年12月19日 17:11:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsReportVO {
    private Long id;
    //搜索开始日期
    private String startDate;
    //搜索结束日期
    private String endDate;
    //报告日期
    private String date;
    //搜索词
    private String searchTerm;
    //账户id（REFERENCES ads_account_info）
    private String accountId;
    //账户文件id（REFERENCES ads_account_profile）
    private String profileId;
    //广告组合id
    private String portfolioId;
    //活动id
    private String campaignId;
    //活动名称
    private String campaignName;
    //活动状态
    private String campaignStatus;
    //活动预算货币编码
    private String campaignBudgetCurrencyCode;
    //活动预算类型
    private String campaignBudgetType;
    //活动预算金额
    private Double campaignBudgetAmount;
    //组id
    private String adGroupId;
    //组名称
    private String adGroupName;
    //组状态
    private String adGroupStatus;
    //关键字id
    private String keywordId;
    //关键字内容
    private String keyword;
    //关键字类型
    private String keywordType;
    //关键字状态
    private String adKeywordStatus;
    //关键字出价
    private Double keywordBid;
    //投放目标（关键词）
    private String targeting;
    //搜索结果首页首位展示量份额
    private Double topOfSearchImpressionShare;
    //匹配类型（Broad广泛、Phrase词组、Exact精准）
    private String matchType;
    //曝光量
    private Integer impressions;
    //点击量
    private Integer clicks;
    //销售量（1d）
    private Integer purchases1d;
    //相同sku的销售量（1d）
    private Integer purchasesSameSku1d;
    //销售额（1d）
    private Double sales1d;
    //花费
    private Double cost;
    //产投比（ACOS = 花费/销售）
    private Double acosClicks;
    //单次点击成本（CPC = 花费/点击）
    private Double costPerClick;
    //点击率（CTR = 点击/曝光）
    private Double clickThroughRate;
    //转化率 (CVR 订单数/点击率)
    private Double cvr;
    //单位销售点击量（1d）
    private Integer unitsSoldClicks1d;
    //相同Sku的单位销量（1d）
    private Integer unitsSoldSameSku1d;
    //相同sku的归因销售额（1d）
    private Double attributedSalesSameSku1d;
    //搜索词唯一标识：accountId-profileId-campaignId-adGroupId-keywordId-searchTerm
    //关键词唯一标识：keywordId
    private String uniqueSign;

    public AdsReportVO(){
    }
}
