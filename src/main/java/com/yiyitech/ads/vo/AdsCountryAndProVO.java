package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsCountryAndProVO.java
 * @Description
 * @createTime 2025年06月23日 14:03:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsCountryAndProVO {
    private String countryCode;
    private String countryName;
    private List<AdsCountryAndProVO.Region> regions = new ArrayList<>();
    private List<AdsCountryAndProVO.AccountProfile> accountProfiles = new ArrayList<>();

    @Getter
    @Setter
    public static class AccountProfile{
        private Long accInfoId;
        private String accountId;
        private String accountName;
        private String accProId;
        private String profileId;
        private String proName;
        private String proCountryCode;
        private List<AdsCountryAndProVO.Campaigns> campaigns = new ArrayList<>();
    }

    @Getter
    @Setter
    public static class Region{
        private String regionName;
        private List<AdsCountryAndProVO.Country> countrys = new ArrayList<>();
    }

    @Getter
    @Setter
    public static class Country{
        private String countryCode;
        private String countryName;
    }

    @Getter
    @Setter
    public static class Campaigns{
        private String campaignId;
        private String campaignName;
        private List<AdsCountryAndProVO.AdGroups> adGroups = new ArrayList<>();
    }

    @Getter
    @Setter
    public static class AdGroups{
        private String adGroupId;
        private String adGroupName;
    }
}
