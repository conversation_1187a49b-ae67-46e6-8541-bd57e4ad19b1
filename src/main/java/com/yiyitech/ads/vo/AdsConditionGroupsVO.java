package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsConditionGroups.java
 * @Description
 * @createTime 2025年07月02日 11:54:00
 */
@Setter
@Getter
@Accessors(chain = true)
public class AdsConditionGroupsVO {
    private Long groupId;
    private String logicalRelation;
    private Integer sortOrder;
    private List<AdsConditionsVO> conditions;
}
