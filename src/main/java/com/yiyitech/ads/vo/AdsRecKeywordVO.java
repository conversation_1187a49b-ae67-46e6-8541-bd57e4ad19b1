package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsRecKeywordVO.java
 * @Description
 * @createTime 2025年02月11日 16:52:00
 */
@Setter
@Getter
@Accessors(chain = true)
public class AdsRecKeywordVO {
    private String keyword;
    List<AdsRecKeywordVO.BidInfoData> bidInfo;
//    private String translation;
    private String searchTermImpressionRank;
    private String searchTermImpressionShare;
    private String recId;

    @Getter
    @Setter
    public static class BidInfoData{
        private String matchType;
        private int rank;
        private double bid;
        List<AdsRecKeywordVO.SuggestedBidData> suggestedBid;

        public void setBid(double bid) {
            this.bid = bid / 100;
        }
    }

    @Getter
    @Setter
    public static class SuggestedBidData{
        private double rangeStart;
        private double rangeMedian;
        private double rangeEnd;

        public void setRangeStart(double rangeStart) {
            this.rangeStart = rangeStart / 100;
        }
        public void setRangeMedian(double rangeMedian) {
            this.rangeMedian = rangeMedian / 100;
        }
        public void setRangeEnd(double rangeEnd) {
            this.rangeEnd = rangeEnd / 100;
        }
    }

}
