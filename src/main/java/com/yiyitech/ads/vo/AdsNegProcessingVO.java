package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegProcessingVO.java
 * @Description
 * @createTime 2025年07月11日 16:35:00
 */
@Getter
@Setter
public class AdsNegProcessingVO {
    //操作来源：1日志处理，2主动否词
    private Integer operSource;
    //活动名称
    private String campaignName;
    //组名称
    private String adGroupName;
    //来源（1搜索词报告，2关键词报告）
    private Integer negSource;
    //asin
    private String asin;
    //asin图片
    private String picture;
    //修改级别：1关键词否定，2商品ASIN否定，3自动匹配否定
    private Integer operType;
    //否定的词的集合
    private List<AdsNegProcessingVO.OperateInfo> operateLst;

    @Getter
    @Setter
    public static class OperateInfo{
        //搜索词 / 关键词唯一标识
        private String uniqueSign;
        //日志否定时的日志记录id
        private Long recordId;
        //否定内容
        private String value;
        //否定方式：1精准否，2词组否，3商品asin否
        private Integer matchType;
        //否定范围：1当前广告组合，2当前广告活动
        private Integer scope;
        //精准+词组的形式时，词组放在subInfo中
        private AdsNegativeOperateVO.OperateInfo subInfo;
    }

}
