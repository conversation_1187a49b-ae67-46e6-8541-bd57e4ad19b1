package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeOperLogVO.java
 * @Description
 * @createTime 2025年07月03日 22:24:00
 */
@Getter
@Setter
@Accessors(chain = true)
public class AdsPushNotificationLogVO {
//    private List<AdsPushNotificationLogVO.NegativeAsinVO> notViewed;
//    private List<AdsPushNotificationLogVO.NegativeAsinVO> untreated;
//    private List<AdsPushNotificationLogVO.NegativeAsinVO> offlineNotViewed;
    private List<AdsPushNotificationLogVO.NegativeAsinVO> notification;
    private List<AdsPushNotificationLogVO.NegativeAsinVO> push;
    private Integer num;


    @Getter
    @Setter
    public static class NegativeAsinVO {
        private String asin;
        private String pictureUrl;
        private String iteamName;
        private Integer num;
        //每个asin组内第一条记录的时间
        private String time;
        //1待查看，2待处理
        private Integer flag;
        //记录本次登陆的时间，给查看接口调用，以便查看记录和推送记录的时间保持一致
        private String loginTime;
    }
}
