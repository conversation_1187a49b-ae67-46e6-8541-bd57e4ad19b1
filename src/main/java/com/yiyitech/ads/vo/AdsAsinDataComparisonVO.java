package com.yiyitech.ads.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAsinDataComparisonVO.java
 * @Description
 * @createTime 2025年07月08日 21:23:00
 */
@Setter
@Getter
@Accessors(chain = true)
public class AdsAsinDataComparisonVO {
    private AdsAsinDataComparisonVO.Info last7;
    private AdsAsinDataComparisonVO.Info last30;
    private AdsAsinDataComparisonVO.Info last65;
    private AdsAsinDataComparisonVO.Info all;


    @Getter
    @Setter
    public static class Info {
        //点击量
        private Integer clicks;
        //销售量
        private Integer purchases1d;
    }

}
