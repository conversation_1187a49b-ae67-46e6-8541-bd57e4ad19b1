package com.yiyitech.ads.util;

import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsUtils.java
 * @Description
 * @createTime 2024年01月22日 16:01:00
 */
@Component
public class AdsCountryUtils {

    //获取国家及国家编码
    public Map<String, String> getCountryMap() {
        List<Locale> locales = Arrays.asList(Locale.getAvailableLocales());
        Map<String, String> countryMap = locales.stream()
                .filter(locale -> !locale.getCountry().isEmpty())
                .collect(Collectors.toMap(Locale::getCountry, Locale::getDisplayCountry, (existing, replacement) -> existing));
//        countryAbbreviations.forEach((key, value) -> {
//            System.out.println(key + "--" + value);
//        });
        return countryMap;
    }

    //获取国家及国家编码(根据header传入的语言来选择显示文字，默认中文)
    public Map<String, String> getCountryMapByLang(String acceptLanguage) {
        List<Locale> locales = Arrays.asList(Locale.getAvailableLocales());

        Locale curLocale;
        if (acceptLanguage.contains("zh")) {
            curLocale = Locale.SIMPLIFIED_CHINESE;
        } else {
            curLocale = Locale.ENGLISH;
        }

        Map<String, String> countryMap = locales.stream()
                .filter(locale -> !locale.getCountry().isEmpty())
                .collect(Collectors.toMap(Locale::getCountry, locale -> locale.getDisplayCountry(curLocale), (existing, replacement) -> existing));
        return countryMap;
    }

    //根据国家编码获取国家名称
    public String getCountryNameByCode(String countryCode) {
        List<Locale> locales = Arrays.asList(Locale.getAvailableLocales());
        Map<String, String> countryMap = locales.stream()
                .filter(locale -> !locale.getCountry().isEmpty())
                .collect(Collectors.toMap(Locale::getCountry, Locale::getDisplayCountry, (existing, replacement) -> existing));
        return countryMap.get(countryCode);
    }

    //获取国家货币名称及简写
    public void getCurrency() {
        Set<Currency> currencies = Currency.getAvailableCurrencies();
        int i = 0;
        // 遍历并输出货币编码
        for (Currency currency : currencies) {
            System.out.println(i += 1);
            System.out.println("Country: " + currency.getDisplayName() +
                    ", Currency Code: " + currency.getCurrencyCode());
        }
    }

    //获取国家名称、编码、货币名称、货币编码
    public void getCountryAndCurrency() {
        // 获取所有可用的国家缩写编码
        List<Locale> locales = Arrays.asList(Locale.getAvailableLocales());
        // 遍历国家缩写编码，获取对应的货币编码
        for (Locale locale : locales) {
            if(!locale.getCountry().isEmpty()){
                Currency currency = Currency.getInstance(locale);
                System.out.println("国家名称："+locale.getDisplayCountry()+"  国家编码: " + locale.getCountry() +"  货币名称："+currency.getDisplayName()+"  货币编码: " + currency.getCurrencyCode());
            }
        }
    }


    //根据国家编码获取货币编码（Locale的对应关系不全，后续有需求改动）
    public String getCurrencyCode(String countryCode) {
        try {
            Locale locale = new Locale("", countryCode);
            Currency currency = Currency.getInstance(locale);
            return currency.getCurrencyCode();
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    //根据国家货币编码获取货币名称（Locale的对应关系不全，后续有需求改动）
    public String getCurrencyNameByCode(String currencyCode) {
        try {
            Currency currency = Currency.getInstance(currencyCode.toUpperCase());
            return currency.getDisplayName();
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
//    public static void main(String[] args) {
//        List<Locale> locales = Arrays.asList(Locale.getAvailableLocales());
//        // 遍历国家缩写编码，获取对应的货币编码
//        for (Locale locale : locales) {
//            if(!locale.getCountry().isEmpty()){
//                Currency currency = Currency.getInstance(locale);
//                System.out.println("国家名称："+locale.getDisplayCountry()+"  国家编码: " + locale.getCountry() +"  货币名称："+currency.getDisplayName()+"  货币编码: " + currency.getCurrencyCode());
//            }
//        }
//    }

    //生成随机数用作授权token加密
    public static String generateRandomToken(int byteLength) {
        SecureRandom secureRandom = new SecureRandom();
        byte[] randomBytes = new byte[byteLength];
        secureRandom.nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }

}
