package com.yiyitech.ads.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName QueryWrapperUtil.java
 * @Description
 * @createTime 2025年07月09日 12:04:00
 */
public class QueryWrapperUtil {

    public static <T> void appendAccountProfileCondition(QueryWrapper<T> wrapper, List<AdsAccountProfileModel> profileLst) {
        if (CollectionUtils.isEmpty(profileLst)) {
            return;
        }
        wrapper.and(w -> {
            for (AdsAccountProfileModel profile : profileLst) {
                w.or(sub -> sub.eq("account_id", profile.getAccountId())
                        .eq("profile_id", profile.getProfileId()));
            }
        });
    }

    public static <T> void appendSearchTermCondition(QueryWrapper<T> wrapper, List<String> searchTermLst, Boolean exceptSearchTerm) {
        if (CollectionUtils.isEmpty(searchTermLst)) {
            return;
        }
        wrapper.and(w -> {
            for (String composite : searchTermLst) {
                String[] parts = composite.split("-", 6);//限制6段（后续需要再补充）
                if (parts.length < 5 || parts.length > 6) continue;

                String accountId = parts[0];
                String profileIdStr = parts[1];
                String campaignId = parts[2];
                String adGroupId = parts[3];
                String keywordId = parts[4];
                Long profileId;

                try {
                    profileId = Long.parseLong(profileIdStr);
                } catch (NumberFormatException e) {
                    continue; //跳过无效profileId
                }
                if (parts.length == 6 && !exceptSearchTerm) {
                    String searchTerm = parts[5];
                    w.or(orWrapper -> orWrapper
                            .eq("account_id", accountId)
                            .eq("profile_id", profileId)
                            .eq("campaign_id", campaignId)
                            .eq("ad_group_id", adGroupId)
                            .eq("keyword_id", keywordId)
                            .eq("search_term", searchTerm)
                    );
                } else {
                    w.or(orWrapper -> orWrapper
                            .eq("account_id", accountId)
                            .eq("profile_id", profileId)
                            .eq("campaign_id", campaignId)
                            .eq("ad_group_id", adGroupId)
                            .eq("keyword_id", keywordId)
                    );
                }
            }
        });
    }


}
