package com.yiyitech.ads.util;

import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DateUtil.java
 * @Description
 * @createTime 2024年01月10日 14:36:00
 */
public class AdsDateUtil {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public List<String> getDatesBetween(String startDateStr, String endDateStr) {
        LocalDate startDate = LocalDate.parse(startDateStr);
        LocalDate endDate = LocalDate.parse(endDateStr);
        List<String> datesInRange = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        while (!startDate.isAfter(endDate)) {
            datesInRange.add(startDate.format(formatter));
            startDate = startDate.plusDays(1);
        }
        return datesInRange;
    }

    public static Map<String, String> getLastDays(int num) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate endDate = LocalDate.now().minusDays(1);
        LocalDate startDate = endDate.minusDays(num - 1);

        Map<String, String> dateRange = new HashMap<>();
        dateRange.put("startDateStr", startDate.format(formatter));
        dateRange.put("endDateStr", endDate.format(formatter));
        return dateRange;
    }

    /**
     * 根据传入的 yyyy-MM-dd 字符串，生成一天的起止时间（00:00:00 到 23:59:59）
     * @param startDateStr 开始日期字符串（yyyy-MM-dd）
     * @param endDateStr   结束日期字符串（yyyy-MM-dd）
     * @return Map 包含 formattedStartTime 和 formattedEndTime
     */
    public static Map<String, String> buildFullDayTimeRange(String startDateStr, String endDateStr) {
        LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
        LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);

        //00:00:00
        LocalDateTime startDateTime = startDate.atStartOfDay();
        //23:59:59
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

        Map<String, String> result = new HashMap<>();
        result.put("startTime", startDateTime.format(DATETIME_FORMATTER));
        result.put("endTime", endDateTime.format(DATETIME_FORMATTER));
        return result;
    }

    /**
     * 将 java.sql.Timestamp 格式化为 yyyy-MM-dd HH:mm:ss 字符串
     * @param timestamp 时间戳
     * @return 格式化字符串
     */
    public static String formatTimestamp(Timestamp timestamp) {
        if (timestamp == null) return null;
        return timestamp.toLocalDateTime().format(DATETIME_FORMATTER);
    }

    /**
     * 将 java.util.Date 格式化为 yyyy-MM-dd HH:mm:ss 字符串
     * @param date 日期对象
     * @return 格式化字符串
     */
    public static String formatDate(Date date) {
        if (date == null) return null;
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime()
                .format(DATETIME_FORMATTER);
    }

    /**
     * 将 yyyy-MM-dd 格式的字符串转为 java.util.Date
     * @param dateStr 日期字符串
     * @return java.util.Date 对象
     */
    public static Date parseDateYmd(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) return null;
        LocalDate localDate = LocalDate.parse(dateStr, DATE_FORMATTER);
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }


}
