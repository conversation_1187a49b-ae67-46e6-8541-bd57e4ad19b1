package com.yiyitech.ads.util;

import cn.hutool.core.codec.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AesUtil.java
 * @Description
 * @createTime 2023年12月28日 14:09:00
 */
public final class AESUtil {
    private static final String SECRET_KEY = "yiyiTech@ads";

    public AESUtil() {
    }

    public static String encode(String plainText, String secretKey) throws RuntimeException {
        try {
            if (secretKey == null) {
                secretKey = SECRET_KEY;
            }
            Key sKey = getKey(secretKey);
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(1, sKey);
            byte[] p = plainText.getBytes("UTF-8");
            byte[] result = cipher.doFinal(p);
            String encodeText = Base64.encode(result);
            return encodeText;
        } catch (Exception e) {
            throw new RuntimeException("AES encode exception", e);
        }
    }

    public static String decode(String decodeText, String secretKey) throws RuntimeException {
        try {
            if (secretKey == null) {
                secretKey = SECRET_KEY;
            }
            Key sKey = getKey(secretKey);
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(2, sKey);
            byte[] buffer = Base64.decode(decodeText);
            byte[] result = cipher.doFinal(buffer);
            String plainText = new String(result, "UTF-8");
            return plainText;
        } catch (Exception e) {
            throw new RuntimeException("AES decode exception", e);
        }
    }

    private static Key getKey(String keySeed) throws NoSuchAlgorithmException {
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(keySeed.getBytes());
        KeyGenerator generator = KeyGenerator.getInstance("AES");
        generator.init(secureRandom);
        return generator.generateKey();
    }

    public static void main(String[] args) {
        String aa = "1dad556d-d756-4caa-8321-a8a6ed25ec0a";
        System.out.println(encode(aa, null));

    }
}