package com.yiyitech.ads.util;


import com.yiyitech.ads.model.AdsDailyAdvertiserReportModel;
import com.yiyitech.ads.model.AdsDailySearchTermReportModel;
import com.yiyitech.ads.vo.AdsAutoNegSearchTerm;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAutoNegSeachTermAggregatorUtil.java
 * @Description
 * @createTime 2025年07月15日 22:18:00
 */
public class AdsAutoNegSeachTermAggregatorUtil {

    public static class MergedSearchTermGroup {
        private List<AdsAutoNegSearchTerm> merged7DayList;
        private List<AdsAutoNegSearchTerm> merged30DayList;

        public MergedSearchTermGroup(List<AdsAutoNegSearchTerm> merged7DayList,
                                     List<AdsAutoNegSearchTerm> merged30DayList) {
            this.merged7DayList = merged7DayList;
            this.merged30DayList = merged30DayList;
        }

        public List<AdsAutoNegSearchTerm> getMerged7DayList() {
            return merged7DayList;
        }

        public List<AdsAutoNegSearchTerm> getMerged30DayList() {
            return merged30DayList;
        }
    }

    public static MergedSearchTermGroup buildMerged7And30(List<AdsDailySearchTermReportModel> fullList, String date,
                                                          Map<String, AdsDailyAdvertiserReportModel> advertiserMap,
                                                          Map<String, List<String>> negLogMap) {
        LocalDate endDay = LocalDate.parse(date);
        ZoneId zone = ZoneId.systemDefault();

        LocalDate from7 = endDay.minusDays(6);
        LocalDate from30 = endDay.minusDays(29);

        List<AdsDailySearchTermReportModel> list7 = fullList.stream()
                .filter(item -> {
                    LocalDate localDate = item.getDate().toInstant().atZone(zone).toLocalDate();
                    return !localDate.isBefore(from7) && !localDate.isAfter(endDay);
                }).collect(Collectors.toList());

        List<AdsDailySearchTermReportModel> list30 = fullList.stream()
                .filter(item -> {
                    LocalDate localDate = item.getDate().toInstant().atZone(zone).toLocalDate();
                    return !localDate.isBefore(from30) && !localDate.isAfter(endDay);
                }).collect(Collectors.toList());

        // 合并逻辑唯一key
        Function<AdsDailySearchTermReportModel, String> groupKeyExtractor = m -> String.join("-",
                m.getAccountId(), String.valueOf(m.getProfileId()),
                m.getCampaignId(), m.getAdGroupId(), m.getKeywordId(), m.getSearchTerm());
        // 这里只做合并，不分 acosType 分类
        List<AdsDailySearchTermReportModel> result7 = new ArrayList<>();
        AcosStatUtils.calculateChartVO(
                true,
                list7,
                null,
                negLogMap,
                AdsDailySearchTermReportModel::getSearchTerm,
                groupKeyExtractor,
                AdsDailySearchTermReportModel::getCost,
                AdsDailySearchTermReportModel::getSales1d,
                AdsDailySearchTermReportModel::getClicks,
                AdsDailySearchTermReportModel::getPurchases1d,
                AdsDailySearchTermReportModel::getImpressions,
                null,
                null,
                result7
        );
        List<AdsDailySearchTermReportModel> result30 = new ArrayList<>();
        AcosStatUtils.calculateChartVO(
                true,
                list30,
                null,
                negLogMap,
                AdsDailySearchTermReportModel::getSearchTerm,
                groupKeyExtractor,
                AdsDailySearchTermReportModel::getCost,
                AdsDailySearchTermReportModel::getSales1d,
                AdsDailySearchTermReportModel::getClicks,
                AdsDailySearchTermReportModel::getPurchases1d,
                AdsDailySearchTermReportModel::getImpressions,
                null,
                null,
                result30
        );

        //转AdsAutoNegSearchTerm
        List<AdsAutoNegSearchTerm> mapped7 = result7.stream()
                .map(m -> {
                    AdsAutoNegSearchTerm term = new AdsAutoNegSearchTerm();
                    BeanUtils.copyProperties(m, term);
                    term.setUniqueSign(groupKeyExtractor.apply(m));
                    //商品信息
                    String asinKey = m.getCampaignId() + "-" + m.getAdGroupId();
                    AdsDailyAdvertiserReportModel adv = advertiserMap.get(asinKey);
                    if (adv != null) {
                        term.setAsin(adv.getAdvertisedAsin());
                        term.setParentAsin(adv.getAdvertisedParentAsin());
                        term.setSku(adv.getAdvertisedSku());
                        term.setPicture(adv.getPictureUrl());
                        term.setAsinItemName(adv.getAdvertisedItemName());
                    }
                    return term;
                }).collect(Collectors.toList());

        List<AdsAutoNegSearchTerm> mapped30 = result30.stream()
                .map(m -> {
                    AdsAutoNegSearchTerm term = new AdsAutoNegSearchTerm();
                    BeanUtils.copyProperties(m, term);
                    term.setUniqueSign(groupKeyExtractor.apply(m));
                    //商品信息
                    String asinKey = m.getCampaignId() + "-" + m.getAdGroupId();
                    AdsDailyAdvertiserReportModel adv = advertiserMap.get(asinKey);
                    if (adv != null) {
                        term.setAsin(adv.getAdvertisedAsin());
                        term.setParentAsin(adv.getAdvertisedParentAsin());
                        term.setSku(adv.getAdvertisedSku());
                        term.setPicture(adv.getPictureUrl());
                        term.setAsinItemName(adv.getAdvertisedItemName());
                    }
                    return term;
                }).collect(Collectors.toList());

        return new MergedSearchTermGroup(mapped7, mapped30);
    }
}
