package com.yiyitech.ads.util;

import com.yiyitech.ads.model.AdsDailyAdvertiserReportModel;
import com.yiyitech.ads.model.AdsDailySearchTermReportModel;
import com.yiyitech.ads.model.AdsDailyTargetingReportModel;
import com.yiyitech.ads.model.MergeableModel;
import com.yiyitech.ads.vo.AdsNegOperControlChartVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AcosSatatUtils.java
 * @Description
 * @createTime 2025年07月10日 22:04:00
 */
public class AcosStatUtils {

    //气泡图页面返回数据
    public static <T> AdsNegOperControlChartVO calculateChartVO(
            Boolean onlyGroup,
            List<T> dataList,
            Map<String, ?> resultMap,
            Map<String, List<String>> negLogMap,
            Function<T, String> negValueExtractor,
            Function<T, String> groupKeyExtractor,
            Function<T, Double> costExtractor,
            Function<T, Double> salesExtractor,
            Function<T, Integer> clickExtractor,
            Function<T, Integer> purchases1dExtractor,
            Function<T, Integer> impressionsExtractor,
            Function<T, String> campaignAdGroupKey,
            Integer acosType,
            List<T> resultModels
    ) {
        //resultMap过滤（当前为asin过滤）
        if (resultMap != null && !resultMap.isEmpty()) {
            Set<String> validKeys = resultMap.keySet();
            dataList = dataList.stream()
                    .filter(item -> validKeys.contains(campaignAdGroupKey.apply(item)))
                    .collect(Collectors.toList());
        }
        //negLogMap（当前为否词过滤）
        if (negLogMap != null && !negLogMap.isEmpty()) {
            dataList = dataList.stream()
                    .filter(item -> {
                        String key = getNegLogKey(item);
                        List<String> negValues = negLogMap.get(key);
                        if (negValues == null || negValues.isEmpty()) return true;
                        String value = negValueExtractor.apply(item);
                        return !negValues.contains(value);//存在否词则排除
                    })
                    .collect(Collectors.toList());
        }
        //分组
        Map<String, List<T>> grouped = dataList.stream().collect(Collectors.groupingBy(groupKeyExtractor));

        AdsNegOperControlChartVO vo = new AdsNegOperControlChartVO()
                .setAcos0(0).setAcos11(0).setAcos21(0).setAcos51(0)
                .setAcos71(0).setAcos101(0).setNoSales(0).setNoClicks(0);

        for (List<T> group : grouped.values()) {
            if (group.isEmpty()) continue;

            T merged = null;
            if (group.get(0) instanceof MergeableModel) {
                @SuppressWarnings("unchecked")
                MergeableModel<T> mergeable = (MergeableModel<T>) group.get(0);
                merged = (T) mergeable.mergeGroup(group);
            } else {
                continue;//不支持合并，跳过
            }

            BigDecimal totalSales = Optional.ofNullable(salesExtractor.apply(merged))
                    .map(BigDecimal::valueOf).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);
            BigDecimal totalCost = Optional.ofNullable(costExtractor.apply(merged))
                    .map(BigDecimal::valueOf).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);
            int totalClicks = Optional.ofNullable(clickExtractor.apply(merged)).orElse(0);

            Optional.ofNullable(purchases1dExtractor.apply(merged)).orElse(0);
            Optional.ofNullable(impressionsExtractor.apply(merged)).orElse(0);

            if (Boolean.TRUE.equals(onlyGroup)) {
                resultModels.add(merged); //仅返回合并后的对象，不计算acos等数据
                continue;
            }

            BigDecimal acos = null;
            if (totalSales.compareTo(BigDecimal.ZERO) > 0) {
                acos = totalCost.divide(totalSales, 4, RoundingMode.HALF_UP);
            }

            boolean matched = false;
            if (acosType == null || acosType == 1) {
                if (acos != null && acos.compareTo(new BigDecimal("0.10")) <= 0) {
                    vo.setAcos0(vo.getAcos0() + 1);
                    matched = true;
                }
            }
            if (acosType == null || acosType == 2) {
                if (acos != null && acos.compareTo(new BigDecimal("0.10")) > 0 && acos.compareTo(new BigDecimal("0.20")) <= 0) {
                    vo.setAcos11(vo.getAcos11() + 1);
                    matched = true;
                }
            }
            if (acosType == null || acosType == 3) {
                if (acos != null && acos.compareTo(new BigDecimal("0.20")) > 0 && acos.compareTo(new BigDecimal("0.50")) <= 0) {
                    vo.setAcos21(vo.getAcos21() + 1);
                    matched = true;
                }
            }
            if (acosType == null || acosType == 4) {
                if (acos != null && acos.compareTo(new BigDecimal("0.50")) > 0 && acos.compareTo(new BigDecimal("0.70")) <= 0) {
                    vo.setAcos51(vo.getAcos51() + 1);
                    matched = true;
                }
            }
            if (acosType == null || acosType == 5) {
                if (acos != null && acos.compareTo(new BigDecimal("0.70")) > 0 && acos.compareTo(new BigDecimal("1.00")) <= 0) {
                    vo.setAcos71(vo.getAcos71() + 1);
                    matched = true;
                }
            }
            if (acosType == null || acosType == 6) {
                if (acos != null && acos.compareTo(new BigDecimal("1.00")) > 0) {
                    vo.setAcos101(vo.getAcos101() + 1);
                    matched = true;
                }
            }
            if (acosType == null || acosType == 7) {
                if (totalCost.compareTo(BigDecimal.ZERO) > 0 && totalSales.compareTo(BigDecimal.ZERO) == 0) {
                    vo.setNoSales(vo.getNoSales() + 1);
                    matched = true;
                }
            }
            if (acosType == null || acosType == 8) {
                if (totalClicks == 0) {
                    vo.setNoClicks(vo.getNoClicks() + 1);
                    matched = true;
                }
            }

            if (acosType != null && matched) {
                resultModels.add(merged);//添加合并后的对象
            }
        }
        return vo;
    }

    private static <T> String getNegLogKey(T item) {
        if (item instanceof AdsDailyTargetingReportModel) {
            AdsDailyTargetingReportModel m = (AdsDailyTargetingReportModel) item;
            return String.join("#",
                    m.getAccountId(),
                    String.valueOf(m.getProfileId()),
                    m.getCampaignId(),
                    m.getAdGroupId()
            );
        }
        if (item instanceof AdsDailySearchTermReportModel) {
            AdsDailySearchTermReportModel m = (AdsDailySearchTermReportModel) item;
            return String.join("#",
                    m.getAccountId(),
                    String.valueOf(m.getProfileId()),
                    m.getCampaignId(),
                    m.getAdGroupId()
            );
        }
        //后续根据需要继续扩展其他类型
        return "";
    }


    //气泡图二级页面组装数据
    public static <T> List<AdsNegOperControlChartVO.Data> convertModelList(
            List<T> modelList,
            Map<String, AdsDailyAdvertiserReportModel> resultMap,
            Integer acosType) {

        List<AdsNegOperControlChartVO.Data> result = new ArrayList<>();

        for (T item : modelList) {
            AdsNegOperControlChartVO.Data data = new AdsNegOperControlChartVO.Data();
            String campaignName = null;
            String adGroupName = null;
            String campaignId = null;
            String adGroupId = null;
            String keywordId = null;
            String searchTerm = null;
            String keywordType = null;
            String accountId = null;
            Long profileId = null;
            Integer impressions = null;
            Integer clicks = null;
            Integer purchases1d = null;
            Double sales1d = null;
            Double cost = null;

            //共同字段抽取（按类型分支）
            if (item instanceof AdsDailyTargetingReportModel) {
                AdsDailyTargetingReportModel model = (AdsDailyTargetingReportModel) item;
                campaignName = model.getCampaignName();
                adGroupName = model.getAdGroupName();
                campaignId = model.getCampaignId();
                adGroupId = model.getAdGroupId();
                keywordId = model.getKeywordId();
                keywordType = model.getKeywordType();
                accountId = model.getAccountId();
                profileId = model.getProfileId();
                impressions = model.getImpressions();
                clicks = model.getClicks();
                purchases1d = model.getPurchases1d();
                sales1d = model.getSales1d();
                cost = model.getCost();

                data.setValue(model.getKeyword());
                data.setUniqueSign(String.join("-", accountId, String.valueOf(profileId), campaignId, adGroupId, keywordId));

            } else if (item instanceof AdsDailySearchTermReportModel) {
                AdsDailySearchTermReportModel model = (AdsDailySearchTermReportModel) item;
                campaignName = model.getCampaignName();
                adGroupName = model.getAdGroupName();
                campaignId = model.getCampaignId();
                adGroupId = model.getAdGroupId();
                keywordId = model.getKeywordId();
                searchTerm = model.getSearchTerm();
                keywordType = model.getKeywordType();
                accountId = model.getAccountId();
                profileId = model.getProfileId();
                impressions = model.getImpressions();
                clicks = model.getClicks();
                purchases1d = model.getPurchases1d();
                sales1d = model.getSales1d();
                cost = model.getCost();

                data.setValue(searchTerm);
                data.setUniqueSign(String.join("-", accountId, String.valueOf(profileId), campaignId, adGroupId, keywordId, searchTerm));

                if (searchTerm != null && searchTerm.startsWith("b0") && searchTerm.length() == 10) {
                    data.setSearchTermType(2);
                } else {
                    data.setSearchTermType(1);
                }
            } else {
                continue;//忽略不支持的类型
            }

            //其他字段
            data.setCampaignName(campaignName);
            data.setAdGroupName(adGroupName);
            data.setKeywordType(keywordType);
            data.setSales1d(sales1d != null ? sales1d : 0.0);
            data.setCost(cost != null ? cost : 0.0);
            data.setImpressions(impressions);
            data.setClicks(clicks);
            data.setPurchases1d(purchases1d);
            data.setBelongsNum(1);

            //ACOS
            if (acosType != null && (acosType == 7 || acosType == 8)) {
                data.setAcos(0.0);
            } else {
                data.setAcos(calcRate(cost, sales1d));
            }
            // CTR
            data.setClickThroughRate(calcRate(clicks, impressions));
            // CVR
            data.setConversionRate(calcRate(purchases1d, clicks));
            // CPC
            data.setCostPerClick(calcRate(cost, clicks));
            //asin、picture
            if (campaignId != null && adGroupId != null) {
                String asinKey = campaignId + "-" + adGroupId;
                AdsDailyAdvertiserReportModel adv = resultMap.get(asinKey);
                if (adv != null) {
                    data.setAsin(adv.getAdvertisedAsin());
                    data.setPicture(adv.getPictureUrl());
                } else {
                    data.setAsin("");
                    data.setPicture("");
                }
            }
            result.add(data);
        }
        return result;
    }

    private static double calcRate(Number numerator, Number denominator) {
        if (numerator == null || denominator == null || denominator.doubleValue() == 0.0) return 0.0;
        return BigDecimal.valueOf(numerator.doubleValue())
                .divide(BigDecimal.valueOf(denominator.doubleValue()), 10, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();
    }

}
