package com.yiyitech.ads.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.EnumMap;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName EncodeImgZxing.java
 * @Description
 * @createTime 2025年07月07日 16:37:00
 */
@Component
public final class EncodeImgUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(EncodeImgUtil.class);
    //二维码颜色
    private static final int BLACK = 0xFF000000;//0xFFFF0000，红色
    //二维码背景色
    private static final int WHITE = 0xFFFFFFFF;//0xFF0000FF，蓝色
    //注：二维码颜色色差大，扫描快，但如果"BLACK'设置为黑色外其他颜色，可能无法扫描
    //二维码图片宽度
    private static final int width = 300;
    //二维码图片高度
    private static final int height = 300;
    //二维码格式参数
    private static final EnumMap<EncodeHintType, Object> hints = new EnumMap<EncodeHintType, Object>(EncodeHintType.class);

    static {
        /*二维码的纠错级别(排错率),4个级别：
         L (7%)、
         M (15%)、
         Q (25%)、
         H (30%)(最高H)
         纠错信息同样存储在二维码中，纠错级别越高，纠错信息占用的空间越多，那么能存储的有用讯息就越少；共有四级；
         选择M，扫描速度快。
         */
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        // 二维码边界空白大小 1,2,3,4 (4为默认,最大)
        hints.put(EncodeHintType.MARGIN, 1);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MAX_SIZE, 350);
        hints.put(EncodeHintType.MIN_SIZE, 150);
    }
    /**
     * 绘制二维码
     * @param contents 二维码内容
     * @return image 二维码图片
     * */
    public static InputStream encodeImg(String contents){
        BufferedImage image = null;
        InputStream is = null;
        try{
            BitMatrix matrix = new MultiFormatWriter().encode(contents, BarcodeFormat.QR_CODE, width, height, hints);
            image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            int width = matrix.getWidth();
            int height = matrix.getHeight();
            for(int x = 0; x < width; x++){
                for(int y =0;y < height; y++){
                    image.setRGB(x, y, matrix.get(x, y) ? BLACK : WHITE);
                }
            }
            ByteArrayOutputStream bs = new ByteArrayOutputStream();
            ImageOutputStream imOut;
            imOut = ImageIO.createImageOutputStream(bs);
            ImageIO.write(image, "jpg", imOut);
            is = new ByteArrayInputStream(bs.toByteArray());
        }catch(Exception e){
            LOGGER.error("生成二维码失败"+e.getMessage());
        }

        return is;
    }

    /**
     * 二维码绘制logo
     *
     * @param twodimensioncodeImg 二维码图片文件
     * @param logoImgUrl             logo图片文件
     */
    public static InputStream encodeImgLogo(InputStream twodimensioncodeImg, String logoImgUrl) {
        InputStream userQrCodeIamge = null;
        BufferedImage twodimensioncode = null;
        try {
            //读取二维码图片
            //JPEGImageDecoder qrCodeFile = JPEGCodec.createJPEGDecoder(twodimensioncodeImg);
            twodimensioncode = ImageIO.read(twodimensioncodeImg);
            //获取画笔
            Graphics2D g = twodimensioncode.createGraphics();
            //获取远程logo
            URL url = new URL(logoImgUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5 * 1000);
            InputStream inStream = conn.getInputStream();// 通过输入流获取图片数据
            //JPEGImageDecoder LogFile = JPEGCodec.createJPEGDecoder(inStream);
            //读取logo图片
            BufferedImage logo = ImageIO.read(inStream);
            //设置二维码大小，太大，会覆盖二维码，此处20%
            int logoWidth = logo.getWidth(null) > twodimensioncode.getWidth() * 2 / 10 ? (twodimensioncode.getWidth() * 2 / 10) : logo.getWidth(null);
            int logoHeight = logo.getHeight(null) > twodimensioncode.getHeight() * 2 / 10 ? (twodimensioncode.getHeight() * 2 / 10) : logo.getHeight(null);
            //设置logo图片放置位置
            //中心
            int x = (twodimensioncode.getWidth() - logoWidth) / 2;
            int y = (twodimensioncode.getHeight() - logoHeight) / 2;
            //开始合并绘制图片
            g.drawImage(logo, x, y, logoWidth, logoHeight, null);
            g.drawRoundRect(x, y, logoWidth, logoHeight, 15, 15);
            //logo边框大小
            g.setStroke(new BasicStroke(2));
            //logo边框颜色
            g.setColor(Color.WHITE);
            g.drawRect(x, y, logoWidth, logoHeight);
            g.dispose();
            logo.flush();
            twodimensioncode.flush();
            ByteArrayOutputStream bs = new ByteArrayOutputStream();
            ImageOutputStream imOut = ImageIO.createImageOutputStream(bs);
            ImageIO.write(twodimensioncode, "png", imOut);
            userQrCodeIamge = new ByteArrayInputStream(bs.toByteArray());
        } catch (Exception e) {
            LOGGER.error("二维码绘制logo失败"+e.getMessage());
        }
        return userQrCodeIamge;
    }

    /**
     * 二维码输出到文件
     *
     * @param contents 二维码内容
     * @param format   图片格式
     * @param file     输出文件
     */
    /*public static void writeToFile(String contents, String format, File file) {
        BufferedImage image = encodeImg(contents);
        try {
            ImageIO.write(image, format, file);
        } catch (IOException e) {
            LOGGER.error("二维码写入文件失败" + e.getMessage());
        }
    }*/

    /*public static void main(String[] args) throws Exception {
        InputStream inputStream = encodeImgLogo(encodeImg("http://www.baidu.com"),"http://wx.qlogo.cn/mmhead/ver_1/5ksX7z08FSOn4Md3NPpk6B4iaicHWtCvlbiccAI5hp4dyXZUJBICoFade6aEePTsuYdsLSINlnVy211IrTwUQZPlg/132");
        BufferedImage image = ImageIO.read(inputStream);
        ImageIO.write(image, "jpg", new File("D:\\haibao\\2222.jpg"));
    }*/

    /**
     * 二维码流式输出
     *  @param contents 二维码内容
     * @param format 图片格式
     * @param stream 输出流
     * */
//    public static void writeToStream(String contents, String format, OutputStream stream){
//        BufferedImage image = encodeImg(contents);
//        try {
//            ImageIO.write(image, format, stream);
//        } catch (IOException e) {
//            LOGGER.error("二维码写入流失败"+e.getMessage());
//        }
//    }
}
