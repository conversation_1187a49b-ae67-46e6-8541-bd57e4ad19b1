package com.yiyitech.ads.util;

import com.yiyitech.ads.vo.AdsAsinDataComparisonVO;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsDataAggregatorUtil.java
 * @Description
 * @createTime 2025年07月09日 16:34:00
 */
public class AdsDataComparisonAggregatorUtil {

    public static <T> AdsAsinDataComparisonVO aggregate(
            List<T> dataList,
            Function<T, Date> getDate,
            Function<T, Integer> getClicks,
            Function<T, Integer> getPurchases1d) {

        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);

        return new AdsAsinDataComparisonVO()
                .setLast7(aggregateClicksAndPurchases(dataList, getDate, getClicks, getPurchases1d, yesterday.minusDays(6), yesterday))
                .setLast30(aggregateClicksAndPurchases(dataList, getDate, getClicks, getPurchases1d, yesterday.minusDays(29), yesterday))
                .setLast65(aggregateClicksAndPurchases(dataList, getDate, getClicks, getPurchases1d, yesterday.minusDays(64), yesterday))
                .setAll(aggregateClicksAndPurchases(dataList, getDate, getClicks, getPurchases1d, null, null));
    }

    private static <T> AdsAsinDataComparisonVO.Info aggregateClicksAndPurchases(
            List<T> dataList,
            Function<T, Date> getDate,
            Function<T, Integer> getClicks,
            Function<T, Integer> getPurchases1d,
            LocalDate from,
            LocalDate to) {
        ZoneId zone = ZoneId.systemDefault();
        List<T> filtered = dataList.stream()
                .filter(item -> {
                    Date date = getDate.apply(item);
                    if (date == null) return false;
                    LocalDate localDate = date.toInstant().atZone(zone).toLocalDate();
                    boolean afterFrom = from == null || !localDate.isBefore(from);
                    boolean beforeTo = to == null || !localDate.isAfter(to);
                    return afterFrom && beforeTo;
                })
                .collect(Collectors.toList());
        int totalClicks = filtered.stream()
                .mapToInt(item -> Optional.ofNullable(getClicks.apply(item)).orElse(0))
                .sum();
        int totalPurchases = filtered.stream()
                .mapToInt(item -> Optional.ofNullable(getPurchases1d.apply(item)).orElse(0))
                .sum();
        return new AdsAsinDataComparisonVO.Info()
                .setClicks(totalClicks)
                .setPurchases1d(totalPurchases);
    }
}
