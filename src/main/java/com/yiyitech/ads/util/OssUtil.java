package com.yiyitech.ads.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsReportDownloadUtil.java
 * @Description
 * @createTime 2023年12月27日 16:56:00
 */
@Slf4j
@Component
public class OssUtil {
    @Value("${oss.endpoint}")
    private String endpoint;
    @Value("${oss.access_key_id}")
    private String accessKeyId;
    @Value("${oss.access_key_secret}")
    private String accessKeySecret;
    @Value("${oss.bucket_name}")
    private String bucketName;
    @Value("${oss.folder}")
    private String folder;
    private OSS ossClient;

    //初始化OSS客户端
    public void openOss() {
        ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }

    //关闭OSS客户端
    public void closeOss() {
        ossClient.shutdown();
    }

    //下载文件并上传oss
    public void downloadFileToOss(String fileName, String sourceUrl) throws Exception{
        try {
            URL url = new URL(sourceUrl);
            URLConnection connection = url.openConnection();
            InputStream inputStream = connection.getInputStream();
            ossClient.putObject(bucketName, folder.concat(fileName), inputStream);
//            log.info(fileName + ": 下载上传oss success");
        } catch (Exception e) {
            log.info(fileName + ": 下载上传oss failure：" + e.getMessage());
            throw e;
        }
//        finally {
//            ossClient.shutdown();
//        }
    }

//    //上传文件到oss并生成url
//    public void ossUpload(String fileName, String sourceUrl, File file) {
//        try {
//            ossClient.putObject(bucketName, gzFolder.concat(fileName), file);
//            Date expiration = new Date(System.currentTimeMillis() + 3600L * 1000);
//            URL signedUrl = ossClient.generatePresignedUrl(bucketName, fileName, expiration);
//            log.info(fileName + ": ossUpload success");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
}
