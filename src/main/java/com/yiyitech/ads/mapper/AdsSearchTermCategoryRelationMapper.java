package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yiyitech.ads.model.AdsSearchTermCategoryRelationModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 搜索词类目关联Mapper接口
 * 用于搜索词与商品类目关联关系的数据库操作
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermCategoryRelationMapper.java
 * @Description 搜索词类目关联Mapper接口
 * @createTime 2025年01月31日
 */
@Mapper
public interface AdsSearchTermCategoryRelationMapper extends BaseMapper<AdsSearchTermCategoryRelationModel> {

    /**
     * 根据搜索词和类目查找关联数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @param categoryId 类目ID
     * @return 关联数据
     */
    AdsSearchTermCategoryRelationModel findBySearchTermAndCategory(@Param("accountId") String accountId,
                                                                  @Param("profileId") Long profileId,
                                                                  @Param("searchTerm") String searchTerm,
                                                                  @Param("categoryId") Long categoryId);

    /**
     * 获取搜索词关联的前N个类目（按点击量排序）
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @param limit 返回数量限制
     * @return 类目关联列表
     */
    List<AdsSearchTermCategoryRelationModel> getTopCategoriesBySearchTerm(@Param("accountId") String accountId,
                                                                          @Param("profileId") Long profileId,
                                                                          @Param("searchTerm") String searchTerm,
                                                                          @Param("limit") Integer limit);

    /**
     * 获取类目关联的前N个搜索词（按点击量排序）
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param categoryId 类目ID
     * @param limit 返回数量限制
     * @return 搜索词关联列表
     */
    List<AdsSearchTermCategoryRelationModel> getTopSearchTermsByCategory(@Param("accountId") String accountId,
                                                                         @Param("profileId") Long profileId,
                                                                         @Param("categoryId") Long categoryId,
                                                                         @Param("limit") Integer limit);

    /**
     * 获取所有搜索词的类目分布
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @return 类目分布统计
     */
    List<Map<String, Object>> getCategoryDistribution(@Param("accountId") String accountId,
                                                      @Param("profileId") Long profileId);

    /**
     * 获取类目的搜索词分布
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param categoryId 类目ID
     * @return 搜索词分布统计
     */
    List<Map<String, Object>> getSearchTermDistributionByCategory(@Param("accountId") String accountId,
                                                                 @Param("profileId") Long profileId,
                                                                 @Param("categoryId") Long categoryId);

    /**
     * 批量插入类目关联数据
     * 
     * @param dataList 数据列表
     * @return 插入数量
     */
    int batchInsert(@Param("dataList") List<AdsSearchTermCategoryRelationModel> dataList);

    /**
     * 批量更新类目关联数据
     * 
     * @param dataList 数据列表
     * @return 更新数量
     */
    int batchUpdate(@Param("dataList") List<AdsSearchTermCategoryRelationModel> dataList);

    /**
     * 获取热门类目（按总点击量排序）
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param limit 返回数量限制
     * @return 热门类目列表
     */
    List<Map<String, Object>> getTopCategories(@Param("accountId") String accountId,
                                              @Param("profileId") Long profileId,
                                              @Param("limit") Integer limit);

    /**
     * 获取类目的关联强度统计
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param categoryId 类目ID
     * @return 关联强度统计
     */
    Map<String, Object> getCategoryRelevanceStatistics(@Param("accountId") String accountId,
                                                       @Param("profileId") Long profileId,
                                                       @Param("categoryId") Long categoryId);

    /**
     * 获取搜索词的类目多样性指标
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 多样性指标
     */
    Map<String, Object> getSearchTermCategoryDiversity(@Param("accountId") String accountId,
                                                       @Param("profileId") Long profileId,
                                                       @Param("searchTerm") String searchTerm);

    /**
     * 获取跨类目的搜索词（关联多个类目的搜索词）
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param minCategoryCount 最小类目数量
     * @param limit 返回数量限制
     * @return 跨类目搜索词列表
     */
    List<Map<String, Object>> getCrossCategorySearchTerms(@Param("accountId") String accountId,
                                                          @Param("profileId") Long profileId,
                                                          @Param("minCategoryCount") Integer minCategoryCount,
                                                          @Param("limit") Integer limit);

    /**
     * 获取类目竞争分析数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param categoryId 类目ID
     * @return 竞争分析数据
     */
    Map<String, Object> getCategoryCompetitionAnalysis(@Param("accountId") String accountId,
                                                       @Param("profileId") Long profileId,
                                                       @Param("categoryId") Long categoryId);

    /**
     * 获取类目趋势数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param categoryId 类目ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 趋势数据
     */
    List<Map<String, Object>> getCategoryTrendData(@Param("accountId") String accountId,
                                                   @Param("profileId") Long profileId,
                                                   @Param("categoryId") Long categoryId,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);

    /**
     * 删除过期的关联数据
     * 
     * @param beforeDate 删除此日期之前的数据
     * @return 删除数量
     */
    int deleteExpiredData(@Param("beforeDate") String beforeDate);

    /**
     * 更新类目关联的排名
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     */
    void updateCategoryRankings(@Param("accountId") String accountId,
                               @Param("profileId") Long profileId,
                               @Param("searchTerm") String searchTerm);

    /**
     * 获取类目层级分布
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @return 层级分布统计
     */
    List<Map<String, Object>> getCategoryLevelDistribution(@Param("accountId") String accountId,
                                                           @Param("profileId") Long profileId);

    /**
     * 获取父类目下的子类目关联数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param parentCategoryId 父类目ID
     * @return 子类目关联数据
     */
    List<AdsSearchTermCategoryRelationModel> getSubCategoryRelations(@Param("accountId") String accountId,
                                                                     @Param("profileId") Long profileId,
                                                                     @Param("parentCategoryId") Long parentCategoryId);
}
