package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsNegativeLibraryModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsNegativeLibraryMapper.java
 * @Description
 * @createTime 2025年07月08日 17:03:00
 */
public interface AdsNegativeLibraryMapper extends AbstractBaseMapper<AdsNegativeLibraryModel> {

//    default List<AdsNegativeLibraryModel> searchByAccountIdByProfileId(String accountId, Long profileId) {
//        QueryWrapper<AdsNegativeLibraryModel> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("account_id", accountId);
//        queryWrapper.eq("profile_id", profileId);
//        return selectList(queryWrapper);
//    }
}
