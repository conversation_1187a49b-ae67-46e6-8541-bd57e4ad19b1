package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsConditionModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsConditionMapper.java
 * @Description
 * @createTime 2025年07月02日 23:59:00
 */
public interface AdsConditionMapper extends AbstractBaseMapper<AdsConditionModel> {

    default List<AdsConditionModel> selectByGroupIds(List<Long> groupIds) {
        QueryWrapper<AdsConditionModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("group_id", groupIds);
        queryWrapper.eq("status", 1);
        return selectList(queryWrapper);
    }

    default List<AdsConditionModel> searchByGroupIdsByStatus(List<Long> groupIds, Boolean status) {
        QueryWrapper<AdsConditionModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("group_id", groupIds);
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }
}
