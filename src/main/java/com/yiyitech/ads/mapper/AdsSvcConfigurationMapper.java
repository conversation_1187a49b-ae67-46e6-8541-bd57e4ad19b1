package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsSvcConfigurationModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsSvcConfigurationMapper.java
 * @Description
 * @createTime 2025年06月27日 14:27:00
 */
public interface AdsSvcConfigurationMapper extends AbstractBaseMapper<AdsSvcConfigurationModel> {

    default List<AdsSvcConfigurationModel> selectByAccountIdByproId(String accountId, Long profileId) {
        QueryWrapper<AdsSvcConfigurationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_id", accountId);
        queryWrapper.eq("profile_id", profileId);
        return selectList(queryWrapper);
    }

    default AdsSvcConfigurationModel getByAccountIdByproId(String accountId, Long profileId) {
        List<AdsSvcConfigurationModel> list = selectList(
                new QueryWrapper<AdsSvcConfigurationModel>()
                        .eq("account_id", accountId)
                        .eq("profile_id", profileId)
        );
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    default List<AdsSvcConfigurationModel> searchByStatus(Boolean status) {
        QueryWrapper<AdsSvcConfigurationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }
}
