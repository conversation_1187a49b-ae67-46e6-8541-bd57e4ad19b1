package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsRoleModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsRoleMapper.java
 * @Description
 * @createTime 2025年06月20日 17:46:00
 */
public interface AdsRoleMapper extends AbstractBaseMapper<AdsRoleModel> {

    default List<AdsRoleModel> searchByIds(List<Long> Ids) {
        if (Ids == null || Ids.isEmpty()) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsRoleModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", Ids);
        return selectList(queryWrapper);
    }
}
