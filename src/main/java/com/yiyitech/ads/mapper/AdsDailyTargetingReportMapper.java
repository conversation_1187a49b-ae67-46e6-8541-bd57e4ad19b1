package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.enums.AdsCommonStatusEnum;
import com.yiyitech.ads.model.AdsAccountProfileModel;
import com.yiyitech.ads.model.AdsDailyTargetingReportModel;
import com.yiyitech.ads.util.QueryWrapperUtil;
import com.yiyitech.support.mybatis.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsDailyTargetingReportMapper.java
 * @Description
 * @createTime 2023年12月19日 17:28:00
 */
public interface AdsDailyTargetingReportMapper extends AbstractBaseMapper<AdsDailyTargetingReportModel> {

    default List<AdsDailyTargetingReportModel> searchDailyTargetingReportModel(List<String> accountId, List<String> profileId, String startDate, String endDate, String campaignName, String adGroupName, String keyword) {
        QueryWrapper<AdsDailyTargetingReportModel> queryWrapper = new QueryWrapper<>();
        if (ObjectUtils.isNotEmpty(accountId)) {
            queryWrapper.in("account_id", accountId);
        }
        if (ObjectUtils.isNotEmpty(profileId)) {
            queryWrapper.in("profile_id", profileId);
        }
        if (ObjectUtils.isNotEmpty(campaignName)) {
            queryWrapper.like("campaign_name", campaignName);
        }
        if (ObjectUtils.isNotEmpty(adGroupName)) {
            queryWrapper.like("ad_group_name", adGroupName);
        }
        if (ObjectUtils.isNotEmpty(keyword)) {
            queryWrapper.like("keyword", keyword);
        }
        queryWrapper.between("date", startDate, endDate).orderByDesc("id");
        return selectList(queryWrapper);
    }

    default List<AdsDailyTargetingReportModel> searchLstByUniqueSign(List<String> uniqueSignLst) {
        if (uniqueSignLst == null || uniqueSignLst.isEmpty()) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsDailyTargetingReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendSearchTermCondition(queryWrapper, uniqueSignLst,false);
        return selectList(queryWrapper);
    }

    default List<AdsDailyTargetingReportModel> searchByProLstByCampByGroupByStatusByDate(List<AdsAccountProfileModel> profileLst, List<String> campaignIds,
                                                                                         List<String> adGroupIds, Integer status, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(profileLst)) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsDailyTargetingReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendAccountProfileCondition(queryWrapper, profileLst);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            queryWrapper.in("campaign_id", campaignIds);
        }
        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            queryWrapper.in("ad_group_id", adGroupIds);
        }
        if (null != status) {
            if (AdsCommonStatusEnum.EXCEPT_ARCHIVED.getCode() == status) {
                queryWrapper.in("campaign_status", AdsCommonStatusEnum.getExceptArchivedDescList());
            } else {
                queryWrapper.eq("campaign_status", AdsCommonStatusEnum.of(status).getDesc());
            }
        }
        queryWrapper.between("date", startDate, endDate).orderByDesc("id");
        return selectList(queryWrapper);
    }

    default List<AdsDailyTargetingReportModel> searchByUniqueSignByDate(String uniqueSign, String startDate, String endDate) {
        QueryWrapper<AdsDailyTargetingReportModel> queryWrapper = new QueryWrapper<>();
        QueryWrapperUtil.appendSearchTermCondition(queryWrapper, Arrays.asList(uniqueSign),true);
        queryWrapper.between("date", startDate, endDate).orderByDesc("id");
        return selectList(queryWrapper);
    }

    default int deleteByProfileIdAndDate(String profileId, String date) {
        QueryWrapper<AdsDailyTargetingReportModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("profile_id", profileId).eq("date", date);
        return delete(queryWrapper);
    }
}
