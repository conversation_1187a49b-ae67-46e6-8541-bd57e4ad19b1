package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsAdGroupModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsAdGroupMapper.java
 * @Description
 * @createTime 2023年12月22日 13:24:00
 */
public interface AdsAdGroupMapper extends AbstractBaseMapper<AdsAdGroupModel> {

//    default List<AdsAdGroupModel> searchByCampaignIds(List<String> campaignIdLst) {
//        QueryWrapper<AdsAdGroupModel> queryWrapper = new QueryWrapper<>();
//        queryWrapper.in("campaign_id", campaignIdLst)
//                .eq("state", CommonConstants.ENABLED_STATE);
//        return selectList(queryWrapper);
//    }

    default List<AdsAdGroupModel> searchByCampaignIds(List<String> campaignIdLst) {
        QueryWrapper<AdsAdGroupModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("campaign_id", campaignIdLst);
        return selectList(queryWrapper);
    }

    default List<AdsAdGroupModel> searchByAccIdByProIdByAdName(List<String> accountId, List<String> profileId, String adGroupName) {
        QueryWrapper<AdsAdGroupModel> queryWrapper = new QueryWrapper<>();
        if (ObjectUtils.isNotEmpty(accountId)) {
            queryWrapper.in("account_id", accountId);
        }
        if (ObjectUtils.isNotEmpty(profileId)) {
            queryWrapper.in("profile_id", profileId);
        }
        if (ObjectUtils.isNotEmpty(adGroupName)) {
            queryWrapper.like("name", adGroupName);
        }
        return selectList(queryWrapper);
    }

}
