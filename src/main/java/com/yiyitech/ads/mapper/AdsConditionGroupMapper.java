package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsConditionGroupModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsConditionGroupMapper.java
 * @Description
 * @createTime 2025年07月02日 23:59:00
 */
public interface AdsConditionGroupMapper extends AbstractBaseMapper<AdsConditionGroupModel> {

    default List<AdsConditionGroupModel> selectByRuleIds(List<Long> ruleIds) {
        QueryWrapper<AdsConditionGroupModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("rule_id", ruleIds);
        queryWrapper.eq("status", 1);
        return selectList(queryWrapper);
    }

    default List<AdsConditionGroupModel> searchByRuleIdsByStatus(List<Long> rulesIds, Boolean status) {
        QueryWrapper<AdsConditionGroupModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("rule_id", rulesIds);
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }
}
