package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yiyitech.ads.model.AdsSearchTermAnalyticsModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 搜索词分析数据Mapper接口
 * 用于搜索词分析数据的数据库操作
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermAnalyticsMapper.java
 * @Description 搜索词分析数据Mapper接口
 * @createTime 2025年01月31日
 */
@Mapper
public interface AdsSearchTermAnalyticsMapper extends BaseMapper<AdsSearchTermAnalyticsModel> {

    /**
     * 根据搜索词和日期查找数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @param reportDate 报告日期
     * @return 搜索词分析数据
     */
    AdsSearchTermAnalyticsModel findBySearchTermAndDate(@Param("accountId") String accountId,
                                                       @Param("profileId") Long profileId,
                                                       @Param("searchTerm") String searchTerm,
                                                       @Param("reportDate") Date reportDate);

    /**
     * 根据条件查询搜索词分析数据列表
     * 
     * @param params 查询参数
     * @return 搜索词分析数据列表
     */
    List<AdsSearchTermAnalyticsModel> selectByConditions(@Param("params") Map<String, Object> params);

    /**
     * 根据条件统计数据数量
     * 
     * @param params 查询参数
     * @return 数据数量
     */
    int countByConditions(@Param("params") Map<String, Object> params);

    /**
     * 获取指定账户的搜索词列表
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @return 搜索词列表
     */
    List<String> getSearchTermsByAccount(@Param("accountId") String accountId,
                                        @Param("profileId") Long profileId);

    /**
     * 获取最新的搜索词数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 最新的搜索词分析数据
     */
    AdsSearchTermAnalyticsModel getLatestBySearchTerm(@Param("accountId") String accountId,
                                                     @Param("profileId") Long profileId,
                                                     @Param("searchTerm") String searchTerm);

    /**
     * 按搜索量获取热门搜索词
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param limit 返回数量限制
     * @return 热门搜索词列表
     */
    List<AdsSearchTermAnalyticsModel> getTopSearchTermsByVolume(@Param("accountId") String accountId,
                                                               @Param("profileId") Long profileId,
                                                               @Param("limit") Integer limit);

    /**
     * 获取搜索词趋势数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 趋势数据列表
     */
    List<AdsSearchTermAnalyticsModel> getSearchTermTrends(@Param("accountId") String accountId,
                                                         @Param("profileId") Long profileId,
                                                         @Param("searchTerm") String searchTerm,
                                                         @Param("startDate") String startDate,
                                                         @Param("endDate") String endDate);

    /**
     * 获取相关搜索词
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param seedKeyword 种子关键词
     * @param limit 返回数量限制
     * @return 相关搜索词列表
     */
    List<String> getRelatedSearchTerms(@Param("accountId") String accountId,
                                      @Param("profileId") Long profileId,
                                      @Param("seedKeyword") String seedKeyword,
                                      @Param("limit") Integer limit);

    /**
     * 批量插入搜索词分析数据
     * 
     * @param dataList 数据列表
     * @return 插入数量
     */
    int batchInsert(@Param("dataList") List<AdsSearchTermAnalyticsModel> dataList);

    /**
     * 批量更新搜索词分析数据
     * 
     * @param dataList 数据列表
     * @return 更新数量
     */
    int batchUpdate(@Param("dataList") List<AdsSearchTermAnalyticsModel> dataList);

    /**
     * 删除过期数据
     * 
     * @param beforeDate 删除此日期之前的数据
     * @return 删除数量
     */
    int deleteExpiredData(@Param("beforeDate") Date beforeDate);

    /**
     * 获取搜索词统计信息
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    Map<String, Object> getSearchTermStatistics(@Param("accountId") String accountId,
                                               @Param("profileId") Long profileId,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate);

    /**
     * 获取高增长搜索词
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param minGrowthRate 最小增长率
     * @param limit 返回数量限制
     * @return 高增长搜索词列表
     */
    List<AdsSearchTermAnalyticsModel> getHighGrowthSearchTerms(@Param("accountId") String accountId,
                                                              @Param("profileId") Long profileId,
                                                              @Param("minGrowthRate") Double minGrowthRate,
                                                              @Param("limit") Integer limit);

    /**
     * 获取低竞争高搜索量的搜索词
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param minSearchVolume 最小搜索量
     * @param maxCompetitionLevel 最大竞争等级
     * @param limit 返回数量限制
     * @return 机会搜索词列表
     */
    List<AdsSearchTermAnalyticsModel> getOpportunitySearchTerms(@Param("accountId") String accountId,
                                                               @Param("profileId") Long profileId,
                                                               @Param("minSearchVolume") Long minSearchVolume,
                                                               @Param("maxCompetitionLevel") Integer maxCompetitionLevel,
                                                               @Param("limit") Integer limit);

    /**
     * 按类目统计搜索词数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param categoryId 类目ID
     * @return 类目搜索词统计
     */
    Map<String, Object> getSearchTermStatisticsByCategory(@Param("accountId") String accountId,
                                                         @Param("profileId") Long profileId,
                                                         @Param("categoryId") Long categoryId);

    /**
     * 获取搜索词的季节性数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @param months 月份数量
     * @return 季节性数据
     */
    List<Map<String, Object>> getSearchTermSeasonalityData(@Param("accountId") String accountId,
                                                          @Param("profileId") Long profileId,
                                                          @Param("searchTerm") String searchTerm,
                                                          @Param("months") Integer months);
}
