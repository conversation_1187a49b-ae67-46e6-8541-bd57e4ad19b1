package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsPermissionModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsPermissionMapper.java
 * @Description
 * @createTime 2025年06月20日 17:49:00
 */
public interface AdsPermissionMapper extends AbstractBaseMapper<AdsPermissionModel> {

    default List<AdsPermissionModel> searchByIds(List<Long> Ids) {
        if (Ids == null || Ids.isEmpty()) {
            return Collections.emptyList();
        }
        QueryWrapper<AdsPermissionModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", Ids);
        return selectList(queryWrapper);
    }
}
