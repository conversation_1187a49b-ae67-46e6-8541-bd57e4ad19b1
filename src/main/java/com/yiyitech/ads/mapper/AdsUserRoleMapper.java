package com.yiyitech.ads.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yiyitech.ads.model.AdsUserRoleModel;
import com.yiyitech.support.mybatis.AbstractBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AdsUserRoleMapper.java
 * @Description
 * @createTime 2025年06月20日 16:13:00
 */
public interface AdsUserRoleMapper extends AbstractBaseMapper<AdsUserRoleModel> {

    default List<AdsUserRoleModel> searchByUserId(Long userId) {
        QueryWrapper<AdsUserRoleModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return selectList(queryWrapper);
    }


}
