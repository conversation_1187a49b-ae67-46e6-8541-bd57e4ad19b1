package com.yiyitech.ads.service;

import com.yiyitech.ads.mapper.AdsAccountInfoMapper;
import com.yiyitech.ads.mapper.AdsSearchTermAnalyticsMapper;
import com.yiyitech.ads.mapper.AdsSearchTermCategoryRelationMapper;
import com.yiyitech.ads.mapper.AdsSearchTermProductRankingMapper;
import com.yiyitech.ads.model.AdsAccountInfoModel;
import com.yiyitech.ads.model.AdsSearchTermAnalyticsModel;
import com.yiyitech.ads.model.AdsSearchTermCategoryRelationModel;
import com.yiyitech.ads.model.AdsSearchTermProductRankingModel;
import com.yiyitech.ads.service.impl.AdsSearchTermAnalyticsServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 搜索词分析服务测试类
 * 测试搜索词分析相关的核心功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermAnalyticsServiceTest.java
 * @Description 搜索词分析服务单元测试
 * @createTime 2025年01月31日
 */
@ExtendWith(MockitoExtension.class)
public class AdsSearchTermAnalyticsServiceTest {

    @Mock
    private AdsSearchTermAnalyticsMapper searchTermAnalyticsMapper;

    @Mock
    private AdsSearchTermCategoryRelationMapper categoryRelationMapper;

    @Mock
    private AdsSearchTermProductRankingMapper productRankingMapper;

    @Mock
    private AdsAccountInfoMapper accountInfoMapper;

    @InjectMocks
    private AdsSearchTermAnalyticsServiceImpl searchTermAnalyticsService;

    private AdsAccountInfoModel testAccount;
    private AdsSearchTermAnalyticsModel testAnalytics;
    private AdsSearchTermCategoryRelationModel testCategoryRelation;
    private AdsSearchTermProductRankingModel testProductRanking;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testAccount = new AdsAccountInfoModel();
        testAccount.setAccountId("TEST_ACCOUNT");
        testAccount.setRefreshTokenSp("test_refresh_token");

        testAnalytics = new AdsSearchTermAnalyticsModel();
        testAnalytics.setId(1L);
        testAnalytics.setAccountId("TEST_ACCOUNT");
        testAnalytics.setProfileId(123L);
        testAnalytics.setSearchTerm("wireless headphones");
        testAnalytics.setMonthlySearchVolume(50000L);
        testAnalytics.setClicks(1200);
        testAnalytics.setImpressions(15000);
        testAnalytics.setSearchRank(5);

        testCategoryRelation = new AdsSearchTermCategoryRelationModel();
        testCategoryRelation.setId(1L);
        testCategoryRelation.setAccountId("TEST_ACCOUNT");
        testCategoryRelation.setProfileId(123L);
        testCategoryRelation.setSearchTerm("wireless headphones");
        testCategoryRelation.setCategoryId(1001L);
        testCategoryRelation.setCategoryName("Electronics > Audio");
        testCategoryRelation.setClicksInCategory(800);
        testCategoryRelation.setClickRank(1);

        testProductRanking = new AdsSearchTermProductRankingModel();
        testProductRanking.setId(1L);
        testProductRanking.setAccountId("TEST_ACCOUNT");
        testProductRanking.setProfileId(123L);
        testProductRanking.setSearchTerm("wireless headphones");
        testProductRanking.setAsin("B08TEST123");
        testProductRanking.setProductTitle("Premium Wireless Headphones");
        testProductRanking.setBrand("TestBrand");
        testProductRanking.setPrice(99.99);
        testProductRanking.setClicks(500);
        testProductRanking.setClickRank(1);
    }

    @Test
    void testGetSearchTermAnalyticsList_Success() {
        // 准备测试数据
        List<AdsSearchTermAnalyticsModel> mockData = Arrays.asList(testAnalytics);
        Map<String, Object> params = new HashMap<>();
        params.put("accountId", "TEST_ACCOUNT");
        params.put("profileId", 123L);

        // 模拟Mapper调用
        when(searchTermAnalyticsMapper.selectByConditions(any())).thenReturn(mockData);
        when(searchTermAnalyticsMapper.countByConditions(any())).thenReturn(1);

        // 执行测试
        Map<String, Object> result = searchTermAnalyticsService.getSearchTermAnalyticsList(
                "TEST_ACCOUNT", 123L, null, null, null, 1, 10);

        // 验证结果
        assertTrue((Boolean) result.get("success"));
        assertEquals(1, result.get("totalCount"));
        assertNotNull(result.get("data"));

        // 验证Mapper调用
        verify(searchTermAnalyticsMapper).selectByConditions(any());
        verify(searchTermAnalyticsMapper).countByConditions(any());
    }

    @Test
    void testGetSearchTermCategories_Success() {
        // 准备测试数据
        List<AdsSearchTermCategoryRelationModel> mockCategories = Arrays.asList(testCategoryRelation);

        // 模拟Mapper调用
        when(categoryRelationMapper.getTopCategoriesBySearchTerm(
                "TEST_ACCOUNT", 123L, "wireless headphones", 3))
                .thenReturn(mockCategories);

        // 执行测试
        List<AdsSearchTermCategoryRelationModel> result = searchTermAnalyticsService.getSearchTermCategories(
                "TEST_ACCOUNT", 123L, "wireless headphones");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Electronics > Audio", result.get(0).getCategoryName());
        assertEquals(1, result.get(0).getClickRank().intValue());

        // 验证Mapper调用
        verify(categoryRelationMapper).getTopCategoriesBySearchTerm(
                "TEST_ACCOUNT", 123L, "wireless headphones", 3);
    }

    @Test
    void testGetSearchTermProductRankings_Success() {
        // 准备测试数据
        List<AdsSearchTermProductRankingModel> mockProducts = Arrays.asList(testProductRanking);

        // 模拟Mapper调用
        when(productRankingMapper.getTopProductsBySearchTerm(
                "TEST_ACCOUNT", 123L, "wireless headphones", 3))
                .thenReturn(mockProducts);

        // 执行测试
        List<AdsSearchTermProductRankingModel> result = searchTermAnalyticsService.getSearchTermProductRankings(
                "TEST_ACCOUNT", 123L, "wireless headphones");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("B08TEST123", result.get(0).getAsin());
        assertEquals("Premium Wireless Headphones", result.get(0).getProductTitle());
        assertEquals(1, result.get(0).getClickRank().intValue());

        // 验证Mapper调用
        verify(productRankingMapper).getTopProductsBySearchTerm(
                "TEST_ACCOUNT", 123L, "wireless headphones", 3);
    }

    @Test
    void testGetSearchTermDetails_Success() {
        // 准备测试数据
        List<AdsSearchTermCategoryRelationModel> mockCategories = Arrays.asList(testCategoryRelation);
        List<AdsSearchTermProductRankingModel> mockProducts = Arrays.asList(testProductRanking);

        // 模拟Mapper调用
        when(searchTermAnalyticsMapper.getLatestBySearchTerm(
                "TEST_ACCOUNT", 123L, "wireless headphones"))
                .thenReturn(testAnalytics);
        when(categoryRelationMapper.getTopCategoriesBySearchTerm(
                "TEST_ACCOUNT", 123L, "wireless headphones", 3))
                .thenReturn(mockCategories);
        when(productRankingMapper.getTopProductsBySearchTerm(
                "TEST_ACCOUNT", 123L, "wireless headphones", 3))
                .thenReturn(mockProducts);

        // 执行测试
        Map<String, Object> result = searchTermAnalyticsService.getSearchTermDetails(
                "TEST_ACCOUNT", 123L, "wireless headphones");

        // 验证结果
        assertTrue((Boolean) result.get("success"));
        assertNotNull(result.get("searchTermData"));
        assertNotNull(result.get("topCategories"));
        assertNotNull(result.get("topProducts"));

        // 验证搜索词数据
        AdsSearchTermAnalyticsModel searchTermData = (AdsSearchTermAnalyticsModel) result.get("searchTermData");
        assertEquals("wireless headphones", searchTermData.getSearchTerm());
        assertEquals(50000L, searchTermData.getMonthlySearchVolume().longValue());

        // 验证类目数据
        @SuppressWarnings("unchecked")
        List<AdsSearchTermCategoryRelationModel> categories = 
                (List<AdsSearchTermCategoryRelationModel>) result.get("topCategories");
        assertEquals(1, categories.size());
        assertEquals("Electronics > Audio", categories.get(0).getCategoryName());

        // 验证商品数据
        @SuppressWarnings("unchecked")
        List<AdsSearchTermProductRankingModel> products = 
                (List<AdsSearchTermProductRankingModel>) result.get("topProducts");
        assertEquals(1, products.size());
        assertEquals("B08TEST123", products.get(0).getAsin());
    }

    @Test
    void testGetTopSearchTerms_Success() {
        // 准备测试数据
        List<AdsSearchTermAnalyticsModel> mockTopTerms = Arrays.asList(testAnalytics);

        // 模拟Mapper调用
        when(searchTermAnalyticsMapper.getTopSearchTermsByVolume("TEST_ACCOUNT", 123L, 10))
                .thenReturn(mockTopTerms);

        // 执行测试
        List<AdsSearchTermAnalyticsModel> result = searchTermAnalyticsService.getTopSearchTerms(
                "TEST_ACCOUNT", 123L, 10);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("wireless headphones", result.get(0).getSearchTerm());
        assertEquals(50000L, result.get(0).getMonthlySearchVolume().longValue());

        // 验证Mapper调用
        verify(searchTermAnalyticsMapper).getTopSearchTermsByVolume("TEST_ACCOUNT", 123L, 10);
    }

    @Test
    void testBatchUpdateSearchTermAnalytics_Success() {
        // 准备测试数据
        List<AdsSearchTermAnalyticsModel> dataList = Arrays.asList(testAnalytics);

        // 执行测试
        boolean result = searchTermAnalyticsService.batchUpdateSearchTermAnalytics(dataList);

        // 验证结果
        assertTrue(result);

        // 验证Mapper调用
        verify(searchTermAnalyticsMapper).updateById(testAnalytics);
    }

    @Test
    void testBatchUpdateSearchTermAnalytics_Exception() {
        // 准备测试数据
        List<AdsSearchTermAnalyticsModel> dataList = Arrays.asList(testAnalytics);

        // 模拟异常
        doThrow(new RuntimeException("Database error")).when(searchTermAnalyticsMapper).updateById(any());

        // 执行测试
        boolean result = searchTermAnalyticsService.batchUpdateSearchTermAnalytics(dataList);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testGetSearchTermAnalyticsList_EmptyResult() {
        // 模拟空结果
        when(searchTermAnalyticsMapper.selectByConditions(any())).thenReturn(new ArrayList<>());
        when(searchTermAnalyticsMapper.countByConditions(any())).thenReturn(0);

        // 执行测试
        Map<String, Object> result = searchTermAnalyticsService.getSearchTermAnalyticsList(
                "TEST_ACCOUNT", 123L, null, null, null, 1, 10);

        // 验证结果
        assertTrue((Boolean) result.get("success"));
        assertEquals(0, result.get("totalCount"));
        assertNotNull(result.get("data"));
        assertTrue(((List<?>) result.get("data")).isEmpty());
    }

    @Test
    void testGetSearchTermCategories_EmptyResult() {
        // 模拟空结果
        when(categoryRelationMapper.getTopCategoriesBySearchTerm(any(), any(), any(), any()))
                .thenReturn(new ArrayList<>());

        // 执行测试
        List<AdsSearchTermCategoryRelationModel> result = searchTermAnalyticsService.getSearchTermCategories(
                "TEST_ACCOUNT", 123L, "nonexistent term");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetSearchTermProductRankings_EmptyResult() {
        // 模拟空结果
        when(productRankingMapper.getTopProductsBySearchTerm(any(), any(), any(), any()))
                .thenReturn(new ArrayList<>());

        // 执行测试
        List<AdsSearchTermProductRankingModel> result = searchTermAnalyticsService.getSearchTermProductRankings(
                "TEST_ACCOUNT", 123L, "nonexistent term");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
