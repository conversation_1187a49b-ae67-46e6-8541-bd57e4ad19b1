package com.yiyitech.ads.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyitech.ads.mapper.AdsSearchTermAnalyticsMapper;
import com.yiyitech.ads.mapper.AdsSearchTermCategoryRelationMapper;
import com.yiyitech.ads.mapper.AdsSearchTermProductRankingMapper;
import com.yiyitech.ads.model.AdsSearchTermAnalyticsModel;
import com.yiyitech.ads.model.AdsSearchTermCategoryRelationModel;
import com.yiyitech.ads.model.AdsSearchTermProductRankingModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.Date;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 搜索词分析功能集成测试
 * 测试完整的API调用流程和数据库交互
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName SearchTermAnalyticsIntegrationTest.java
 * @Description 搜索词分析集成测试类
 * @createTime 2025年01月31日
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class SearchTermAnalyticsIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private AdsSearchTermAnalyticsMapper searchTermAnalyticsMapper;

    @Autowired
    private AdsSearchTermCategoryRelationMapper categoryRelationMapper;

    @Autowired
    private AdsSearchTermProductRankingMapper productRankingMapper;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    private static final String TEST_ACCOUNT_ID = "TEST_ACCOUNT";
    private static final Long TEST_PROFILE_ID = 123L;
    private static final String TEST_MARKETPLACE_ID = "ATVPDKIKX0DER";
    private static final String TEST_SEARCH_TERM = "wireless headphones";

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 清理测试数据
        cleanupTestData();
        
        // 插入测试数据
        insertTestData();
    }

    /**
     * 测试获取搜索词分析数据列表API
     */
    @Test
    void testGetSearchTermAnalyticsList() throws Exception {
        mockMvc.perform(get("/api/v1/ads/search-term-analytics/list")
                .param("accountId", TEST_ACCOUNT_ID)
                .param("profileId", TEST_PROFILE_ID.toString())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.totalCount").exists())
                .andExpect(jsonPath("$.data").isArray());
    }

    /**
     * 测试获取搜索词详细信息API
     */
    @Test
    void testGetSearchTermDetails() throws Exception {
        mockMvc.perform(get("/api/v1/ads/search-term-analytics/details")
                .param("accountId", TEST_ACCOUNT_ID)
                .param("profileId", TEST_PROFILE_ID.toString())
                .param("searchTerm", TEST_SEARCH_TERM)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.searchTermData").exists())
                .andExpect(jsonPath("$.topCategories").isArray())
                .andExpect(jsonPath("$.topProducts").isArray());
    }

    /**
     * 测试获取搜索词关联类目API
     */
    @Test
    void testGetSearchTermCategories() throws Exception {
        mockMvc.perform(get("/api/v1/ads/search-term-analytics/categories")
                .param("accountId", TEST_ACCOUNT_ID)
                .param("profileId", TEST_PROFILE_ID.toString())
                .param("searchTerm", TEST_SEARCH_TERM)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.count").exists());
    }

    /**
     * 测试获取搜索词关联商品API
     */
    @Test
    void testGetSearchTermProducts() throws Exception {
        mockMvc.perform(get("/api/v1/ads/search-term-analytics/products")
                .param("accountId", TEST_ACCOUNT_ID)
                .param("profileId", TEST_PROFILE_ID.toString())
                .param("searchTerm", TEST_SEARCH_TERM)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.count").exists());
    }

    /**
     * 测试获取热门搜索词API
     */
    @Test
    void testGetTopSearchTerms() throws Exception {
        mockMvc.perform(get("/api/v1/ads/search-term-analytics/top-search-terms")
                .param("accountId", TEST_ACCOUNT_ID)
                .param("profileId", TEST_PROFILE_ID.toString())
                .param("limit", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.count").exists());
    }

    /**
     * 测试获取搜索词趋势数据API
     */
    @Test
    void testGetSearchTermTrends() throws Exception {
        mockMvc.perform(get("/api/v1/ads/search-term-analytics/trends")
                .param("accountId", TEST_ACCOUNT_ID)
                .param("profileId", TEST_PROFILE_ID.toString())
                .param("searchTerm", TEST_SEARCH_TERM)
                .param("startDate", "2025-01-01")
                .param("endDate", "2025-01-31")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试竞争分析API
     */
    @Test
    void testAnalyzeSearchTermCompetition() throws Exception {
        mockMvc.perform(get("/api/v1/ads/search-term-analytics/competition-analysis")
                .param("accountId", TEST_ACCOUNT_ID)
                .param("profileId", TEST_PROFILE_ID.toString())
                .param("searchTerm", TEST_SEARCH_TERM)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试获取搜索词建议API
     */
    @Test
    void testGetSearchTermSuggestions() throws Exception {
        mockMvc.perform(get("/api/v1/ads/search-term-analytics/suggestions")
                .param("accountId", TEST_ACCOUNT_ID)
                .param("profileId", TEST_PROFILE_ID.toString())
                .param("seedKeyword", "wireless")
                .param("limit", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    /**
     * 测试参数验证 - 缺少必需参数
     */
    @Test
    void testGetSearchTermDetails_MissingParameters() throws Exception {
        mockMvc.perform(get("/api/v1/ads/search-term-analytics/details")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    /**
     * 测试不存在的数据
     */
    @Test
    void testGetSearchTermDetails_NotFound() throws Exception {
        mockMvc.perform(get("/api/v1/ads/search-term-analytics/details")
                .param("accountId", "NONEXISTENT")
                .param("profileId", "999")
                .param("searchTerm", "nonexistent term")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 插入测试数据
     */
    private void insertTestData() {
        // 插入搜索词分析数据
        AdsSearchTermAnalyticsModel analytics = new AdsSearchTermAnalyticsModel();
        analytics.setAccountId(TEST_ACCOUNT_ID);
        analytics.setProfileId(TEST_PROFILE_ID);
        analytics.setMarketplaceId(TEST_MARKETPLACE_ID);
        analytics.setSearchTerm(TEST_SEARCH_TERM);
        analytics.setSearchRank(5);
        analytics.setMonthlySearchVolume(50000L);
        analytics.setPreviousMonthSearchVolume(45000L);
        analytics.setClicks(1200);
        analytics.setImpressions(15000);
        analytics.setClickThroughRate(8.0);
        analytics.setConversionRate(12.5);
        analytics.setCompetitionIntensity(2);
        analytics.setDataSource("TEST_DATA");
        analytics.setStatus("ACTIVE");
        analytics.setReportDate(new Date());
        analytics.setCreateTime(new Date());
        analytics.setUpdateTime(new Date());
        searchTermAnalyticsMapper.insert(analytics);

        // 插入类目关联数据
        AdsSearchTermCategoryRelationModel categoryRelation = new AdsSearchTermCategoryRelationModel();
        categoryRelation.setAccountId(TEST_ACCOUNT_ID);
        categoryRelation.setProfileId(TEST_PROFILE_ID);
        categoryRelation.setMarketplaceId(TEST_MARKETPLACE_ID);
        categoryRelation.setSearchTerm(TEST_SEARCH_TERM);
        categoryRelation.setCategoryId(1001L);
        categoryRelation.setCategoryName("Electronics > Audio");
        categoryRelation.setClicksInCategory(800);
        categoryRelation.setImpressionsInCategory(10000);
        categoryRelation.setClickRank(1);
        categoryRelation.setIsPrimaryCategory(true);
        categoryRelation.setDataSource("TEST_DATA");
        categoryRelation.setStatus("ACTIVE");
        categoryRelation.setStatisticsDate(new Date());
        categoryRelation.setCreateTime(new Date());
        categoryRelation.setUpdateTime(new Date());
        categoryRelationMapper.insert(categoryRelation);

        // 插入商品排名数据
        AdsSearchTermProductRankingModel productRanking = new AdsSearchTermProductRankingModel();
        productRanking.setAccountId(TEST_ACCOUNT_ID);
        productRanking.setProfileId(TEST_PROFILE_ID);
        productRanking.setMarketplaceId(TEST_MARKETPLACE_ID);
        productRanking.setSearchTerm(TEST_SEARCH_TERM);
        productRanking.setAsin("B08TEST123");
        productRanking.setProductTitle("Premium Wireless Headphones");
        productRanking.setMainImageUrl("https://example.com/image.jpg");
        productRanking.setBrand("TestBrand");
        productRanking.setPrice(99.99);
        productRanking.setCurrency("USD");
        productRanking.setRating(4.5);
        productRanking.setReviewCount(1500);
        productRanking.setClicks(500);
        productRanking.setImpressions(5000);
        productRanking.setClickRank(1);
        productRanking.setDataSource("TEST_DATA");
        productRanking.setStatus("ACTIVE");
        productRanking.setStatisticsDate(new Date());
        productRanking.setCreateTime(new Date());
        productRanking.setUpdateTime(new Date());
        productRankingMapper.insert(productRanking);
    }

    /**
     * 清理测试数据
     */
    private void cleanupTestData() {
        // 这里可以添加清理逻辑，或者依赖@Transactional的回滚
    }
}
