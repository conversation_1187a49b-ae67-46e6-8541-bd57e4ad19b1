# MySQL索引长度限制修正说明

## 问题描述

在创建搜索词分析相关数据表时，遇到以下错误：

```
1071 - Specified key was too long; max key length is 767 bytes
```

## 问题原因

### 1. MySQL索引长度限制
- **InnoDB引擎默认限制**：索引键最大长度为767字节
- **utf8mb4编码**：每个字符占用4字节
- **问题字段**：`search_term VARCHAR(500)` = 500 × 4 = 2000字节 > 767字节

### 2. 受影响的索引
- `UNIQUE KEY uk_search_term_date (account_id, profile_id, search_term, report_date)`
- `KEY idx_search_term (search_term)`
- 其他包含`search_term`字段的复合索引

## 解决方案

### 1. 使用前缀索引
对于长文本字段，使用前缀索引来限制索引长度：

```sql
-- 修正前（会报错）
KEY `idx_search_term` (`search_term`)

-- 修正后（正常）
KEY `idx_search_term` (`search_term`(100))
```

### 2. 修正的索引列表

#### 搜索词分析数据表 (ads_search_term_analytics)
```sql
-- 修正前
UNIQUE KEY `uk_search_term_date` (`account_id`, `profile_id`, `search_term`, `report_date`),
KEY `idx_search_term` (`search_term`),

-- 修正后
UNIQUE KEY `uk_search_term_date` (`account_id`, `profile_id`, `search_term`(100), `report_date`),
KEY `idx_search_term` (`search_term`(100)),
```

#### 搜索词类目关联表 (ads_search_term_category_relation)
```sql
-- 修正前
UNIQUE KEY `uk_search_term_category` (`account_id`, `profile_id`, `search_term`, `category_id`),
KEY `idx_search_term` (`search_term`),

-- 修正后
UNIQUE KEY `uk_search_term_category` (`account_id`, `profile_id`, `search_term`(100), `category_id`),
KEY `idx_search_term` (`search_term`(100)),
```

#### 搜索词商品排名表 (ads_search_term_product_ranking)
```sql
-- 修正前
UNIQUE KEY `uk_search_term_asin` (`account_id`, `profile_id`, `search_term`, `asin`),
KEY `idx_search_term` (`search_term`),
KEY `idx_brand` (`brand`),

-- 修正后
UNIQUE KEY `uk_search_term_asin` (`account_id`, `profile_id`, `search_term`(100), `asin`),
KEY `idx_search_term` (`search_term`(100)),
KEY `idx_brand` (`brand`(50)),
```

## 前缀索引的影响

### 1. 优点
- ✅ **解决长度限制**：符合MySQL索引长度要求
- ✅ **节省存储空间**：索引占用更少磁盘空间
- ✅ **提高性能**：较短的索引键提高查询性能

### 2. 注意事项
- ⚠️ **前缀长度选择**：100个字符对于大多数搜索词已足够
- ⚠️ **唯一性约束**：前缀索引仍能保证大部分情况下的唯一性
- ⚠️ **查询优化**：前缀索引对前缀匹配查询最有效

### 3. 前缀长度分析
```sql
-- 分析搜索词长度分布（建议在有数据后执行）
SELECT 
    LENGTH(search_term) as term_length,
    COUNT(*) as count,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ads_search_term_analytics) as percentage
FROM ads_search_term_analytics 
GROUP BY LENGTH(search_term) 
ORDER BY term_length;

-- 分析前100个字符的唯一性
SELECT 
    LEFT(search_term, 100) as prefix,
    COUNT(*) as count
FROM ads_search_term_analytics 
GROUP BY LEFT(search_term, 100)
HAVING COUNT(*) > 1;
```

## MySQL配置优化

### 1. 增加索引长度限制（可选）
如果你的MySQL版本支持，可以增加索引长度限制：

```sql
-- 查看当前配置
SHOW VARIABLES LIKE 'innodb_large_prefix';
SHOW VARIABLES LIKE 'innodb_file_format';

-- 启用大前缀支持（MySQL 5.7+默认启用）
SET GLOBAL innodb_large_prefix = ON;
SET GLOBAL innodb_file_format = 'Barracuda';
```

### 2. 表格式设置
```sql
-- 创建表时指定行格式
CREATE TABLE `ads_search_term_analytics` (
    -- 字段定义...
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  ROW_FORMAT=DYNAMIC 
  COMMENT='搜索词分析数据表';
```

## 验证修正结果

### 1. 检查索引创建
```sql
-- 查看表索引
SHOW INDEX FROM ads_search_term_analytics;
SHOW INDEX FROM ads_search_term_category_relation;
SHOW INDEX FROM ads_search_term_product_ranking;
```

### 2. 测试索引效果
```sql
-- 测试前缀索引查询
EXPLAIN SELECT * FROM ads_search_term_analytics 
WHERE search_term LIKE 'wireless headphones%';

-- 测试唯一约束
INSERT INTO ads_search_term_analytics 
(account_id, profile_id, search_term, report_date) 
VALUES ('test', 1, 'test search term', '2025-01-31');
```

## 最佳实践建议

### 1. 字段设计
- 对于可能很长的文本字段，考虑使用前缀索引
- 评估实际数据的长度分布，选择合适的前缀长度
- 对于搜索词，100-200个字符通常足够

### 2. 索引策略
- 优先为常用查询条件创建索引
- 避免创建过多的复合索引
- 定期分析索引使用情况

### 3. 监控和维护
- 定期检查索引效果
- 监控查询性能
- 根据实际使用情况调整索引策略

## 修正后的文件

1. **src/main/resources/sql/ads_search_term_analytics_tables.sql** - 完整版SQL脚本
2. **database_setup_required.sql** - 简化版SQL脚本

这两个文件都已经修正了索引长度问题，可以直接在MySQL中执行。
