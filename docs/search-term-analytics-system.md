# Amazon SP-API 搜索词分析系统

## 系统概述

本系统基于Amazon SP-API实现了完整的搜索词分析功能，能够获取、存储和分析Amazon平台的搜索词数据，包括搜索词与商品类目的关联关系、商品点击排名等核心数据。

## 核心功能

### 1. 搜索词数据获取
- **搜索查询性能报告**: 获取搜索词的点击量、展示量、转化率等性能数据
- **搜索目录性能报告**: 获取搜索词与商品类目的关联性能数据
- **Amazon搜索词报告**: 获取Amazon平台的搜索词趋势和搜索量数据

### 2. 数据关联分析
- **搜索词与类目关联**: 分析搜索词与商品类目的关联关系，按点击量排序取前三
- **搜索词与商品关联**: 获取搜索词关联的商品信息，包括ASIN、图片、标题等
- **竞争分析**: 分析搜索词的竞争强度和市场机会

### 3. 数据存储与分表
- **按月分表策略**: 支持大数据量的分表存储，提高查询性能
- **自动表创建**: 根据日期自动创建分表，无需手动维护
- **数据清理**: 定期清理过期数据，控制存储成本

### 4. 定时数据同步
- **每日自动同步**: 定时从Amazon SP-API获取最新数据
- **增量更新**: 支持增量数据更新，避免重复处理
- **错误重试**: 自动重试失败的同步任务

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Amazon SP-API │    │  数据处理层      │    │   数据存储层     │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │搜索查询性能  │ │───▶│ │数据获取服务  │ │───▶│ │搜索词分析表  │ │
│ │报告API     │ │    │ │             │ │    │ │             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │搜索目录性能  │ │───▶│ │数据处理服务  │ │───▶│ │类目关联表    │ │
│ │报告API     │ │    │ │             │ │    │ │             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │Amazon搜索词 │ │───▶│ │分表管理服务  │ │───▶│ │商品排名表    │ │
│ │报告API     │ │    │ │             │ │    │ │             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 数据模型

### 1. 搜索词分析数据表 (ads_search_term_analytics)
存储搜索词的基本分析数据：
- 搜索词排名和搜索量
- 月搜索量环比增长率
- 点击量、展示量、转化率
- 竞争强度和相关性得分

### 2. 搜索词类目关联表 (ads_search_term_category_relation)
存储搜索词与商品类目的关联关系：
- 搜索词在各类目下的表现数据
- 类目点击量排名（取前三）
- 关联强度得分和市场份额

### 3. 搜索词商品排名表 (ads_search_term_product_ranking)
存储搜索词关联的商品排名数据：
- 商品基本信息（ASIN、标题、图片、价格）
- 商品在搜索词下的点击排名（取前三）
- 商品竞争力得分和性能指标

## API接口

### 查询接口
- `GET /api/v1/ads/search-term-analytics/list` - 获取搜索词分析数据列表
- `GET /api/v1/ads/search-term-analytics/details` - 获取搜索词详细信息
- `GET /api/v1/ads/search-term-analytics/categories` - 获取搜索词关联类目
- `GET /api/v1/ads/search-term-analytics/products` - 获取搜索词关联商品
- `GET /api/v1/ads/search-term-analytics/trends` - 获取搜索词趋势数据
- `GET /api/v1/ads/search-term-analytics/competition-analysis` - 竞争分析

### 同步接口
- `POST /api/v1/ads/search-term-analytics/sync/analytics` - 同步搜索词分析数据
- `POST /api/v1/ads/search-term-analytics/sync/categories` - 同步类目关联数据
- `POST /api/v1/ads/search-term-analytics/sync/products` - 同步商品排名数据
- `POST /api/v1/ads/search-term-analytics/sync/all` - 完整数据同步

## 配置说明

### Amazon SP-API配置
```yaml
amazon:
  spapi:
    analytics:
      report-types:
        search-query-performance: "GET_BRAND_ANALYTICS_SEARCH_QUERY_PERFORMANCE_REPORT"
        search-catalog-performance: "GET_BRAND_ANALYTICS_SEARCH_CATALOG_PERFORMANCE_REPORT"
      processing:
        max-wait-attempts: 30
        wait-interval-seconds: 10
```

### 分表配置
```yaml
database:
  sharding:
    strategy:
      search-term-analytics: "BY_MONTH"
    config:
      auto-create-table: true
      table-retention-months: 12
```

### 定时任务配置
```yaml
scheduler:
  search-term-analytics:
    daily-sync:
      analytics-cron: "0 0 2 * * ?"  # 每天凌晨2点
      category-cron: "0 0 3 * * ?"   # 每天凌晨3点
      product-cron: "0 0 4 * * ?"    # 每天凌晨4点
```

## 部署指南

### 1. 数据库初始化
```sql
-- 执行建表脚本
source src/main/resources/sql/ads_search_term_analytics_tables.sql;

-- 创建当月分表
CALL CreateMonthlyTables('ads_search_term_analytics', CURDATE());
CALL CreateMonthlyTables('ads_search_term_category_relation', CURDATE());
CALL CreateMonthlyTables('ads_search_term_product_ranking', CURDATE());
```

### 2. 配置文件
将 `application-search-term-analytics.yml` 配置合并到主配置文件中。

### 3. 启动应用
```bash
java -jar ads-application.jar --spring.profiles.active=search-term-analytics
```

## 使用示例

### 1. 获取搜索词详细信息
```bash
curl -X GET "http://localhost:8080/api/v1/ads/search-term-analytics/details?accountId=A123&profileId=456&searchTerm=wireless%20headphones"
```

### 2. 手动同步数据
```bash
curl -X POST "http://localhost:8080/api/v1/ads/search-term-analytics/sync/all" \
  -d "accountId=A123&profileId=456&marketplaceId=ATVPDKIKX0DER&startDate=2025-01-30&endDate=2025-01-30"
```

### 3. 查询搜索词趋势
```bash
curl -X GET "http://localhost:8080/api/v1/ads/search-term-analytics/trends?accountId=A123&profileId=456&searchTerm=wireless%20headphones&startDate=2025-01-01&endDate=2025-01-30"
```

## 监控和维护

### 1. 数据质量监控
- 监控同步成功率和数据完整性
- 检查数据异常值和重复数据
- 验证搜索词与类目关联的准确性

### 2. 性能监控
- 监控API响应时间和吞吐量
- 跟踪数据库查询性能
- 监控分表大小和增长趋势

### 3. 定期维护
- 清理过期数据和日志文件
- 优化数据库索引和查询
- 更新Amazon SP-API访问令牌

## 注意事项

1. **API限制**: Amazon SP-API有调用频率限制，需要合理控制请求频率
2. **数据延迟**: Amazon报告数据通常有1-2天的延迟
3. **分表管理**: 需要定期检查和创建新的分表
4. **数据备份**: 建议定期备份重要的分析数据
5. **权限管理**: 确保SP-API访问令牌的安全性

## 故障排除

### 常见问题
1. **报告处理超时**: 检查网络连接和API响应时间
2. **数据同步失败**: 验证访问令牌和账户权限
3. **分表创建失败**: 检查数据库权限和存储空间
4. **查询性能慢**: 优化索引和查询条件

### 日志查看
```bash
# 查看同步任务日志
tail -f logs/ads-search-term-analytics.log

# 查看API调用日志
grep "SpAnalyticsReportsApi" logs/application.log
```
